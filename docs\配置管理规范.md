# AI驱动邮件周报分析系统 - 配置管理规范

## 配置管理原则

### 1. 统一配置管理
- **所有配置必须在 `config.py` 中统一管理**
- **禁止在代码中硬编码配置信息**
- **禁止在多个文件中分散配置**
- **所有模块必须从 `config.py` 导入配置**

### 2. 生产环境配置
- **禁止使用测试数据或虚拟数据进行测试**
- **所有默认配置即为生产环境配置**
- **配置必须是真实可用的生产级配置**
- **禁止使用mock或模拟配置作为默认值**

### 3. 数据库配置规范
- **强制使用PostgreSQL数据库**
- **禁止使用SQLite或其他数据库**
- **禁止数据库自动切换功能**

## 标准配置规范

### 数据库配置 (DB_CONFIG)

```python
# 数据库配置 - 统一PostgreSQL配置，禁止使用SQLite
DB_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "5432")),
    "dbname": os.getenv("DB_NAME", "zkteci"),
    "user": os.getenv("DB_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD", "123456"),
}
```

**配置说明**:
- **数据库名**: `zkteci` (固定，不可更改)
- **用户名**: `postgres` (固定，不可更改)
- **密码**: `123456` (固定，不可更改)
- **端口**: `5432` (PostgreSQL标准端口)
- **主机**: `localhost` (默认本地)

**强制要求**:
- ✅ 必须使用PostgreSQL
- ❌ 禁止使用SQLite
- ❌ 禁止数据库自动切换
- ❌ 禁止备选数据库配置

### 邮箱配置 (IMAP_CONFIG)

```python
# IMAP邮箱配置 - 生产环境真实配置
IMAP_CONFIG = {
    "server": os.getenv("IMAP_SERVER", "imap.exmail.qq.com"),
    "port": int(os.getenv("IMAP_PORT", "993")),
    "email": os.getenv("IMAP_EMAIL", "<EMAIL>"),
    "password": os.getenv("IMAP_PASSWORD", "iBeoUbpKHA96Pbyf"),
    "use_ssl": os.getenv("IMAP_USE_SSL", "true").lower() == "true",
    "days": int(os.getenv("IMAP_DAYS", "30")),
    "max_emails": int(os.getenv("IMAP_MAX_EMAILS", "100")),
    "use_mock": os.getenv("IMAP_USE_MOCK", "false").lower() == "true",
}
```

**配置说明**:
- **邮箱服务器**: `imap.exmail.qq.com` (腾讯企业邮箱)
- **邮箱账号**: `<EMAIL>` (真实生产邮箱)
- **邮箱密码**: `iBeoUbpKHA96Pbyf` (真实授权密码)
- **SSL加密**: `true` (强制启用)
- **默认天数**: `30` (获取最近30天邮件)

**强制要求**:
- ✅ 必须使用真实邮箱配置
- ❌ 禁止使用测试邮箱
- ❌ 禁止使用mock模式作为默认
- ❌ 禁止使用虚拟邮箱数据

## 配置使用规范

### 1. 导入配置的标准方式

```python
# 正确的配置导入方式
from config import DB_CONFIG, IMAP_CONFIG, FILE_PATHS, LOG_CONFIG

# 使用配置
database_url = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}"
```

### 2. 禁止的配置方式

```python
# ❌ 禁止：硬编码配置
database_url = "postgresql://postgres:123456@localhost:5432/zkteci"

# ❌ 禁止：分散配置
DB_HOST = "localhost"
DB_PORT = 5432

# ❌ 禁止：测试配置作为默认
email = "<EMAIL>"
```

### 3. 环境变量覆盖

虽然配置文件提供默认值，但可以通过环境变量覆盖：

```bash
# 生产环境可以通过环境变量覆盖
export DB_HOST="production-db-server"
export DB_PASSWORD="production-password"
export IMAP_EMAIL="<EMAIL>"
```

## 配置验证规范

### 1. 配置完整性检查

每个模块在启动时必须验证配置的完整性：

```python
def validate_config():
    """验证配置完整性"""
    required_db_keys = ['host', 'port', 'dbname', 'user', 'password']
    for key in required_db_keys:
        if key not in DB_CONFIG:
            raise ValueError(f"数据库配置缺少必需键: {key}")
    
    required_imap_keys = ['server', 'port', 'email', 'password', 'use_ssl']
    for key in required_imap_keys:
        if key not in IMAP_CONFIG:
            raise ValueError(f"IMAP配置缺少必需键: {key}")
```

### 2. 配置安全性检查

```python
def validate_security():
    """验证配置安全性"""
    # 检查是否使用默认密码（生产环境应该更改）
    if DB_CONFIG['password'] == '123456':
        logger.warning("使用默认数据库密码，生产环境建议更改")
    
    # 检查邮箱配置
    if '@example.com' in IMAP_CONFIG['email']:
        raise ValueError("禁止使用示例邮箱配置")
```

## 配置更新流程

### 1. 配置变更原则
- **所有配置变更必须在 `config.py` 中进行**
- **配置变更必须更新本规范文档**
- **配置变更必须通过全面验证测试**

### 2. 配置变更步骤
1. 修改 `config.py` 中的配置
2. 更新相关模块的README文档
3. 运行全面验证测试
4. 更新本规范文档
5. 提交变更并记录变更日志

### 3. 配置回滚机制
- 保留配置变更历史
- 提供快速回滚能力
- 验证回滚后的系统稳定性

## 模块配置集成规范

### 1. 数据库模块 (db/orm.py)
```python
from config import DB_CONFIG

def create_database_engine():
    """创建PostgreSQL数据库引擎 - 统一使用PostgreSQL，禁止SQLite"""
    database_url = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}"
    # ... 其他代码
```

### 2. 邮件模块 (email_module/*)
```python
from config import IMAP_CONFIG

class EmailConnection:
    def __init__(self, config=None):
        self.config = config or IMAP_CONFIG
        # ... 其他代码
```

### 3. API模块 (api/*)
```python
from config import DB_CONFIG, LOG_CONFIG

# 使用统一配置
```

## 配置监控和审计

### 1. 配置使用监控
- 记录配置读取日志
- 监控配置变更
- 跟踪配置使用情况

### 2. 配置审计
- 定期审计配置安全性
- 检查配置一致性
- 验证配置有效性

## 违规处理

### 1. 配置违规类型
- **严重违规**: 使用硬编码配置、分散配置管理
- **一般违规**: 使用测试数据、不规范的配置导入
- **轻微违规**: 配置注释不完整、格式不规范

### 2. 违规处理流程
1. 立即停止相关功能
2. 修复配置违规问题
3. 重新运行全面验证
4. 记录违规和修复日志

## 配置规范检查清单

### 开发阶段检查
- [ ] 所有配置在 `config.py` 中统一管理
- [ ] 没有硬编码配置信息
- [ ] 使用真实生产配置作为默认值
- [ ] 数据库配置使用PostgreSQL
- [ ] 邮箱配置使用真实邮箱

### 测试阶段检查
- [ ] 配置完整性验证通过
- [ ] 配置安全性检查通过
- [ ] 所有模块正确导入配置
- [ ] 配置变更测试通过

### 部署阶段检查
- [ ] 生产环境配置验证
- [ ] 配置文件权限设置
- [ ] 敏感配置加密存储
- [ ] 配置备份和恢复测试

## 更新日志

### [2024-05-25] 配置管理规范制定
- 制定统一配置管理规范
- 规范数据库配置为PostgreSQL only
- 规范邮箱配置为生产环境配置
- 禁止使用测试数据和虚拟数据
- 建立配置验证和审计机制

---

**重要提醒**: 本规范为强制执行规范，所有开发人员必须严格遵守。违反本规范的代码将不被接受，必须立即修复。
