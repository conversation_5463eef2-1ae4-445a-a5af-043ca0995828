#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用服务层模块包

模块描述: 应用服务层，协调领域服务和基础设施，实现业务用例
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .analysis, .visualization, .data, .workflow
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 分析服务导入
from .analysis import (
    AnalysisService, MLAnalyzer, ClusteringAnalyzer, 
    TrendAnalyzer, AnomalyAnalyzer
)

# 可视化服务导入
from .visualization import (
    VisualizationService, PlotlyVisualizer, 
    ChartFactory, DashboardBuilder
)

# 数据服务导入
from .data import (
    DataService, ReportRepository, EmployeeRepository,
    CacheService
)

# 工作流服务导入
from .workflow import (
    WorkflowEngine, TaskScheduler, EventHandler
)

__all__ = [
    # 分析服务
    'AnalysisService',
    'MLAnalyzer',
    'ClusteringAnalyzer',
    'TrendAnalyzer',
    'AnomalyAnalyzer',
    # 可视化服务
    'VisualizationService',
    'PlotlyVisualizer',
    'ChartFactory',
    'DashboardBuilder',
    # 数据服务
    'DataService',
    'ReportRepository',
    'EmployeeRepository',
    'CacheService',
    # 工作流服务
    'WorkflowEngine',
    'TaskScheduler',
    'EventHandler'
]
