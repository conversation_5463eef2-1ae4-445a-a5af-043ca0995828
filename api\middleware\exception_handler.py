#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异常处理中间件
实现全局异常处理机制，统一错误响应格式
"""
import time
import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from pydantic import ValidationError
import asyncio

logger = logging.getLogger(__name__)


class ErrorCode:
    """错误码定义"""
    
    # 成功
    SUCCESS = 200
    
    # 客户端错误 4xx
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    VALIDATION_ERROR = 422
    RATE_LIMIT_EXCEEDED = 429
    
    # 服务器错误 5xx
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504
    
    # 业务错误 6xx
    BUSINESS_ERROR = 600
    DATABASE_ERROR = 601
    EXTERNAL_SERVICE_ERROR = 602
    AI_ANALYSIS_ERROR = 603
    EMAIL_FETCH_ERROR = 604


class BusinessException(Exception):
    """业务异常"""
    
    def __init__(self, message: str, code: int = ErrorCode.BUSINESS_ERROR, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class DatabaseException(BusinessException):
    """数据库异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, ErrorCode.DATABASE_ERROR, details)


class AIAnalysisException(BusinessException):
    """AI分析异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, ErrorCode.AI_ANALYSIS_ERROR, details)


class EmailFetchException(BusinessException):
    """邮件获取异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, ErrorCode.EMAIL_FETCH_ERROR, details)


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self):
        self.error_stats = {
            'total_errors': 0,
            'error_by_type': {},
            'error_by_endpoint': {},
            'last_errors': []
        }
    
    def _record_error(self, error_type: str, endpoint: str, error_details: Dict[str, Any]):
        """记录错误统计"""
        self.error_stats['total_errors'] += 1
        
        # 按类型统计
        if error_type not in self.error_stats['error_by_type']:
            self.error_stats['error_by_type'][error_type] = 0
        self.error_stats['error_by_type'][error_type] += 1
        
        # 按端点统计
        if endpoint not in self.error_stats['error_by_endpoint']:
            self.error_stats['error_by_endpoint'][endpoint] = 0
        self.error_stats['error_by_endpoint'][endpoint] += 1
        
        # 记录最近错误（保留最近100个）
        self.error_stats['last_errors'].append({
            'timestamp': time.time(),
            'type': error_type,
            'endpoint': endpoint,
            'details': error_details
        })
        
        if len(self.error_stats['last_errors']) > 100:
            self.error_stats['last_errors'].pop(0)
    
    def _create_error_response(self, 
                             code: int, 
                             message: str, 
                             error_type: str = "UnknownError",
                             details: Optional[Dict[str, Any]] = None,
                             request_id: Optional[str] = None) -> JSONResponse:
        """创建标准错误响应"""
        
        response_data = {
            "success": False,
            "code": code,
            "message": message,
            "error": {
                "type": error_type,
                "details": details or {}
            },
            "timestamp": time.time()
        }
        
        if request_id:
            response_data["request_id"] = request_id
        
        return JSONResponse(
            status_code=code if code < 600 else 500,  # 业务错误码映射到500
            content=response_data
        )
    
    def handle_http_exception(self, request: Request, exc: HTTPException) -> JSONResponse:
        """处理HTTP异常"""
        error_details = {
            "status_code": exc.status_code,
            "detail": exc.detail
        }
        
        self._record_error("HTTPException", request.url.path, error_details)
        
        return self._create_error_response(
            code=exc.status_code,
            message=str(exc.detail),
            error_type="HTTPException",
            details=error_details
        )
    
    def handle_validation_error(self, request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求验证错误"""
        error_details = {
            "validation_errors": exc.errors(),
            "body": str(exc.body) if hasattr(exc, 'body') else None
        }
        
        self._record_error("ValidationError", request.url.path, error_details)
        
        # 提取具体的验证错误信息
        error_messages = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            error_messages.append(f"{field}: {message}")
        
        return self._create_error_response(
            code=ErrorCode.VALIDATION_ERROR,
            message="请求参数验证失败",
            error_type="ValidationError",
            details={
                "validation_errors": error_messages,
                "raw_errors": exc.errors()
            }
        )
    
    def handle_business_exception(self, request: Request, exc: BusinessException) -> JSONResponse:
        """处理业务异常"""
        error_details = {
            "business_code": exc.code,
            "business_message": exc.message,
            "business_details": exc.details
        }
        
        self._record_error(type(exc).__name__, request.url.path, error_details)
        
        return self._create_error_response(
            code=exc.code,
            message=exc.message,
            error_type=type(exc).__name__,
            details=exc.details
        )
    
    def handle_generic_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """处理通用异常"""
        error_details = {
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "traceback": traceback.format_exc()
        }
        
        self._record_error(type(exc).__name__, request.url.path, error_details)
        
        # 记录详细错误日志
        logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)}")
        logger.error(f"请求路径: {request.url.path}")
        logger.error(f"请求方法: {request.method}")
        logger.error(f"异常堆栈:\n{traceback.format_exc()}")
        
        # 生产环境不暴露详细错误信息
        return self._create_error_response(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message="服务器内部错误",
            error_type="InternalServerError",
            details={"error_id": str(time.time())}  # 只返回错误ID，便于日志查找
        )
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def clear_error_stats(self):
        """清空错误统计"""
        self.error_stats = {
            'total_errors': 0,
            'error_by_type': {},
            'error_by_endpoint': {},
            'last_errors': []
        }


# 全局异常处理器实例
exception_handler = ExceptionHandler()


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """异常处理中间件"""
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as exc:
            return exception_handler.handle_http_exception(request, exc)
            
        except RequestValidationError as exc:
            return exception_handler.handle_validation_error(request, exc)
            
        except BusinessException as exc:
            return exception_handler.handle_business_exception(request, exc)
            
        except Exception as exc:
            return exception_handler.handle_generic_exception(request, exc)


# FastAPI异常处理器注册函数
def register_exception_handlers(app):
    """注册FastAPI异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return exception_handler.handle_http_exception(request, exc)
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return exception_handler.handle_validation_error(request, exc)
    
    @app.exception_handler(BusinessException)
    async def business_exception_handler(request: Request, exc: BusinessException):
        return exception_handler.handle_business_exception(request, exc)
    
    @app.exception_handler(Exception)
    async def generic_exception_handler(request: Request, exc: Exception):
        return exception_handler.handle_generic_exception(request, exc)
