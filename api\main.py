#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FastAPI主应用 - 增强版
集成缓存、限流、异常处理、性能监控等中间件
"""
from fastapi import FastAPI, Response, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from api.endpoints import report, staff, template, tag, anomaly
from api.endpoints import email
import logging
import time
from typing import Dict, Any

# 导入中间件
from api.middleware.cache import CacheMiddleware, cache_manager
from api.middleware.rate_limit import RateLimitMiddleware, rate_limiter
from api.middleware.exception_handler import ExceptionHandlerMiddleware, register_exception_handlers
from api.middleware.performance import PerformanceMiddleware, performance_metrics

# 导入配置
from config import DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(name)s %(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI驱动邮件周报分析系统API",
    description="智能邮件采集与周报分析API - 增强版",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 注册异常处理器
register_exception_handlers(app)

# 添加中间件（注意顺序：从外到内）
# 1. 性能监控中间件（最外层）
app.add_middleware(PerformanceMiddleware, enabled=True)

# 2. 限流中间件
app.add_middleware(RateLimitMiddleware, enabled=True)

# 3. 缓存中间件
app.add_middleware(CacheMiddleware, cache_enabled=True)

# 4. 异常处理中间件
app.add_middleware(ExceptionHandlerMiddleware)

# 5. GZIP压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 6. CORS中间件（最内层）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(report.router, prefix="/api/report", tags=["report"])
app.include_router(staff.router, prefix="/api/staff", tags=["staff"])
app.include_router(template.router, prefix="/api/template", tags=["template"])
app.include_router(tag.router, prefix="/api/tags", tags=["tags"])
app.include_router(anomaly.router, prefix="/api/anomaly", tags=["anomaly"])
app.include_router(email.router, tags=["email"])

# 系统监控端点
@app.get("/api/system/health", tags=["system"])
async def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        from db.orm import SessionLocal
        from sqlalchemy import text

        with SessionLocal() as session:
            session.execute(text("SELECT 1"))

        db_status = "healthy"
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        db_status = "unhealthy"

    return {
        "status": "ok",
        "timestamp": time.time(),
        "database": db_status,
        "version": "2.0.0"
    }

@app.get("/api/system/metrics", tags=["system"])
async def get_system_metrics():
    """获取系统性能指标"""
    return {
        "performance": performance_metrics.get_overall_stats(),
        "endpoints": performance_metrics.get_endpoint_stats(),
        "system": performance_metrics.get_system_stats(),
        "cache": cache_manager.get_stats(),
        "rate_limit": rate_limiter.get_stats()
    }

@app.get("/api/system/cache/stats", tags=["system"])
async def get_cache_stats():
    """获取缓存统计"""
    return cache_manager.get_stats()

@app.post("/api/system/cache/clear", tags=["system"])
async def clear_cache(cache_type: str = "all"):
    """清空缓存"""
    if cache_type == "all":
        cache_manager.memory_cache.clear()
        return {"message": "所有缓存已清空"}
    else:
        cache_manager.clear_by_type(cache_type)
        return {"message": f"{cache_type} 类型缓存已清空"}

@app.get("/api/system/performance/slow", tags=["system"])
async def get_slow_requests(threshold: float = 1.0, limit: int = 10):
    """获取慢请求列表"""
    return performance_metrics.get_slow_requests(threshold, limit)

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("AI驱动邮件周报分析系统API启动")
    logger.info("中间件已加载：缓存、限流、异常处理、性能监控")

    # 清理过期的限流器
    rate_limiter.cleanup_expired_limiters()

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("AI驱动邮件周报分析系统API关闭")

    # 清理资源
    performance_metrics.clear_metrics()
    cache_manager.memory_cache.clear()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )