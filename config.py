#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置文件
包含数据库和IMAP邮箱的配置信息
注意：实际生产环境中应使用环境变量或安全的配置管理系统
"""

import os

# 数据库配置 - 统一PostgreSQL配置，禁止使用SQLite
DB_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "5432")),
    "dbname": os.getenv("DB_NAME", "zkteci"),
    "user": os.getenv("DB_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD", "123456"),
}

# IMAP邮箱配置
IMAP_CONFIG = {
    "server": os.getenv("IMAP_SERVER", "imap.exmail.qq.com"),
    "port": int(os.getenv("IMAP_PORT", "993")),
    "email": os.getenv("IMAP_EMAIL", "<EMAIL>"),
    "password": os.getenv("IMAP_PASSWORD", "iBeoUbpKHA96Pbyf"),
    "use_ssl": os.getenv("IMAP_USE_SSL", "true").lower() == "true",
    "days": int(os.getenv("IMAP_DAYS", "30")),
    "max_emails": int(os.getenv("IMAP_MAX_EMAILS", "100")),
    "use_mock": os.getenv("IMAP_USE_MOCK", "false").lower()
    == "true",  # 是否使用模拟邮件服务器
}

# 文件路径配置
FILE_PATHS = {
    "staff_info": os.getenv(
        "STAFF_INFO_PATH",
        "F:\\jj\\ali\\dataann_email\\zkteco_js\\人员信息-中国区技术支持.xlsx",
    )
}

# 日志配置
LOG_CONFIG = {
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": os.getenv("LOG_FILE", "app.log"),
}

# 应用配置
APP_CONFIG = {"debug": os.getenv("DEBUG", "false").lower() == "true"}

# 安全提示
if __name__ == "__main__":
    print("警告：此文件包含敏感信息，不应直接执行或提交到版本控制系统")
    print("建议：在生产环境中使用环境变量或安全的配置管理系统")
