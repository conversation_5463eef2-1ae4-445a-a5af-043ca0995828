#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI分析器测试脚本
"""

import sys
import os

print("🚀 AI分析器测试开始")
print("=" * 50)

print("Python版本:", sys.version)
print("当前目录:", os.getcwd())

# 测试基础导入
print("\n📦 测试基础模块导入...")

try:
    import json
    print("✅ json模块导入成功")
except Exception as e:
    print(f"❌ json模块导入失败: {e}")

try:
    import uuid
    print("✅ uuid模块导入成功")
except Exception as e:
    print(f"❌ uuid模块导入失败: {e}")

try:
    from datetime import datetime
    print("✅ datetime模块导入成功")
    print(f"   当前时间: {datetime.now()}")
except Exception as e:
    print(f"❌ datetime模块导入失败: {e}")

# 测试项目模块
print("\n🔧 测试项目模块导入...")

try:
    sys.path.append('.')
    from ai.analysis.utils import generate_report_id, create_default_analysis
    print("✅ ai.analysis.utils导入成功")
    
    report_id = generate_report_id()
    print(f"   生成报告ID: {report_id}")
    
    default_analysis = create_default_analysis()
    print(f"   默认分析ID: {default_analysis.get('report_id')}")
    
except Exception as e:
    print(f"❌ ai.analysis.utils导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🤖 测试AI分析器...")

try:
    from ai.simple_analyzer import SimpleAIAnalyzer
    print("✅ SimpleAIAnalyzer导入成功")
    
    analyzer = SimpleAIAnalyzer()
    print("✅ SimpleAIAnalyzer初始化成功")
    
    # 测试分析功能
    test_content = """
    本周主要工作：
    1. 处理客户技术问题，耗时4小时
    2. 参与产品培训，耗时2小时
    3. 编写技术文档，耗时3小时
    总计工时：9小时
    """
    
    print("📝 开始分析测试内容...")
    result = analyzer.analyze(
        test_content,
        department="技术支持部",
        role="技术支持工程师",
        employee_email="<EMAIL>",
        employee_name="测试用户"
    )
    
    print("✅ AI分析完成")
    print(f"   报告ID: {result.get('report_id')}")
    print(f"   员工姓名: {result.get('employee', {}).get('name')}")
    print(f"   员工邮箱: {result.get('employee', {}).get('email')}")
    print(f"   部门: {result.get('employee', {}).get('department')}")
    print(f"   岗位: {result.get('employee', {}).get('role')}")
    print(f"   工作项数量: {len(result.get('work_items', []))}")
    print(f"   总工时: {result.get('metrics', {}).get('total_hours')}小时")
    print(f"   饱和度: {result.get('metrics', {}).get('saturation_tag')}")
    print(f"   标签: {', '.join(result.get('tags', []))}")
    print(f"   异常标记: {', '.join(result.get('anomaly_flags', []))}")
    
    # 显示工作项详情
    work_items = result.get('work_items', [])
    if work_items:
        print("\n📋 工作项详情:")
        for i, item in enumerate(work_items, 1):
            print(f"   {i}. {item.get('title', 'N/A')}")
            print(f"      工时: {item.get('duration_hours', 0)}小时")
            print(f"      复杂度: {item.get('complexity', 'N/A')}")
            print(f"      类别: {item.get('category', 'N/A')}")
    
    # 显示专项分析
    print("\n🔍 专项分析:")
    print(f"   创新分析: {result.get('innovation_analysis', 'N/A')}")
    print(f"   品质分析: {result.get('quality_analysis', 'N/A')}")
    print(f"   趋势分析: {result.get('trend_analysis', 'N/A')}")
    
    print("\n✅ AI分析器测试全部通过！")
    
except Exception as e:
    print(f"❌ AI分析器测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("🎉 测试完成！")

# 保存测试结果
try:
    test_result = {
        "test_time": datetime.now().isoformat(),
        "test_status": "success",
        "ai_analyzer_available": True,
        "sample_analysis": result if 'result' in locals() else None
    }
    
    with open("ai_test_result.json", "w", encoding="utf-8") as f:
        json.dump(test_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"📄 测试结果已保存到: ai_test_result.json")
    
except Exception as e:
    print(f"⚠️ 保存测试结果失败: {e}")

print("🏁 AI分析器测试结束")
