@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   AI邮件分析系统 - 开发测试启动
echo ========================================
echo.

:: 设置项目根目录
cd /d "%~dp0"
echo 📍 当前目录: %CD%

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 激活虚拟环境（如果存在）
if exist "venv\Scripts\activate.bat" (
    echo 🔧 激活虚拟环境...
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️ 虚拟环境不存在，使用系统Python
)

:: 快速测试AI分析器
echo.
echo 🤖 测试AI分析器...
python -c "
import sys
sys.path.append('.')
try:
    from ai.simple_analyzer import SimpleAIAnalyzer
    print('✅ AI分析器导入成功')
    analyzer = SimpleAIAnalyzer()
    print('✅ AI分析器初始化成功')
    result = analyzer.analyze('本周完成开发任务，耗时10小时', department='技术部', role='工程师')
    print(f'✅ 分析完成，报告ID: {result.get(\"report_id\", \"N/A\")}')
    print(f'   工时: {result.get(\"metrics\", {}).get(\"total_hours\", 0)}小时')
    print(f'   标签: {len(result.get(\"tags\", []))}个')
except Exception as e:
    print(f'❌ AI分析器测试失败: {e}')
"

:: 检查系统状态
echo.
echo 🔍 检查系统状态...
python system_check.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 系统检查有问题
) else (
    echo ✅ 系统检查通过
)

:: 启动选项
echo.
echo 🚀 选择操作:
echo 1. 启动前端UI
echo 2. 运行AI测试
echo 3. 查看系统状态
echo 4. 退出
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🌐 启动前端UI...
    echo 💡 如果缺少依赖，请先运行: pip install streamlit plotly pandas
    echo.
    cd ui
    python -m streamlit run enhanced_app.py --server.port 8501
) else if "%choice%"=="2" (
    echo.
    echo 🤖 运行详细AI测试...
    python ai_test.py
    pause
) else if "%choice%"=="3" (
    echo.
    echo 📊 系统状态详情:
    python -c "
import sys
sys.path.append('.')
print('Python版本:', sys.version.split()[0])
try:
    from ai.simple_analyzer import SimpleAIAnalyzer
    print('✅ AI分析器: 可用')
except:
    print('❌ AI分析器: 不可用')
try:
    from db.orm import engine
    print('✅ 数据库ORM: 可用')
except:
    print('❌ 数据库ORM: 不可用')
try:
    import streamlit
    print('✅ Streamlit: 已安装')
except:
    print('❌ Streamlit: 未安装')
"
    pause
) else (
    echo 👋 退出
)

echo.
echo 开发测试完成
pause
