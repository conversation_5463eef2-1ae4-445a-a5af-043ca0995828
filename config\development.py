#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
开发环境配置模块

模块描述: 开发环境的特定配置，包括调试设置、本地服务配置等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .base
"""

from .base import BaseConfig

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    
    def __init__(self):
        super().__init__()
        self.debug = True
        self.testing = False
        
    def load_environment_specific(self) -> None:
        """加载开发环境特定配置"""
        # 数据库配置 - 使用本地数据库
        self.database.host = "localhost"
        self.database.port = 5432
        self.database.database = "zkteci"
        self.database.username = "postgres"
        self.database.password = "123456"
        self.database.echo = True  # 开发环境显示SQL
        
        # AI服务配置 - 使用本地Ollama
        self.ai.base_url = "http://localhost:11434"
        self.ai.model_name = "qwen2.5:7b"
        self.ai.embedding_model = "nomic-embed-text"
        self.ai.timeout = 300
        self.ai.temperature = 0.7
        
        # UI配置 - 开发主题
        self.ui.theme = "light"
        self.ui.page_title = "AI周报分析系统 - 开发环境"
        self.ui.layout = "wide"
        self.ui.sidebar_state = "expanded"
        
        # 缓存配置 - 使用本地Redis
        self.cache.redis_url = "redis://localhost:6379/0"
        self.cache.default_ttl = 1800  # 开发环境缓存时间短一些
        
        # 邮件配置 - 开发环境使用测试邮箱
        self.email.host = "smtp.gmail.com"
        self.email.port = 587
        self.email.username = "<EMAIL>"
        self.email.password = "test_password"
        self.email.use_tls = True
        
        # 日志配置 - 开发环境详细日志
        self.logging.level = "DEBUG"
        self.logging.console_output = True
        self.logging.file_output = True
        self.logging.log_dir = "logs"
        
        # 安全配置 - 开发环境宽松设置
        self.security.secret_key = "dev-secret-key-not-for-production"
        self.security.session_timeout = 7200  # 2小时
        self.security.secure_cookies = False  # HTTP环境
        self.security.csrf_protection = False  # 开发时关闭CSRF
        
        # 开发环境特有配置
        self.auto_reload = True
        self.hot_reload = True
        self.show_error_details = True
        self.enable_profiling = True
        self.mock_external_services = False
        
        # 性能配置
        self.database.pool_size = 5  # 开发环境连接池小一些
        self.database.max_overflow = 10
        self.cache.max_connections = 5
        
        # 开发工具配置
        self.enable_debug_toolbar = True
        self.enable_sql_logging = True
        self.enable_request_logging = True
        
    def get_development_features(self) -> dict:
        """获取开发环境特有功能配置"""
        return {
            'auto_reload': self.auto_reload,
            'hot_reload': self.hot_reload,
            'show_error_details': self.show_error_details,
            'enable_profiling': self.enable_profiling,
            'mock_external_services': self.mock_external_services,
            'enable_debug_toolbar': self.enable_debug_toolbar,
            'enable_sql_logging': self.enable_sql_logging,
            'enable_request_logging': self.enable_request_logging
        }
    
    def get_streamlit_config(self) -> dict:
        """获取Streamlit开发配置"""
        config = self.ui.get_streamlit_config()
        config.update({
            'server.runOnSave': self.auto_reload,
            'server.allowRunOnSave': True,
            'browser.gatherUsageStats': False,
            'global.developmentMode': True,
            'global.showWarningOnDirectExecution': False,
            'logger.level': 'debug'
        })
        return config
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"DevelopmentConfig(debug={self.debug}, auto_reload={self.auto_reload})"
