#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版AI分析器
用于系统测试和开发，不依赖外部AI服务
"""

import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from .analysis.utils import generate_report_id, create_default_analysis, normalize_email

logger = logging.getLogger(__name__)


class SimpleAIAnalyzer:
    """简化版AI分析器，用于系统测试"""
    
    def __init__(self):
        self.logger = logger
        
    def analyze(self, report_text: str, department: str = None, role: str = None, 
                employee_email: str = None, employee_name: str = None) -> Dict[str, Any]:
        """
        分析周报内容，返回结构化结果
        """
        try:
            self.logger.info(f"开始分析周报，部门: {department}, 岗位: {role}")
            
            # 基础信息提取
            result = {
                "report_id": generate_report_id(),
                "employee": {
                    "name": employee_name or "测试用户",
                    "email": normalize_email(employee_email or "<EMAIL>"),
                    "department": department or "技术部",
                    "role": role or "工程师"
                },
                "week": datetime.now().strftime("%Y-W%U"),
                "work_items": self._extract_work_items(report_text),
                "summary": self._generate_summary(report_text),
                "metrics": {},
                "tags": self._generate_tags(report_text, department, role),
                "anomaly_flags": self._detect_anomalies(report_text),
                "innovation_analysis": self._analyze_innovation(report_text),
                "quality_analysis": self._analyze_quality(report_text),
                "trend_analysis": self._analyze_trend(report_text),
                "ai_version": "simple_v1.0",
                "raw_text": report_text
            }
            
            # 计算metrics
            result["metrics"] = self._calculate_metrics(result["work_items"])
            
            self.logger.info(f"分析完成，报告ID: {result['report_id']}")
            return result
            
        except Exception as e:
            self.logger.error(f"分析失败: {e}")
            return create_default_analysis({
                "name": employee_name,
                "email": employee_email,
                "department": department,
                "role": role
            })
    
    def _extract_work_items(self, text: str) -> list:
        """提取工作项"""
        work_items = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 简单的工作项识别
            if any(keyword in line for keyword in ['工作', '任务', '项目', '开发', '测试', '会议', '培训']):
                # 尝试提取工时
                hours = self._extract_hours(line)
                
                work_item = {
                    "title": line[:50] + "..." if len(line) > 50 else line,
                    "description": line,
                    "duration_hours": hours,
                    "complexity": self._assess_complexity(line),
                    "category": self._categorize_work(line),
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "extra": {},
                    "tags": [],
                    "anomaly_flags": []
                }
                work_items.append(work_item)
        
        # 如果没有识别到工作项，创建一个默认的
        if not work_items:
            work_items.append({
                "title": "日常工作",
                "description": text[:100] + "..." if len(text) > 100 else text,
                "duration_hours": 8.0,
                "complexity": "中",
                "category": "日常工作",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "extra": {},
                "tags": [],
                "anomaly_flags": []
            })
        
        return work_items
    
    def _extract_hours(self, text: str) -> float:
        """从文本中提取工时"""
        import re
        
        # 查找数字+小时的模式
        hour_patterns = [
            r'(\d+(?:\.\d+)?)\s*小时',
            r'(\d+(?:\.\d+)?)\s*h',
            r'(\d+(?:\.\d+)?)\s*H',
            r'耗时\s*(\d+(?:\.\d+)?)',
            r'用时\s*(\d+(?:\.\d+)?)'
        ]
        
        for pattern in hour_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue
        
        # 默认工时
        return 1.0
    
    def _assess_complexity(self, text: str) -> str:
        """评估复杂度"""
        high_keywords = ['复杂', '困难', '挑战', '难点', '问题', '调试', '优化']
        low_keywords = ['简单', '容易', '常规', '日常', '例行']
        
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in high_keywords):
            return "高"
        elif any(keyword in text_lower for keyword in low_keywords):
            return "低"
        else:
            return "中"
    
    def _categorize_work(self, text: str) -> str:
        """工作分类"""
        categories = {
            "开发": ["开发", "编程", "代码", "功能", "模块"],
            "测试": ["测试", "验证", "检查", "调试"],
            "会议": ["会议", "讨论", "沟通", "汇报"],
            "培训": ["培训", "学习", "研究", "文档"],
            "支持": ["支持", "客户", "问题", "解决"],
            "管理": ["管理", "计划", "安排", "协调"]
        }
        
        text_lower = text.lower()
        for category, keywords in categories.items():
            if any(keyword in text_lower for keyword in keywords):
                return category
        
        return "其他"
    
    def _generate_summary(self, text: str) -> Dict[str, Any]:
        """生成摘要"""
        work_items_count = len(text.split('\n'))
        estimated_hours = work_items_count * 1.5  # 简单估算
        
        return {
            "total_hours": estimated_hours,
            "main_achievements": "完成了多项工作任务",
            "risks": "无明显风险",
            "suggestions": "继续保持良好的工作状态"
        }
    
    def _calculate_metrics(self, work_items: list) -> Dict[str, Any]:
        """计算指标"""
        total_hours = sum(item.get("duration_hours", 0) for item in work_items)
        task_count = len(work_items)
        
        # 简单的饱和度计算
        saturation = min(total_hours / 40.0, 1.5)  # 假设40小时为标准工作量
        
        if saturation >= 1.2:
            saturation_tag = "过载"
        elif saturation >= 0.8:
            saturation_tag = "饱和"
        elif saturation >= 0.5:
            saturation_tag = "适中"
        else:
            saturation_tag = "不足"
        
        return {
            "total_hours": total_hours,
            "task_count": task_count,
            "saturation": saturation,
            "saturation_tag": saturation_tag
        }
    
    def _generate_tags(self, text: str, department: str, role: str) -> list:
        """生成标签"""
        tags = []
        
        # 基于部门和岗位的标签
        if department:
            tags.append(f"部门-{department}")
        if role:
            tags.append(f"岗位-{role}")
        
        # 基于内容的标签
        content_tags = {
            "技术": ["技术", "开发", "代码", "系统"],
            "客户": ["客户", "用户", "支持", "服务"],
            "创新": ["创新", "优化", "改进", "新"],
            "质量": ["质量", "测试", "验证", "检查"],
            "学习": ["学习", "培训", "研究", "文档"]
        }
        
        text_lower = text.lower()
        for tag, keywords in content_tags.items():
            if any(keyword in text_lower for keyword in keywords):
                tags.append(tag)
        
        return list(set(tags))  # 去重
    
    def _detect_anomalies(self, text: str) -> list:
        """检测异常"""
        anomalies = []
        
        # 简单的异常检测
        if len(text) < 50:
            anomalies.append("内容过短")
        
        if "问题" in text or "错误" in text:
            anomalies.append("包含问题描述")
        
        return anomalies
    
    def _analyze_innovation(self, text: str) -> str:
        """创新能力分析"""
        innovation_keywords = ["创新", "优化", "改进", "新方法", "效率", "自动化"]
        
        text_lower = text.lower()
        found_keywords = [kw for kw in innovation_keywords if kw in text_lower]
        
        if found_keywords:
            return f"发现创新点：{', '.join(found_keywords)}"
        else:
            return "暂未发现明显创新点"
    
    def _analyze_quality(self, text: str) -> str:
        """品质分析"""
        quality_keywords = ["质量", "测试", "验证", "标准", "规范", "检查"]
        
        text_lower = text.lower()
        found_keywords = [kw for kw in quality_keywords if kw in text_lower]
        
        if found_keywords:
            return f"关注品质方面：{', '.join(found_keywords)}"
        else:
            return "品质相关信息较少"
    
    def _analyze_trend(self, text: str) -> str:
        """趋势分析"""
        return "基于单次分析，暂无趋势数据"
