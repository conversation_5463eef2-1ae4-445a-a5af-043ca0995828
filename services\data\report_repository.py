#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周报数据仓储

模块描述: 周报数据的持久化操作，包括CRUD和查询功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.base, domain.entities, sqlalchemy
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from core.base import BaseRepository
from core.decorators import performance_monitor, error_handler
from core.exceptions import DataAccessError
from domain.entities import WeeklyReport, Employee, WorkItem, TaskComplexity, TaskCategory


class ReportRepository(BaseRepository):
    """
    周报数据仓储
    
    功能：
    - 周报的CRUD操作
    - 复杂查询和筛选
    - 数据聚合和统计
    - 批量操作
    """
    
    def __init__(self, config, database_session=None):
        """
        初始化周报仓储
        
        Args:
            config: 仓储配置
            database_session: 数据库会话
        """
        super().__init__(config)
        self.db_session = database_session
        self._reports_cache: Dict[str, WeeklyReport] = {}
        
        # 模拟数据存储（实际应该连接数据库）
        self._reports_storage: Dict[str, Dict[str, Any]] = {}
        self._next_id = 1
    
    def initialize(self) -> bool:
        """初始化仓储"""
        try:
            # 这里应该初始化数据库连接、创建表等
            self.logger.info("周报仓储初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"周报仓储初始化失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True
    
    @performance_monitor
    @error_handler(reraise=True)
    def create(self, report: WeeklyReport) -> WeeklyReport:
        """
        创建周报
        
        Args:
            report: 周报对象
            
        Returns:
            WeeklyReport: 创建后的周报对象
        """
        try:
            # 序列化周报数据
            report_data = self._serialize_report(report)
            
            # 存储到模拟存储（实际应该存储到数据库）
            self._reports_storage[report.report_id] = report_data
            
            # 更新缓存
            self._reports_cache[report.report_id] = report
            
            self.logger.info(f"创建周报成功: {report.report_id}")
            return report
            
        except Exception as e:
            raise DataAccessError(f"创建周报失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def get_by_id(self, report_id: str) -> Optional[WeeklyReport]:
        """
        根据ID获取周报
        
        Args:
            report_id: 周报ID
            
        Returns:
            Optional[WeeklyReport]: 周报对象，如果不存在则返回None
        """
        try:
            # 先检查缓存
            if report_id in self._reports_cache:
                return self._reports_cache[report_id]
            
            # 从存储获取
            if report_id in self._reports_storage:
                report_data = self._reports_storage[report_id]
                report = self._deserialize_report(report_data)
                
                # 更新缓存
                self._reports_cache[report_id] = report
                return report
            
            return None
            
        except Exception as e:
            raise DataAccessError(f"获取周报失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def update(self, report: WeeklyReport) -> WeeklyReport:
        """
        更新周报
        
        Args:
            report: 周报对象
            
        Returns:
            WeeklyReport: 更新后的周报对象
        """
        try:
            if report.report_id not in self._reports_storage:
                raise DataAccessError(f"周报不存在: {report.report_id}")
            
            # 更新时间戳
            report.updated_at = datetime.now()
            
            # 序列化并存储
            report_data = self._serialize_report(report)
            self._reports_storage[report.report_id] = report_data
            
            # 更新缓存
            self._reports_cache[report.report_id] = report
            
            self.logger.info(f"更新周报成功: {report.report_id}")
            return report
            
        except Exception as e:
            raise DataAccessError(f"更新周报失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def delete(self, report_id: str) -> bool:
        """
        删除周报
        
        Args:
            report_id: 周报ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if report_id not in self._reports_storage:
                return False
            
            # 从存储删除
            del self._reports_storage[report_id]
            
            # 从缓存删除
            if report_id in self._reports_cache:
                del self._reports_cache[report_id]
            
            self.logger.info(f"删除周报成功: {report_id}")
            return True
            
        except Exception as e:
            raise DataAccessError(f"删除周报失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def query(self, criteria: Dict[str, Any]) -> List[WeeklyReport]:
        """
        查询周报
        
        Args:
            criteria: 查询条件
            
        Returns:
            List[WeeklyReport]: 查询结果列表
        """
        try:
            results = []
            
            for report_id, report_data in self._reports_storage.items():
                if self._matches_criteria(report_data, criteria):
                    # 检查缓存
                    if report_id in self._reports_cache:
                        report = self._reports_cache[report_id]
                    else:
                        report = self._deserialize_report(report_data)
                        self._reports_cache[report_id] = report
                    
                    results.append(report)
            
            return results
            
        except Exception as e:
            raise DataAccessError(f"查询周报失败: {e}")
    
    @performance_monitor
    def count(self, criteria: Dict[str, Any] = None) -> int:
        """
        统计周报数量
        
        Args:
            criteria: 查询条件
            
        Returns:
            int: 周报数量
        """
        if criteria is None:
            return len(self._reports_storage)
        
        count = 0
        for report_data in self._reports_storage.values():
            if self._matches_criteria(report_data, criteria):
                count += 1
        
        return count
    
    def get_reports_by_employee(self, employee_email: str, 
                              start_week: str = None, end_week: str = None) -> List[WeeklyReport]:
        """
        获取员工的周报列表
        
        Args:
            employee_email: 员工邮箱
            start_week: 开始周次
            end_week: 结束周次
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        criteria = {'employee.email': employee_email}
        
        if start_week and end_week:
            criteria['week_range'] = (start_week, end_week)
        
        return self.query(criteria)
    
    def get_reports_by_department(self, department: str, 
                                week: str = None) -> List[WeeklyReport]:
        """
        获取部门的周报列表
        
        Args:
            department: 部门名称
            week: 周次（可选）
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        criteria = {'employee.department': department}
        
        if week:
            criteria['week'] = week
        
        return self.query(criteria)
    
    def get_reports_by_week_range(self, start_week: str, end_week: str) -> List[WeeklyReport]:
        """
        获取周次范围内的周报
        
        Args:
            start_week: 开始周次
            end_week: 结束周次
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        criteria = {'week_range': (start_week, end_week)}
        return self.query(criteria)
    
    def get_latest_reports(self, limit: int = 10) -> List[WeeklyReport]:
        """
        获取最新的周报
        
        Args:
            limit: 限制数量
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        all_reports = []
        for report_id, report_data in self._reports_storage.items():
            if report_id in self._reports_cache:
                report = self._reports_cache[report_id]
            else:
                report = self._deserialize_report(report_data)
                self._reports_cache[report_id] = report
            
            all_reports.append(report)
        
        # 按创建时间排序
        all_reports.sort(key=lambda r: r.created_at, reverse=True)
        
        return all_reports[:limit]
    
    def batch_create(self, reports: List[WeeklyReport]) -> List[WeeklyReport]:
        """
        批量创建周报
        
        Args:
            reports: 周报列表
            
        Returns:
            List[WeeklyReport]: 创建后的周报列表
        """
        created_reports = []
        
        for report in reports:
            try:
                created_report = self.create(report)
                created_reports.append(created_report)
            except Exception as e:
                self.logger.error(f"批量创建周报失败: {report.report_id}, 错误: {e}")
        
        return created_reports
    
    def _serialize_report(self, report: WeeklyReport) -> Dict[str, Any]:
        """序列化周报对象"""
        return {
            'report_id': report.report_id,
            'employee': {
                'email': report.employee.email,
                'name': report.employee.name,
                'department': report.employee.department,
                'role': report.employee.role,
                'level': report.employee.level.value if report.employee.level else None,
                'skills': report.employee.skills,
                'manager_email': report.employee.manager_email,
                'hire_date': report.employee.hire_date.isoformat() if report.employee.hire_date else None,
                'is_active': report.employee.is_active,
                'metadata': report.employee.metadata,
                'created_at': report.employee.created_at.isoformat(),
                'updated_at': report.employee.updated_at.isoformat()
            },
            'week': report.week,
            'work_items': [
                {
                    'title': item.title,
                    'description': item.description,
                    'duration_hours': item.duration_hours,
                    'complexity': item.complexity.value,
                    'category': item.category.value,
                    'date': item.date.isoformat(),
                    'employee_email': item.employee_email,
                    'status': item.status.value,
                    'priority': item.priority,
                    'tags': item.tags,
                    'dependencies': item.dependencies,
                    'deliverables': item.deliverables,
                    'extra_data': item.extra_data,
                    'created_at': item.created_at.isoformat(),
                    'updated_at': item.updated_at.isoformat()
                }
                for item in report.work_items
            ],
            'summary': report.summary,
            'metrics': report.metrics,
            'ai_version': report.ai_version,
            'raw_text': report.raw_text,
            'tags': report.tags,
            'anomaly_flags': report.anomaly_flags,
            'quality_score': report.quality_score,
            'completeness_score': report.completeness_score,
            'reviewed': report.reviewed,
            'reviewer_email': report.reviewer_email,
            'review_comments': report.review_comments,
            'created_at': report.created_at.isoformat(),
            'updated_at': report.updated_at.isoformat()
        }
    
    def _deserialize_report(self, data: Dict[str, Any]) -> WeeklyReport:
        """反序列化周报对象"""
        from domain.entities import EmployeeLevel, TaskStatus
        
        # 反序列化员工
        employee_data = data['employee']
        employee = Employee(
            email=employee_data['email'],
            name=employee_data['name'],
            department=employee_data['department'],
            role=employee_data['role'],
            level=EmployeeLevel(employee_data['level']) if employee_data['level'] else None,
            skills=employee_data['skills'],
            manager_email=employee_data['manager_email'],
            hire_date=datetime.fromisoformat(employee_data['hire_date']) if employee_data['hire_date'] else None,
            is_active=employee_data['is_active'],
            metadata=employee_data['metadata']
        )
        employee.created_at = datetime.fromisoformat(employee_data['created_at'])
        employee.updated_at = datetime.fromisoformat(employee_data['updated_at'])
        
        # 反序列化工作项
        work_items = []
        for item_data in data['work_items']:
            work_item = WorkItem(
                title=item_data['title'],
                description=item_data['description'],
                duration_hours=item_data['duration_hours'],
                complexity=TaskComplexity(item_data['complexity']),
                category=TaskCategory(item_data['category']),
                date=datetime.fromisoformat(item_data['date']),
                employee_email=item_data['employee_email'],
                status=TaskStatus(item_data['status']),
                priority=item_data['priority'],
                tags=item_data['tags'],
                dependencies=item_data['dependencies'],
                deliverables=item_data['deliverables'],
                extra_data=item_data['extra_data']
            )
            work_item.created_at = datetime.fromisoformat(item_data['created_at'])
            work_item.updated_at = datetime.fromisoformat(item_data['updated_at'])
            work_items.append(work_item)
        
        # 创建周报
        report = WeeklyReport(
            report_id=data['report_id'],
            employee=employee,
            week=data['week'],
            work_items=work_items,
            summary=data['summary'],
            metrics=data['metrics'],
            ai_version=data['ai_version'],
            raw_text=data['raw_text'],
            tags=data['tags'],
            anomaly_flags=data['anomaly_flags'],
            quality_score=data['quality_score'],
            completeness_score=data['completeness_score'],
            reviewed=data['reviewed'],
            reviewer_email=data['reviewer_email'],
            review_comments=data['review_comments']
        )
        report.created_at = datetime.fromisoformat(data['created_at'])
        report.updated_at = datetime.fromisoformat(data['updated_at'])
        
        return report
    
    def _matches_criteria(self, report_data: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查周报数据是否匹配查询条件"""
        for key, value in criteria.items():
            if key == 'employee.email':
                if report_data['employee']['email'] != value:
                    return False
            elif key == 'employee.department':
                if report_data['employee']['department'] != value:
                    return False
            elif key == 'employee.role':
                if report_data['employee']['role'] != value:
                    return False
            elif key == 'week':
                if report_data['week'] != value:
                    return False
            elif key == 'week_range':
                start_week, end_week = value
                if not (start_week <= report_data['week'] <= end_week):
                    return False
            elif key == 'date_range':
                start_date, end_date = value
                report_date = datetime.fromisoformat(report_data['created_at'])
                if not (start_date <= report_date <= end_date):
                    return False
        
        return True
    
    def cleanup(self) -> None:
        """清理资源"""
        self._reports_cache.clear()
        self.logger.info("周报仓储资源清理完成")
