#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心基础抽象类模块

模块描述: 定义系统中所有组件和服务的基础抽象类
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: abc, typing, logging, datetime
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from .types import ComponentConfig, ComponentStatus, ServiceResponse
from .exceptions import ComponentInitializationError, ValidationError

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

class BaseComponent(ABC):
    """
    所有组件的基类

    提供组件的基础功能：
    - 配置管理
    - 生命周期管理
    - 日志记录
    - 状态跟踪
    """

    def __init__(self, config: ComponentConfig, logger: Optional[logging.Logger] = None):
        """
        初始化组件

        Args:
            config: 组件配置
            logger: 日志记录器，如果为None则创建默认logger
        """
        self.config = config
        self.logger = logger or logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._status = ComponentStatus.STOPPED
        self._initialized = False
        self._start_time: Optional[datetime] = None
        self._metrics: Dict[str, Any] = {}

        # 验证配置
        if not self.validate_config():
            raise ComponentInitializationError(
                f"组件配置验证失败: {self.config.name}",
                component_name=self.config.name
            )

    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化组件

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    def validate_config(self) -> bool:
        """
        验证配置

        Returns:
            bool: 配置是否有效
        """
        pass

    def start(self) -> bool:
        """
        启动组件

        Returns:
            bool: 启动是否成功
        """
        try:
            self._status = ComponentStatus.STARTING
            self.logger.info(f"正在启动组件: {self.config.name}")

            if not self._initialized:
                if not self.initialize():
                    self._status = ComponentStatus.ERROR
                    return False
                self._initialized = True

            self._status = ComponentStatus.RUNNING
            self._start_time = datetime.now()
            self.logger.info(f"组件启动成功: {self.config.name}")
            return True

        except Exception as e:
            self._status = ComponentStatus.ERROR
            self.logger.error(f"组件启动失败: {self.config.name}, 错误: {e}")
            return False

    def stop(self) -> bool:
        """
        停止组件

        Returns:
            bool: 停止是否成功
        """
        try:
            self._status = ComponentStatus.STOPPING
            self.logger.info(f"正在停止组件: {self.config.name}")

            # 子类可以重写此方法实现自定义停止逻辑
            self._cleanup()

            self._status = ComponentStatus.STOPPED
            self._start_time = None
            self.logger.info(f"组件停止成功: {self.config.name}")
            return True

        except Exception as e:
            self._status = ComponentStatus.ERROR
            self.logger.error(f"组件停止失败: {self.config.name}, 错误: {e}")
            return False

    def _cleanup(self) -> None:
        """清理资源，子类可重写"""
        pass

    def get_info(self) -> Dict[str, Any]:
        """
        获取组件信息

        Returns:
            Dict[str, Any]: 组件信息
        """
        uptime = None
        if self._start_time:
            uptime = (datetime.now() - self._start_time).total_seconds()

        return {
            'name': self.config.name,
            'version': self.config.version,
            'status': self._status.value,
            'enabled': self.config.enabled,
            'initialized': self._initialized,
            'start_time': self._start_time.isoformat() if self._start_time else None,
            'uptime_seconds': uptime,
            'service_level': self.config.service_level.value,
            'metrics': self._metrics.copy()
        }

    def update_metric(self, key: str, value: Any) -> None:
        """更新指标"""
        self._metrics[key] = value

    def get_metric(self, key: str, default: Any = None) -> Any:
        """获取指标"""
        return self._metrics.get(key, default)

    @property
    def status(self) -> ComponentStatus:
        """获取组件状态"""
        return self._status

    @property
    def is_running(self) -> bool:
        """检查组件是否运行中"""
        return self._status == ComponentStatus.RUNNING

    @property
    def is_initialized(self) -> bool:
        """检查组件是否已初始化"""
        return self._initialized

class BaseService(BaseComponent):
    """
    服务基类

    扩展BaseComponent，添加服务特有功能：
    - 依赖管理
    - 健康检查
    - 服务发现
    """

    def __init__(self, config: ComponentConfig, dependencies: List['BaseComponent'] = None):
        """
        初始化服务

        Args:
            config: 服务配置
            dependencies: 依赖的组件列表
        """
        super().__init__(config)
        self.dependencies = dependencies or []
        self._health_check_interval = 30  # 秒
        self._last_health_check: Optional[datetime] = None

    def add_dependency(self, component: 'BaseComponent') -> None:
        """
        添加依赖组件

        Args:
            component: 依赖的组件
        """
        if component not in self.dependencies:
            self.dependencies.append(component)
            self.logger.info(f"添加依赖: {component.config.name}")

    def check_dependencies(self) -> bool:
        """
        检查依赖组件状态

        Returns:
            bool: 所有依赖是否都正常
        """
        for dep in self.dependencies:
            if not dep.is_running:
                self.logger.error(f"依赖组件未运行: {dep.config.name}")
                return False
        return True

    def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            Dict[str, Any]: 健康状态信息
        """
        self._last_health_check = datetime.now()

        health_status = {
            'healthy': True,
            'timestamp': self._last_health_check.isoformat(),
            'service_name': self.config.name,
            'status': self._status.value,
            'dependencies': [],
            'metrics': self._metrics.copy()
        }

        # 检查依赖
        for dep in self.dependencies:
            dep_healthy = dep.is_running
            health_status['dependencies'].append({
                'name': dep.config.name,
                'healthy': dep_healthy,
                'status': dep.status.value
            })

            if not dep_healthy:
                health_status['healthy'] = False

        # 检查自身状态
        if self._status != ComponentStatus.RUNNING:
            health_status['healthy'] = False

        return health_status

    @abstractmethod
    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务信息

        Returns:
            Dict[str, Any]: 服务信息
        """
        pass

class BaseRepository(BaseComponent):
    """
    数据访问基类

    提供统一的CRUD接口和数据访问模式
    """

    @abstractmethod
    def create(self, entity: Any) -> Any:
        """
        创建实体

        Args:
            entity: 要创建的实体

        Returns:
            Any: 创建后的实体
        """
        pass

    @abstractmethod
    def get_by_id(self, entity_id: str) -> Optional[Any]:
        """
        根据ID获取实体

        Args:
            entity_id: 实体ID

        Returns:
            Optional[Any]: 实体对象，如果不存在则返回None
        """
        pass

    @abstractmethod
    def update(self, entity: Any) -> Any:
        """
        更新实体

        Args:
            entity: 要更新的实体

        Returns:
            Any: 更新后的实体
        """
        pass

    @abstractmethod
    def delete(self, entity_id: str) -> bool:
        """
        删除实体

        Args:
            entity_id: 实体ID

        Returns:
            bool: 删除是否成功
        """
        pass

    @abstractmethod
    def query(self, criteria: Dict[str, Any]) -> List[Any]:
        """
        查询实体

        Args:
            criteria: 查询条件

        Returns:
            List[Any]: 查询结果列表
        """
        pass

    @abstractmethod
    def count(self, criteria: Dict[str, Any] = None) -> int:
        """
        统计实体数量

        Args:
            criteria: 查询条件

        Returns:
            int: 实体数量
        """
        pass


# 依赖注入容器
class ServiceContainer:
    """
    依赖注入容器

    管理服务的注册、创建和生命周期
    """

    def __init__(self):
        self._services: Dict[str, Type[BaseService]] = {}
        self._instances: Dict[str, BaseService] = {}
        self._singletons: Dict[str, BaseService] = {}
        self._configurations: Dict[str, ComponentConfig] = {}
        self.logger = logging.getLogger(__name__)

    def register_service(self, name: str, service_class: Type[BaseService],
                        config: ComponentConfig, singleton: bool = True) -> None:
        """
        注册服务

        Args:
            name: 服务名称
            service_class: 服务类
            config: 服务配置
            singleton: 是否为单例
        """
        if not issubclass(service_class, BaseService):
            raise ValueError(f"服务类必须继承自BaseService: {service_class}")

        self._services[name] = service_class
        self._configurations[name] = config

        if singleton:
            self._singletons[name] = None

        self.logger.info(f"注册服务: {name}, 单例: {singleton}")

    def get_service(self, name: str) -> BaseService:
        """
        获取服务实例

        Args:
            name: 服务名称

        Returns:
            BaseService: 服务实例
        """
        if name not in self._services:
            raise ValueError(f"服务未注册: {name}")

        # 检查是否为单例
        if name in self._singletons:
            if self._singletons[name] is None:
                self._singletons[name] = self._create_service(name)
            return self._singletons[name]

        # 非单例，每次创建新实例
        return self._create_service(name)

    def _create_service(self, name: str) -> BaseService:
        """创建服务实例"""
        service_class = self._services[name]
        config = self._configurations[name]

        # 解析依赖
        dependencies = []
        if hasattr(service_class, '_dependencies'):
            for dep_name in service_class._dependencies:
                dependencies.append(self.get_service(dep_name))

        return service_class(config, dependencies)

    def start_all_services(self) -> bool:
        """启动所有服务"""
        self.logger.info("开始启动所有服务...")

        for name in self._services:
            try:
                service = self.get_service(name)
                if not service.start():
                    self.logger.error(f"服务启动失败: {name}")
                    return False
                self.logger.info(f"服务启动成功: {name}")
            except Exception as e:
                self.logger.error(f"服务启动异常: {name}, 错误: {e}")
                return False

        self.logger.info("所有服务启动完成")
        return True

    def stop_all_services(self) -> bool:
        """停止所有服务"""
        self.logger.info("开始停止所有服务...")

        # 按相反顺序停止服务
        service_names = list(self._services.keys())
        for name in reversed(service_names):
            try:
                if name in self._instances:
                    service = self._instances[name]
                    service.stop()
                    self.logger.info(f"服务停止成功: {name}")
            except Exception as e:
                self.logger.error(f"服务停止异常: {name}, 错误: {e}")

        self.logger.info("所有服务停止完成")
        return True

    def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务状态"""
        status = {}
        for name in self._services:
            try:
                service = self.get_service(name)
                status[name] = service.health_check()
            except Exception as e:
                status[name] = {
                    'healthy': False,
                    'error': str(e),
                    'service_name': name
                }
        return status


# 全局服务容器实例
_service_container = ServiceContainer()


def get_service_container() -> ServiceContainer:
    """获取全局服务容器"""
    return _service_container


def register_service(name: str, service_class: Type[BaseService],
                    config: ComponentConfig, singleton: bool = True) -> None:
    """注册服务到全局容器"""
    _service_container.register_service(name, service_class, config, singleton)


def get_service(name: str) -> BaseService:
    """从全局容器获取服务"""
    return _service_container.get_service(name)