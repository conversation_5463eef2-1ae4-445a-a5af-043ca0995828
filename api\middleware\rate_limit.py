#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
限流中间件
实现基于令牌桶和滑动窗口的智能限流策略
"""
import time
import logging
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON>NResponse
import asyncio
from threading import Lock
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class TokenBucket:
    """令牌桶限流算法"""
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        初始化令牌桶
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌补充速率（令牌/秒）
        """
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self.lock = Lock()
    
    def _refill(self):
        """补充令牌"""
        now = time.time()
        elapsed = now - self.last_refill
        tokens_to_add = elapsed * self.refill_rate
        
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now
    
    def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        with self.lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    def get_available_tokens(self) -> float:
        """获取可用令牌数"""
        with self.lock:
            self._refill()
            return self.tokens


class SlidingWindow:
    """滑动窗口限流算法"""
    
    def __init__(self, window_size: int, max_requests: int):
        """
        初始化滑动窗口
        
        Args:
            window_size: 窗口大小（秒）
            max_requests: 窗口内最大请求数
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.requests = deque()
        self.lock = Lock()
    
    def _clean_old_requests(self):
        """清理过期请求"""
        now = time.time()
        while self.requests and now - self.requests[0] > self.window_size:
            self.requests.popleft()
    
    def is_allowed(self) -> bool:
        """检查是否允许请求"""
        with self.lock:
            self._clean_old_requests()
            
            if len(self.requests) < self.max_requests:
                self.requests.append(time.time())
                return True
            return False
    
    def get_current_requests(self) -> int:
        """获取当前窗口内请求数"""
        with self.lock:
            self._clean_old_requests()
            return len(self.requests)


class RateLimiter:
    """综合限流器"""
    
    def __init__(self):
        self.limiters: Dict[str, Dict[str, Any]] = {}
        self.lock = Lock()
        
        # 默认限流配置
        self.default_config = {
            'global': {'type': 'token_bucket', 'capacity': 1000, 'refill_rate': 100},
            'per_ip': {'type': 'sliding_window', 'window_size': 60, 'max_requests': 100},
            'per_user': {'type': 'sliding_window', 'window_size': 60, 'max_requests': 200},
            'per_endpoint': {
                '/api/report/analyze': {'type': 'token_bucket', 'capacity': 50, 'refill_rate': 5},
                '/api/email/fetch': {'type': 'token_bucket', 'capacity': 10, 'refill_rate': 1},
                '/api/report/query': {'type': 'sliding_window', 'window_size': 60, 'max_requests': 300},
            }
        }
    
    def _get_limiter_key(self, limiter_type: str, identifier: str) -> str:
        """生成限流器键"""
        return f"{limiter_type}:{identifier}"
    
    def _create_limiter(self, config: Dict[str, Any]):
        """创建限流器实例"""
        if config['type'] == 'token_bucket':
            return TokenBucket(config['capacity'], config['refill_rate'])
        elif config['type'] == 'sliding_window':
            return SlidingWindow(config['window_size'], config['max_requests'])
        else:
            raise ValueError(f"不支持的限流器类型: {config['type']}")
    
    def _get_or_create_limiter(self, limiter_key: str, config: Dict[str, Any]):
        """获取或创建限流器"""
        if limiter_key not in self.limiters:
            with self.lock:
                if limiter_key not in self.limiters:
                    self.limiters[limiter_key] = {
                        'limiter': self._create_limiter(config),
                        'config': config,
                        'created_at': time.time()
                    }
        
        return self.limiters[limiter_key]['limiter']
    
    def check_global_limit(self) -> bool:
        """检查全局限流"""
        config = self.default_config['global']
        limiter_key = self._get_limiter_key('global', 'system')
        limiter = self._get_or_create_limiter(limiter_key, config)
        
        if isinstance(limiter, TokenBucket):
            return limiter.consume(1)
        elif isinstance(limiter, SlidingWindow):
            return limiter.is_allowed()
        
        return True
    
    def check_ip_limit(self, ip: str) -> bool:
        """检查IP限流"""
        config = self.default_config['per_ip']
        limiter_key = self._get_limiter_key('ip', ip)
        limiter = self._get_or_create_limiter(limiter_key, config)
        
        if isinstance(limiter, TokenBucket):
            return limiter.consume(1)
        elif isinstance(limiter, SlidingWindow):
            return limiter.is_allowed()
        
        return True
    
    def check_user_limit(self, user_id: str) -> bool:
        """检查用户限流"""
        config = self.default_config['per_user']
        limiter_key = self._get_limiter_key('user', user_id)
        limiter = self._get_or_create_limiter(limiter_key, config)
        
        if isinstance(limiter, TokenBucket):
            return limiter.consume(1)
        elif isinstance(limiter, SlidingWindow):
            return limiter.is_allowed()
        
        return True
    
    def check_endpoint_limit(self, endpoint: str) -> bool:
        """检查端点限流"""
        endpoint_config = self.default_config['per_endpoint'].get(endpoint)
        if not endpoint_config:
            return True
        
        limiter_key = self._get_limiter_key('endpoint', endpoint)
        limiter = self._get_or_create_limiter(limiter_key, endpoint_config)
        
        if isinstance(limiter, TokenBucket):
            return limiter.consume(1)
        elif isinstance(limiter, SlidingWindow):
            return limiter.is_allowed()
        
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取限流统计"""
        stats = {
            'total_limiters': len(self.limiters),
            'limiter_types': defaultdict(int),
            'active_limiters': []
        }
        
        current_time = time.time()
        for key, limiter_info in self.limiters.items():
            limiter_type = limiter_info['config']['type']
            stats['limiter_types'][limiter_type] += 1
            
            # 获取活跃限流器信息
            limiter = limiter_info['limiter']
            if isinstance(limiter, TokenBucket):
                available_tokens = limiter.get_available_tokens()
                stats['active_limiters'].append({
                    'key': key,
                    'type': 'token_bucket',
                    'available_tokens': available_tokens,
                    'capacity': limiter.capacity
                })
            elif isinstance(limiter, SlidingWindow):
                current_requests = limiter.get_current_requests()
                stats['active_limiters'].append({
                    'key': key,
                    'type': 'sliding_window',
                    'current_requests': current_requests,
                    'max_requests': limiter.max_requests
                })
        
        return stats
    
    def cleanup_expired_limiters(self, max_age: int = 3600):
        """清理过期的限流器"""
        current_time = time.time()
        expired_keys = []
        
        for key, limiter_info in self.limiters.items():
            if current_time - limiter_info['created_at'] > max_age:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.limiters[key]
        
        logger.info(f"清理了 {len(expired_keys)} 个过期限流器")


# 全局限流器实例
rate_limiter = RateLimiter()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app, enabled: bool = True):
        super().__init__(app)
        self.enabled = enabled
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 优先从X-Forwarded-For获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # 从X-Real-IP获取
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 从连接信息获取
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    def _get_user_id(self, request: Request) -> str:
        """获取用户ID（从认证信息中提取）"""
        # 这里可以从JWT token或session中提取用户ID
        # 暂时使用IP作为用户标识
        return self._get_client_ip(request)
    
    def _create_rate_limit_response(self, limit_type: str, retry_after: int = 60) -> JSONResponse:
        """创建限流响应"""
        return JSONResponse(
            status_code=429,
            content={
                "success": False,
                "code": 429,
                "message": f"请求频率过高，请稍后重试",
                "error": {
                    "type": "RateLimitExceeded",
                    "limit_type": limit_type,
                    "retry_after": retry_after
                },
                "timestamp": time.time()
            },
            headers={
                "Retry-After": str(retry_after),
                "X-RateLimit-Limit-Type": limit_type
            }
        )
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        if not self.enabled:
            return await call_next(request)
        
        # 获取请求信息
        client_ip = self._get_client_ip(request)
        user_id = self._get_user_id(request)
        endpoint = request.url.path
        
        # 检查全局限流
        if not rate_limiter.check_global_limit():
            logger.warning(f"全局限流触发: IP={client_ip}, endpoint={endpoint}")
            return self._create_rate_limit_response("global")
        
        # 检查IP限流
        if not rate_limiter.check_ip_limit(client_ip):
            logger.warning(f"IP限流触发: IP={client_ip}, endpoint={endpoint}")
            return self._create_rate_limit_response("ip")
        
        # 检查用户限流
        if not rate_limiter.check_user_limit(user_id):
            logger.warning(f"用户限流触发: user={user_id}, endpoint={endpoint}")
            return self._create_rate_limit_response("user")
        
        # 检查端点限流
        if not rate_limiter.check_endpoint_limit(endpoint):
            logger.warning(f"端点限流触发: endpoint={endpoint}, IP={client_ip}")
            return self._create_rate_limit_response("endpoint")
        
        # 执行请求
        response = await call_next(request)
        
        # 添加限流信息到响应头
        response.headers["X-RateLimit-Remaining"] = "available"
        
        return response
