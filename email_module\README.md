# 邮件模块 (email_module)

## 模块概述

邮件模块负责邮件的采集、解析、处理和管理，是AI驱动邮件周报分析系统的核心数据源模块。

## 模块结构

```
email_module/
├── README.md                    # 模块说明文档
├── __init__.py                  # 模块初始化
├── email_connection.py          # 邮件连接管理（已增强）
├── email_parser.py              # 邮件解析器
├── email_normalizer.py          # 邮箱标准化处理
├── email_filter.py              # 邮件过滤器
├── attachment_downloader.py     # 附件下载器
├── attachment_parser.py         # 附件解析器
├── attachment_processor.py      # 附件处理器
├── downloader.py                # 邮件下载器
├── email_attachment.py          # 邮件附件处理
├── email_type_analyzer.py       # 邮件类型分析器
└── mock_email_connection.py     # 模拟邮件连接（测试用）
```

## 核心功能

### 1. 邮件连接管理 (email_connection.py)
- **智能重连机制**：支持指数退避重试策略
- **连接健康检查**：定期检查连接状态，自动恢复
- **连接池管理**：维护多个备用连接，提高稳定性
- **异常处理**：完善的异常捕获和处理机制

### 2. 邮件解析 (email_parser.py)
- **多格式支持**：支持HTML、纯文本等多种邮件格式
- **编码处理**：自动识别和处理各种字符编码
- **内容提取**：准确提取邮件正文、发件人、主题等信息

### 3. 附件处理
- **自动下载**：支持各类附件的自动下载
- **格式解析**：支持Excel、Word、PDF等格式解析
- **重复检测**：防止重复下载相同附件

### 4. 断点续传
- **精确记录**：基于邮件ID的精确断点记录
- **多级断点**：支持邮件内容和附件的分级断点
- **验证机制**：确保续传的准确性和完整性

## 系统优化特性

### 连接稳定性增强
- 实现智能重连机制，自动检测连接状态并重新建立连接
- 优化连接池管理，维护多个备用连接
- 实现指数退避重试策略，避免频繁重连加剧问题

### 断点续传完善
- 实现基于邮件ID的精确断点记录
- 增加断点验证机制，确保续传准确性
- 支持多级断点，细化断点粒度到邮件内容和附件

### 邮件解析增强
- 增强HTML内容提取能力，更准确识别正文
- 完善编码处理，支持更多特殊字符集
- 优化附件处理，提高各类附件的解析成功率

## 配置说明

### IMAP配置
```python
IMAP_CONFIG = {
    "server": "imap.exmail.qq.com",
    "port": 993,
    "email": "<EMAIL>",
    "password": "your_password",
    "use_ssl": True,
    "days": 30,
    "max_emails": 100,
    "use_mock": False,
    # 连接稳定性配置
    "max_retries": 5,
    "base_delay": 1,
    "max_delay": 60,
    "health_check_interval": 300
}
```

## 使用示例

### 基本邮件下载
```python
from email_module.email_filter import EmailFilter

# 创建邮件过滤器
filter = EmailFilter()

# 下载最近7天的邮件
result = filter.download_emails(
    start_date="2024-05-01",
    max_emails=50
)

print(f"下载了 {result['saved']} 封邮件")
```

### 使用增强连接
```python
from email_module.email_connection import EmailConnection
from config import IMAP_CONFIG

# 创建增强连接
connection = EmailConnection(IMAP_CONFIG)

# 自动重连和健康检查
if connection.connect():
    emails = connection.search_emails(max_emails=10)
    for email_id in emails:
        content = connection.fetch_email(email_id)
        # 处理邮件内容
```

## 性能指标

### 目标性能
- **邮件处理速度**：30封/分钟
- **连接稳定性**：断点续传成功率>99%
- **异常恢复**：自动重连成功率>95%

### 监控指标
- 邮件下载成功率
- 连接重试次数
- 附件下载完成率
- 解析错误率

## 测试覆盖

### 单元测试
- 邮件连接测试
- 邮件解析测试
- 附件下载测试
- 断点续传测试

### 集成测试
- 端到端邮件处理流程
- 异常恢复测试
- 性能压力测试

## 注意事项

1. **邮箱安全**：确保邮箱密码和配置信息的安全性
2. **网络稳定**：模块已优化网络不稳定情况的处理
3. **存储空间**：注意附件下载的存储空间管理
4. **权限控制**：确保邮箱访问权限的合规性

## 更新日志

### [2024-05-25] 邮件连接稳定性增强
- 增强EmailConnection类，添加智能重连机制
- 实现指数退避重试策略
- 添加连接健康检查功能
- 优化异常处理和日志记录

### [2024-05-25] 模块文档规范化
- 创建模块README文档
- 规范化目录结构说明
- 添加使用示例和配置说明
