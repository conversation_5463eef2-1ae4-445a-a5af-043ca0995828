#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域实体定义模块

模块描述: 定义系统核心业务实体，包括员工、工作项、周报等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: dataclasses, typing, datetime, enum
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 枚举定义
class TaskComplexity(Enum):
    """任务复杂度枚举"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"
    CRITICAL = "极高"

class TaskCategory(Enum):
    """任务类别枚举"""
    DEVELOPMENT = "开发"
    TESTING = "测试"
    MEETING = "会议"
    TRAINING = "培训"
    SUPPORT = "支持"
    MANAGEMENT = "管理"
    RESEARCH = "研究"
    DOCUMENTATION = "文档"

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "待处理"
    IN_PROGRESS = "进行中"
    COMPLETED = "已完成"
    CANCELLED = "已取消"
    BLOCKED = "阻塞"

class EmployeeLevel(Enum):
    """员工级别枚举"""
    JUNIOR = "初级"
    INTERMEDIATE = "中级"
    SENIOR = "高级"
    EXPERT = "专家"
    LEAD = "主管"

# 实体定义
@dataclass
class Employee:
    """
    员工实体 - 核心业务实体

    表示系统中的员工信息，包括基本信息、技能、部门等
    """
    email: str
    name: str
    department: str
    role: str
    level: Optional[EmployeeLevel] = None
    skills: List[str] = field(default_factory=list)
    manager_email: Optional[str] = None
    hire_date: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """初始化后处理"""
        if not self.email:
            raise ValueError("员工邮箱不能为空")
        if not self.name:
            raise ValueError("员工姓名不能为空")
        if not self.department:
            raise ValueError("员工部门不能为空")
        if not self.role:
            raise ValueError("员工角色不能为空")

    def add_skill(self, skill: str) -> None:
        """
        添加技能

        Args:
            skill: 技能名称
        """
        if skill and skill not in self.skills:
            self.skills.append(skill)
            self.updated_at = datetime.now()

    def remove_skill(self, skill: str) -> None:
        """
        移除技能

        Args:
            skill: 技能名称
        """
        if skill in self.skills:
            self.skills.remove(skill)
            self.updated_at = datetime.now()

    def has_skill(self, skill: str) -> bool:
        """
        检查是否具有某项技能

        Args:
            skill: 技能名称

        Returns:
            bool: 是否具有该技能
        """
        return skill in self.skills

    def get_experience_years(self) -> Optional[float]:
        """
        获取工作经验年数

        Returns:
            Optional[float]: 工作经验年数，如果没有入职日期则返回None
        """
        if self.hire_date:
            delta = datetime.now() - self.hire_date
            return delta.days / 365.25
        return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'email': self.email,
            'name': self.name,
            'department': self.department,
            'role': self.role,
            'level': self.level.value if self.level else None,
            'skills': self.skills.copy(),
            'manager_email': self.manager_email,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'is_active': self.is_active,
            'metadata': self.metadata.copy(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

@dataclass
class WorkItem:
    """
    工作项实体

    表示员工的具体工作任务
    """
    title: str
    description: str
    duration_hours: float
    complexity: TaskComplexity
    category: TaskCategory
    date: datetime
    employee_email: str
    status: TaskStatus = TaskStatus.COMPLETED
    priority: int = 3  # 1-5，1最高，5最低
    tags: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)  # 依赖的其他任务ID
    deliverables: List[str] = field(default_factory=list)  # 交付物
    extra_data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """初始化后处理"""
        if not self.title:
            raise ValueError("工作项标题不能为空")
        if not self.employee_email:
            raise ValueError("员工邮箱不能为空")
        if self.duration_hours < 0:
            raise ValueError("工作时长不能为负数")
        if not (1 <= self.priority <= 5):
            raise ValueError("优先级必须在1-5之间")

    def add_tag(self, tag: str) -> None:
        """
        添加标签

        Args:
            tag: 标签名称
        """
        if tag and tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now()

    def remove_tag(self, tag: str) -> None:
        """
        移除标签

        Args:
            tag: 标签名称
        """
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now()

    def calculate_complexity_score(self) -> float:
        """
        计算复杂度分数

        Returns:
            float: 复杂度分数
        """
        complexity_scores = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0,
            TaskComplexity.CRITICAL: 4.0
        }
        return complexity_scores.get(self.complexity, 1.0)

    def calculate_weighted_hours(self) -> float:
        """
        计算加权工时（考虑复杂度）

        Returns:
            float: 加权工时
        """
        return self.duration_hours * self.calculate_complexity_score()

    def is_high_priority(self) -> bool:
        """
        判断是否高优先级任务

        Returns:
            bool: 是否高优先级
        """
        return self.priority <= 2

    def add_dependency(self, task_id: str) -> None:
        """
        添加依赖任务

        Args:
            task_id: 依赖任务ID
        """
        if task_id and task_id not in self.dependencies:
            self.dependencies.append(task_id)
            self.updated_at = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'title': self.title,
            'description': self.description,
            'duration_hours': self.duration_hours,
            'complexity': self.complexity.value,
            'category': self.category.value,
            'date': self.date.isoformat(),
            'employee_email': self.employee_email,
            'status': self.status.value,
            'priority': self.priority,
            'tags': self.tags.copy(),
            'dependencies': self.dependencies.copy(),
            'deliverables': self.deliverables.copy(),
            'extra_data': self.extra_data.copy(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

@dataclass
class WeeklyReport:
    """
    周报实体 - 聚合根

    表示员工的周报信息，包含多个工作项
    """
    report_id: str
    employee: Employee
    week: str  # 格式: YYYY-WXX，如 2024-W01
    work_items: List[WorkItem]
    summary: Dict[str, Any]
    metrics: Dict[str, Any]
    ai_version: str
    raw_text: str
    tags: List[str] = field(default_factory=list)
    anomaly_flags: List[str] = field(default_factory=list)
    quality_score: float = 0.0  # 0.0-1.0
    completeness_score: float = 0.0  # 0.0-1.0
    reviewed: bool = False
    reviewer_email: Optional[str] = None
    review_comments: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """初始化后处理"""
        if not self.report_id:
            raise ValueError("周报ID不能为空")
        if not self.week:
            raise ValueError("周报周次不能为空")
        if not self.raw_text:
            raise ValueError("周报原始文本不能为空")

    def calculate_total_hours(self) -> float:
        """
        计算总工时

        Returns:
            float: 总工时
        """
        return sum(item.duration_hours for item in self.work_items)

    def calculate_weighted_hours(self) -> float:
        """
        计算加权总工时

        Returns:
            float: 加权总工时
        """
        return sum(item.calculate_weighted_hours() for item in self.work_items)

    def get_work_items_by_category(self, category: TaskCategory) -> List[WorkItem]:
        """
        按类别获取工作项

        Args:
            category: 任务类别

        Returns:
            List[WorkItem]: 指定类别的工作项列表
        """
        return [item for item in self.work_items if item.category == category]

    def get_work_items_by_complexity(self, complexity: TaskComplexity) -> List[WorkItem]:
        """
        按复杂度获取工作项

        Args:
            complexity: 任务复杂度

        Returns:
            List[WorkItem]: 指定复杂度的工作项列表
        """
        return [item for item in self.work_items if item.complexity == complexity]

    def add_anomaly_flag(self, flag: str) -> None:
        """
        添加异常标记

        Args:
            flag: 异常标记
        """
        if flag and flag not in self.anomaly_flags:
            self.anomaly_flags.append(flag)
            self.updated_at = datetime.now()

    def remove_anomaly_flag(self, flag: str) -> None:
        """
        移除异常标记

        Args:
            flag: 异常标记
        """
        if flag in self.anomaly_flags:
            self.anomaly_flags.remove(flag)
            self.updated_at = datetime.now()

    def has_anomalies(self) -> bool:
        """
        检查是否有异常

        Returns:
            bool: 是否有异常
        """
        return len(self.anomaly_flags) > 0

    def get_category_distribution(self) -> Dict[str, int]:
        """
        获取任务类别分布

        Returns:
            Dict[str, int]: 类别分布统计
        """
        distribution = {}
        for item in self.work_items:
            category = item.category.value
            distribution[category] = distribution.get(category, 0) + 1
        return distribution

    def get_complexity_distribution(self) -> Dict[str, int]:
        """
        获取复杂度分布

        Returns:
            Dict[str, int]: 复杂度分布统计
        """
        distribution = {}
        for item in self.work_items:
            complexity = item.complexity.value
            distribution[complexity] = distribution.get(complexity, 0) + 1
        return distribution

    def calculate_productivity_score(self) -> float:
        """
        计算生产力分数

        Returns:
            float: 生产力分数 (0.0-1.0)
        """
        if not self.work_items:
            return 0.0

        total_weighted_hours = self.calculate_weighted_hours()
        total_hours = self.calculate_total_hours()

        if total_hours == 0:
            return 0.0

        # 基于加权工时和完成任务数量计算
        complexity_factor = total_weighted_hours / total_hours
        task_count_factor = min(len(self.work_items) / 10.0, 1.0)  # 假设10个任务为满分

        return min((complexity_factor * 0.7 + task_count_factor * 0.3), 1.0)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'report_id': self.report_id,
            'employee': self.employee.to_dict(),
            'week': self.week,
            'work_items': [item.to_dict() for item in self.work_items],
            'summary': self.summary.copy(),
            'metrics': self.metrics.copy(),
            'ai_version': self.ai_version,
            'raw_text': self.raw_text,
            'tags': self.tags.copy(),
            'anomaly_flags': self.anomaly_flags.copy(),
            'quality_score': self.quality_score,
            'completeness_score': self.completeness_score,
            'reviewed': self.reviewed,
            'reviewer_email': self.reviewer_email,
            'review_comments': self.review_comments,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

@dataclass
class AnalysisResult:
    """
    分析结果实体

    表示AI分析的结果数据
    """
    result_id: str
    report_id: str
    analysis_type: str
    result_data: Dict[str, Any]
    confidence_score: float
    model_version: str
    processing_time: float
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """初始化后处理"""
        if not self.result_id:
            raise ValueError("结果ID不能为空")
        if not self.report_id:
            raise ValueError("报告ID不能为空")
        if not (0.0 <= self.confidence_score <= 1.0):
            raise ValueError("置信度分数必须在0.0到1.0之间")
        if self.processing_time < 0:
            raise ValueError("处理时间不能为负数")

    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """判断是否高置信度"""
        return self.confidence_score >= threshold

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'result_id': self.result_id,
            'report_id': self.report_id,
            'analysis_type': self.analysis_type,
            'result_data': self.result_data.copy(),
            'confidence_score': self.confidence_score,
            'model_version': self.model_version,
            'processing_time': self.processing_time,
            'created_at': self.created_at.isoformat(),
            'metadata': self.metadata.copy()
        }
