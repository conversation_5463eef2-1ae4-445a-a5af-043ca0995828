#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI分析模块 - 增强版
提供完整的数据分析流程优化功能
"""

# 增强版组件
from .enhanced_analyzer import EnhancedDataAnalyzer, AnalysisCache, ModelPerformanceTracker, AnalysisQualityEvaluator
from .data_flow_optimizer import DataFlowOptimizer, DataFlowMetrics, DataQualityChecker
from .smart_scheduler import SmartScheduler, AnalysisTask, TaskPriority, TaskStatus, SystemMonitor
from .integrated_analyzer import IntegratedAnalyzer

# 原有组件（向后兼容）
from .analysis_engine import AnalysisEngine
from .model_api import ModelAPI
from .prompt_generator import PromptGenerator
from .result_processor import ResultProcessor
from .rag_adapter import RAGAdapter

__all__ = [
    # 增强版组件
    "EnhancedDataAnalyzer",
    "AnalysisCache",
    "ModelPerformanceTracker",
    "AnalysisQualityEvaluator",
    "DataFlowOptimizer",
    "DataFlowMetrics",
    "DataQualityChecker",
    "SmartScheduler",
    "AnalysisTask",
    "TaskPriority",
    "TaskStatus",
    "SystemMonitor",
    "IntegratedAnalyzer",

    # 原有组件（向后兼容）
    "AnalysisEngine",
    "ModelAPI",
    "PromptGenerator",
    "ResultProcessor",
    "RAGAdapter"
]
