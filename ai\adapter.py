import json
import os
import requests

CONFIG_PATH = os.path.join(os.path.dirname(__file__), "ai_config.json")


class AIAdapter:
    def __init__(self, config_path="ai/ai_config.json", provider="alibaba"):
        with open(config_path, "r", encoding="utf-8") as f:
            full_config = json.load(f)

        # 选择提供商配置
        if provider in full_config:
            self.config = full_config[provider]
        else:
            # 默认使用alibaba配置
            self.config = full_config.get("alibaba", {})

        self.model = self.config.get("model", "qwen-turbo-2025-04-28")
        self.api_key = self.config.get("api_key")
        self.api_url = self.config.get("api_url")
        self.temperature = self.config.get("temperature", 0.2)
        self.max_tokens = self.config.get("max_tokens", 2048)

    def call(self, prompt, stop=None):
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }
            payload = {
                "model": self.model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
            }
            if stop:
                payload["stop"] = stop

            resp = requests.post(
                self.api_url, headers=headers, json=payload, timeout=60
            )
            resp.raise_for_status()
            result = resp.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"AI调用出错: {e}")
            # 返回一个有效的JSON字符串，确保下游处理不会出错
            return json.dumps(
                {
                    "email_type": "other",
                    "confidence": 0.5,
                    "reasoning": f"AI调用出错: {e}",
                }
            )


if __name__ == "__main__":
    # 简单测试
    adapter = AIAdapter("openai")
    prompt = "请用一句话介绍你自己。"
    print(adapter.call(prompt))
