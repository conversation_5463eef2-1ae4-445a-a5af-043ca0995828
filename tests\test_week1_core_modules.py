#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
第一周核心模块测试

模块描述: 测试第一阶段开发的核心模块功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import sys
import os
import pytest
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 测试核心模块
def test_core_module_import():
    """测试核心模块导入"""
    from core import BaseComponent, BaseService, BaseRepository
    from core import IAnalyzer, IVisualizer, IDataProcessor
    from core import performance_monitor, cache_result, error_handler
    from core import CoreException, ValidationError, ProcessingError
    from core import AnalysisResult, ComponentConfig, ServiceResponse

    assert BaseComponent is not None
    assert BaseService is not None
    assert BaseRepository is not None
    print("✅ 核心模块导入测试通过")

def test_domain_module_import():
    """测试领域模块导入"""
    from domain import Employee, WorkItem, WeeklyReport
    from domain import TaskComplexity, TaskCategory, TaskStatus, EmployeeLevel
    from domain import ProcessingContext, VisualizationConfig, FilterCriteria

    assert Employee is not None
    assert WorkItem is not None
    assert WeeklyReport is not None
    print("✅ 领域模块导入测试通过")

def test_config_module_import():
    """测试配置模块导入"""
    from config import config, ConfigFactory
    from config import BaseConfig, DevelopmentConfig, ProductionConfig

    assert config is not None
    assert config.environment == 'development'
    assert ConfigFactory is not None
    print("✅ 配置模块导入测试通过")

def test_employee_entity():
    """测试员工实体"""
    from domain import Employee, EmployeeLevel

    # 创建员工实例
    employee = Employee(
        email="<EMAIL>",
        name="测试员工",
        department="技术部",
        role="开发工程师",
        level=EmployeeLevel.INTERMEDIATE
    )

    # 测试基本属性
    assert employee.email == "<EMAIL>"
    assert employee.name == "测试员工"
    assert employee.department == "技术部"
    assert employee.role == "开发工程师"
    assert employee.level == EmployeeLevel.INTERMEDIATE

    # 测试技能管理
    employee.add_skill("Python")
    employee.add_skill("JavaScript")
    assert "Python" in employee.skills
    assert "JavaScript" in employee.skills
    assert employee.has_skill("Python")

    employee.remove_skill("JavaScript")
    assert "JavaScript" not in employee.skills

    # 测试字典转换
    employee_dict = employee.to_dict()
    assert employee_dict['email'] == "<EMAIL>"
    assert employee_dict['level'] == "中级"

    print("✅ 员工实体测试通过")

def test_work_item_entity():
    """测试工作项实体"""
    from domain import WorkItem, TaskComplexity, TaskCategory, TaskStatus

    # 创建工作项实例
    work_item = WorkItem(
        title="开发用户管理模块",
        description="实现用户注册、登录、权限管理功能",
        duration_hours=8.0,
        complexity=TaskComplexity.MEDIUM,
        category=TaskCategory.DEVELOPMENT,
        date=datetime.now(),
        employee_email="<EMAIL>",
        priority=2
    )

    # 测试基本属性
    assert work_item.title == "开发用户管理模块"
    assert work_item.duration_hours == 8.0
    assert work_item.complexity == TaskComplexity.MEDIUM
    assert work_item.category == TaskCategory.DEVELOPMENT
    assert work_item.priority == 2

    # 测试复杂度计算
    complexity_score = work_item.calculate_complexity_score()
    assert complexity_score == 2.0  # MEDIUM = 2.0

    # 测试加权工时
    weighted_hours = work_item.calculate_weighted_hours()
    assert weighted_hours == 16.0  # 8.0 * 2.0

    # 测试优先级判断
    assert work_item.is_high_priority()  # priority <= 2

    # 测试标签管理
    work_item.add_tag("前端")
    work_item.add_tag("后端")
    assert "前端" in work_item.tags
    assert "后端" in work_item.tags

    # 测试字典转换
    work_item_dict = work_item.to_dict()
    assert work_item_dict['complexity'] == "中"
    assert work_item_dict['category'] == "开发"

    print("✅ 工作项实体测试通过")

def test_weekly_report_entity():
    """测试周报实体"""
    from domain import Employee, WorkItem, WeeklyReport
    from domain import TaskComplexity, TaskCategory, EmployeeLevel

    # 创建员工
    employee = Employee(
        email="<EMAIL>",
        name="测试员工",
        department="技术部",
        role="开发工程师"
    )

    # 创建工作项
    work_items = [
        WorkItem(
            title="开发功能A",
            description="功能A描述",
            duration_hours=6.0,
            complexity=TaskComplexity.MEDIUM,
            category=TaskCategory.DEVELOPMENT,
            date=datetime.now(),
            employee_email="<EMAIL>"
        ),
        WorkItem(
            title="测试功能B",
            description="功能B测试",
            duration_hours=4.0,
            complexity=TaskComplexity.LOW,
            category=TaskCategory.TESTING,
            date=datetime.now(),
            employee_email="<EMAIL>"
        )
    ]

    # 创建周报
    weekly_report = WeeklyReport(
        report_id="WR-2024-W01-001",
        employee=employee,
        week="2024-W01",
        work_items=work_items,
        summary={"total_tasks": 2, "total_hours": 10.0},
        metrics={"productivity": 0.8, "quality": 0.9},
        ai_version="1.0.0",
        raw_text="本周完成了功能A的开发和功能B的测试..."
    )

    # 测试基本属性
    assert weekly_report.report_id == "WR-2024-W01-001"
    assert weekly_report.week == "2024-W01"
    assert len(weekly_report.work_items) == 2

    # 测试工时计算
    total_hours = weekly_report.calculate_total_hours()
    assert total_hours == 10.0  # 6.0 + 4.0

    weighted_hours = weekly_report.calculate_weighted_hours()
    assert weighted_hours == 16.0  # (6.0 * 2.0) + (4.0 * 1.0)

    # 测试按类别获取工作项
    dev_items = weekly_report.get_work_items_by_category(TaskCategory.DEVELOPMENT)
    assert len(dev_items) == 1
    assert dev_items[0].title == "开发功能A"

    test_items = weekly_report.get_work_items_by_category(TaskCategory.TESTING)
    assert len(test_items) == 1
    assert test_items[0].title == "测试功能B"

    # 测试分布统计
    category_dist = weekly_report.get_category_distribution()
    assert category_dist["开发"] == 1
    assert category_dist["测试"] == 1

    complexity_dist = weekly_report.get_complexity_distribution()
    assert complexity_dist["中"] == 1
    assert complexity_dist["低"] == 1

    # 测试生产力分数计算
    productivity_score = weekly_report.calculate_productivity_score()
    assert 0.0 <= productivity_score <= 1.0

    # 测试异常标记
    weekly_report.add_anomaly_flag("工时异常")
    assert "工时异常" in weekly_report.anomaly_flags
    assert weekly_report.has_anomalies()

    weekly_report.remove_anomaly_flag("工时异常")
    assert "工时异常" not in weekly_report.anomaly_flags
    assert not weekly_report.has_anomalies()

    print("✅ 周报实体测试通过")

def test_configuration_system():
    """测试配置系统"""
    from config import config, ConfigFactory, get_database_url, get_ai_config

    # 测试配置获取
    assert config.environment == 'development'
    assert config.debug == True

    # 测试数据库配置
    db_url = get_database_url()
    assert "postgresql://" in db_url
    assert "zkteci" in db_url

    # 测试AI配置
    ai_config = get_ai_config()
    assert 'base_url' in ai_config
    assert 'model_name' in ai_config
    assert ai_config['model_name'] == 'qwen2.5:7b'

    # 测试配置验证
    assert config.validate_config() == True

    # 测试配置字典
    config_dict = config.get_config_dict()
    assert 'database' in config_dict
    assert 'ai' in config_dict
    assert 'environment' in config_dict

    print("✅ 配置系统测试通过")

def test_value_objects():
    """测试值对象"""
    from domain import ProcessingContext, VisualizationConfig, FilterCriteria

    # 测试处理上下文
    context = ProcessingContext(
        user_id="<EMAIL>",
        department="技术部",
        role="开发工程师",
        preferences={"theme": "light"},
        timestamp=datetime.now()
    )

    assert context.user_id == "<EMAIL>"
    assert context.department == "技术部"
    assert context.get_preference("theme") == "light"
    assert context.get_preference("unknown", "default") == "default"

    # 测试更新时间戳
    import time
    time.sleep(0.01)  # 确保时间戳不同
    new_context = context.with_updated_timestamp()
    assert new_context.user_id == context.user_id
    assert new_context.timestamp >= context.timestamp

    # 测试可视化配置
    viz_config = VisualizationConfig(
        chart_type="line",
        title="测试图表",
        width=800,
        height=600
    )

    assert viz_config.chart_type == "line"
    assert viz_config.title == "测试图表"

    # 测试尺寸变更
    new_viz_config = viz_config.with_size(1200, 800)
    assert new_viz_config.width == 1200
    assert new_viz_config.height == 800
    assert new_viz_config.chart_type == "line"  # 其他属性保持不变

    # 测试筛选条件
    filter_criteria = FilterCriteria(
        department="技术部",
        week="2024-W01"
    )

    assert filter_criteria.department == "技术部"
    assert filter_criteria.week == "2024-W01"
    assert not filter_criteria.is_empty()
    assert filter_criteria.has_time_filter()
    assert filter_criteria.has_employee_filter()

    # 测试空筛选条件
    empty_filter = FilterCriteria()
    assert empty_filter.is_empty()

    print("✅ 值对象测试通过")

if __name__ == "__main__":
    """运行所有测试"""
    print("🚀 开始第一周核心模块测试...")

    try:
        test_core_module_import()
        test_domain_module_import()
        test_config_module_import()
        test_employee_entity()
        test_work_item_entity()
        test_weekly_report_entity()
        test_configuration_system()
        test_value_objects()

        print("\n🎉 所有测试通过！第一周核心模块开发完成。")
        print("📋 已完成的模块：")
        print("  ✅ core - 核心基础模块")
        print("  ✅ domain - 领域模型模块")
        print("  ✅ config - 配置管理模块")
        print("  ✅ tests - 测试模块")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
