#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI分析工具函数
提供通用的工具函数和默认值生成
"""

import uuid
import time
import logging
import functools
from datetime import datetime
from typing import Any, Dict, Optional, Callable


def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            logging.getLogger(func.__module__).info(
                f"{func.__name__} 执行完成，耗时: {duration:.2f}秒"
            )
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logging.getLogger(func.__module__).error(
                f"{func.__name__} 执行失败，耗时: {duration:.2f}秒，错误: {e}"
            )
            raise
    return wrapper


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """安全获取字典值"""
    if not isinstance(data, dict):
        return default
    return data.get(key, default)


def generate_report_id() -> str:
    """生成唯一的报告ID"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"report_{timestamp}_{unique_id}"


def create_default_analysis(sender_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """创建默认的分析结果"""
    if sender_info is None:
        sender_info = {}

    return {
        "report_id": generate_report_id(),
        "employee": {
            "name": safe_get(sender_info, "name", ""),
            "email": safe_get(sender_info, "email", "").strip().lower(),
            "department": safe_get(sender_info, "department", ""),
            "role": safe_get(sender_info, "role", "")
        },
        "week": datetime.now().strftime("%Y-W%U"),
        "work_items": [],
        "summary": {
            "total_hours": 0.0,
            "main_achievements": "",
            "risks": "",
            "suggestions": ""
        },
        "metrics": {
            "total_hours": 0.0,
            "task_count": 0,
            "saturation": 0.0,
            "saturation_tag": "不足"
        },
        "tags": [],
        "anomaly_flags": ["AI分析失败"],
        "innovation_analysis": "",
        "quality_analysis": "",
        "trend_analysis": "",
        "ai_version": "default",
        "raw_text": ""
    }


def validate_analysis_result(result: Dict[str, Any]) -> bool:
    """验证分析结果的完整性"""
    required_fields = [
        "report_id", "employee", "week", "work_items",
        "summary", "metrics", "tags", "anomaly_flags"
    ]

    for field in required_fields:
        if field not in result:
            return False

    # 验证employee字段
    employee = result.get("employee", {})
    employee_fields = ["name", "email", "department", "role"]
    for field in employee_fields:
        if field not in employee:
            return False

    return True


def normalize_email(email: str) -> str:
    """标准化邮箱地址"""
    if not email:
        return ""
    return email.strip().lower()


def calculate_saturation_tag(saturation: float) -> str:
    """根据饱和度计算标签"""
    if saturation >= 1.2:
        return "过载"
    elif saturation >= 0.8:
        return "饱和"
    elif saturation >= 0.5:
        return "适中"
    else:
        return "不足"


def merge_tags(tag_lists: list) -> list:
    """合并多个标签列表并去重"""
    all_tags = []
    for tag_list in tag_lists:
        if isinstance(tag_list, list):
            all_tags.extend(tag_list)
        elif isinstance(tag_list, str):
            all_tags.append(tag_list)

    # 去重并保持顺序
    unique_tags = []
    for tag in all_tags:
        if tag and tag not in unique_tags:
            unique_tags.append(tag)

    return unique_tags


def format_duration(hours: float) -> str:
    """格式化工时显示"""
    if hours == 0:
        return "0小时"
    elif hours < 1:
        minutes = int(hours * 60)
        return f"{minutes}分钟"
    else:
        return f"{hours:.1f}小时"


def extract_week_from_date(date_str: str) -> str:
    """从日期字符串提取周期"""
    try:
        if not date_str:
            return datetime.now().strftime("%Y-W%U")

        # 尝试解析日期
        if "W" in date_str:
            return date_str  # 已经是周期格式

        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return date_obj.strftime("%Y-W%U")
    except:
        return datetime.now().strftime("%Y-W%U")
