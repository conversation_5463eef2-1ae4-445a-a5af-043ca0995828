#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域服务模块

模块描述: 实现领域业务逻辑，协调实体和值对象完成复杂业务操作
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: typing, datetime, .entities, .value_objects, .repositories
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from .entities import Employee, WorkItem, WeeklyReport, TaskComplexity, TaskCategory
from .value_objects import ProcessingContext, FilterCriteria, MetricThresholds, TimeRange
from .repositories import (
    IEmployeeRepository, IWorkItemRepository, IWeeklyReportRepository,
    IAnalysisResultRepository, RepositoryFactory
)


class EmployeeDomainService:
    """员工领域服务"""
    
    def __init__(self, employee_repo: IEmployeeRepository):
        self.employee_repo = employee_repo
        self.logger = logging.getLogger(__name__)
    
    def validate_employee_data(self, employee: Employee) -> List[str]:
        """验证员工数据"""
        errors = []
        
        if not employee.email or '@' not in employee.email:
            errors.append("邮箱格式无效")
        
        if not employee.name or len(employee.name.strip()) < 2:
            errors.append("姓名长度至少2个字符")
        
        if not employee.department:
            errors.append("部门不能为空")
        
        if not employee.role:
            errors.append("角色不能为空")
        
        return errors
    
    def get_team_members(self, manager_email: str) -> List[Employee]:
        """获取团队成员"""
        # 这里应该根据组织架构获取下属
        # 简化实现：获取同部门员工
        manager = self.employee_repo.get_by_email(manager_email)
        if not manager:
            return []
        
        return self.employee_repo.get_by_department(manager.department)
    
    def calculate_workload_capacity(self, employee: Employee) -> Dict[str, float]:
        """计算员工工作负载能力"""
        # 根据角色和级别计算标准工时
        base_hours = {
            '开发工程师': 40.0,
            '技术支持': 38.0,
            '销售': 35.0,
            '管理': 45.0
        }
        
        role_hours = base_hours.get(employee.role, 40.0)
        
        # 根据级别调整
        level_multiplier = {
            'JUNIOR': 0.8,
            'INTERMEDIATE': 1.0,
            'SENIOR': 1.2,
            'EXPERT': 1.3,
            'LEAD': 1.1
        }
        
        level_key = employee.level.name if employee.level else 'INTERMEDIATE'
        multiplier = level_multiplier.get(level_key, 1.0)
        
        return {
            'standard_hours': role_hours,
            'capacity_hours': role_hours * multiplier,
            'efficiency_factor': multiplier
        }


class WorkItemDomainService:
    """工作项领域服务"""
    
    def __init__(self, work_item_repo: IWorkItemRepository):
        self.work_item_repo = work_item_repo
        self.logger = logging.getLogger(__name__)
    
    def calculate_complexity_score(self, work_item: WorkItem) -> float:
        """计算工作项复杂度分数"""
        base_scores = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0
        }
        
        base_score = base_scores.get(work_item.complexity, 1.0)
        
        # 根据工时调整
        if work_item.duration_hours > 8:
            base_score *= 1.2
        elif work_item.duration_hours < 2:
            base_score *= 0.8
        
        return base_score
    
    def categorize_work_items(self, work_items: List[WorkItem]) -> Dict[TaskCategory, List[WorkItem]]:
        """按类别分组工作项"""
        categorized = {}
        for item in work_items:
            if item.category not in categorized:
                categorized[item.category] = []
            categorized[item.category].append(item)
        
        return categorized
    
    def calculate_workload_distribution(self, work_items: List[WorkItem]) -> Dict[str, Any]:
        """计算工作负载分布"""
        total_hours = sum(item.duration_hours for item in work_items)
        category_hours = {}
        complexity_hours = {}
        
        for item in work_items:
            # 按类别统计
            category = item.category.value
            if category not in category_hours:
                category_hours[category] = 0
            category_hours[category] += item.duration_hours
            
            # 按复杂度统计
            complexity = item.complexity.value
            if complexity not in complexity_hours:
                complexity_hours[complexity] = 0
            complexity_hours[complexity] += item.duration_hours
        
        return {
            'total_hours': total_hours,
            'category_distribution': {
                k: {'hours': v, 'percentage': v / total_hours * 100 if total_hours > 0 else 0}
                for k, v in category_hours.items()
            },
            'complexity_distribution': {
                k: {'hours': v, 'percentage': v / total_hours * 100 if total_hours > 0 else 0}
                for k, v in complexity_hours.items()
            }
        }


class WeeklyReportDomainService:
    """周报领域服务"""
    
    def __init__(self, report_repo: IWeeklyReportRepository, 
                 work_item_service: WorkItemDomainService,
                 employee_service: EmployeeDomainService):
        self.report_repo = report_repo
        self.work_item_service = work_item_service
        self.employee_service = employee_service
        self.logger = logging.getLogger(__name__)
    
    def validate_report_completeness(self, report: WeeklyReport) -> Dict[str, Any]:
        """验证周报完整性"""
        issues = []
        score = 100.0
        
        # 检查基本字段
        if not report.summary:
            issues.append("缺少工作总结")
            score -= 20
        
        if not report.work_items:
            issues.append("缺少工作项明细")
            score -= 30
        else:
            # 检查工作项质量
            total_hours = sum(item.duration_hours for item in report.work_items)
            if total_hours < 20:
                issues.append("工作时长过少")
                score -= 15
            elif total_hours > 60:
                issues.append("工作时长过多")
                score -= 10
            
            # 检查工作项描述
            empty_descriptions = sum(1 for item in report.work_items 
                                   if not item.description or len(item.description.strip()) < 10)
            if empty_descriptions > 0:
                issues.append(f"{empty_descriptions}个工作项描述不充分")
                score -= empty_descriptions * 5
        
        return {
            'completeness_score': max(0, score),
            'issues': issues,
            'is_complete': score >= 80
        }
    
    def calculate_performance_metrics(self, report: WeeklyReport) -> Dict[str, Any]:
        """计算绩效指标"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        # 工作负载分析
        workload_dist = self.work_item_service.calculate_workload_distribution(report.work_items)
        
        # 员工能力分析
        capacity = self.employee_service.calculate_workload_capacity(report.employee)
        
        # 计算饱和度
        actual_hours = workload_dist['total_hours']
        standard_hours = capacity['standard_hours']
        saturation = actual_hours / standard_hours if standard_hours > 0 else 0
        
        # 计算效率指标
        high_complexity_hours = sum(
            item.duration_hours for item in report.work_items 
            if item.complexity == TaskComplexity.HIGH
        )
        efficiency_score = (high_complexity_hours / actual_hours * 100) if actual_hours > 0 else 0
        
        return {
            'workload_saturation': saturation,
            'efficiency_score': efficiency_score,
            'total_hours': actual_hours,
            'standard_hours': standard_hours,
            'workload_distribution': workload_dist,
            'capacity_info': capacity
        }
    
    def detect_anomalies(self, report: WeeklyReport, 
                        thresholds: MetricThresholds) -> List[Dict[str, Any]]:
        """检测异常"""
        anomalies = []
        metrics = self.calculate_performance_metrics(report)
        
        if 'error' in metrics:
            return anomalies
        
        # 工作负载异常
        saturation = metrics['workload_saturation']
        if saturation > thresholds.workload_high:
            anomalies.append({
                'type': 'workload_overload',
                'severity': 'high',
                'message': f'工作负载过高: {saturation:.2f}',
                'value': saturation,
                'threshold': thresholds.workload_high
            })
        elif saturation < thresholds.workload_low:
            anomalies.append({
                'type': 'workload_underload',
                'severity': 'medium',
                'message': f'工作负载过低: {saturation:.2f}',
                'value': saturation,
                'threshold': thresholds.workload_low
            })
        
        # 效率异常
        efficiency = metrics['efficiency_score']
        if efficiency < thresholds.efficiency_low:
            anomalies.append({
                'type': 'low_efficiency',
                'severity': 'medium',
                'message': f'工作效率偏低: {efficiency:.1f}%',
                'value': efficiency,
                'threshold': thresholds.efficiency_low
            })
        
        return anomalies


class AnalyticsDomainService:
    """分析领域服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_trend_analysis(self, reports: List[WeeklyReport], 
                               time_range: TimeRange) -> Dict[str, Any]:
        """计算趋势分析"""
        if not reports:
            return {'error': '无数据'}
        
        # 按时间排序
        sorted_reports = sorted(reports, key=lambda r: r.created_at)
        
        # 计算各项指标的趋势
        weekly_metrics = []
        for report in sorted_reports:
            total_hours = sum(item.duration_hours for item in report.work_items)
            complexity_score = sum(
                self._get_complexity_score(item.complexity) 
                for item in report.work_items
            ) / len(report.work_items) if report.work_items else 0
            
            weekly_metrics.append({
                'week': report.week,
                'total_hours': total_hours,
                'complexity_score': complexity_score,
                'work_item_count': len(report.work_items)
            })
        
        # 计算趋势
        if len(weekly_metrics) >= 2:
            hours_trend = self._calculate_trend([m['total_hours'] for m in weekly_metrics])
            complexity_trend = self._calculate_trend([m['complexity_score'] for m in weekly_metrics])
        else:
            hours_trend = complexity_trend = 'stable'
        
        return {
            'weekly_metrics': weekly_metrics,
            'trends': {
                'hours': hours_trend,
                'complexity': complexity_trend
            },
            'summary': {
                'total_weeks': len(weekly_metrics),
                'avg_hours': sum(m['total_hours'] for m in weekly_metrics) / len(weekly_metrics),
                'avg_complexity': sum(m['complexity_score'] for m in weekly_metrics) / len(weekly_metrics)
            }
        }
    
    def _get_complexity_score(self, complexity: TaskComplexity) -> float:
        """获取复杂度分数"""
        scores = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0
        }
        return scores.get(complexity, 1.0)
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return 'stable'
        
        # 简单的线性趋势计算
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        change_rate = (second_avg - first_avg) / first_avg if first_avg > 0 else 0
        
        if change_rate > 0.1:
            return 'increasing'
        elif change_rate < -0.1:
            return 'decreasing'
        else:
            return 'stable'
