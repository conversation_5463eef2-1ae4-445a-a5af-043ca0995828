#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础功能测试脚本
用于验证Python环境和基础模块功能
"""

print("=== Basic System Test ===")
print("Starting basic functionality test...")

# 测试基础导入
print("\n1. Testing basic imports...")
try:
    import sys
    import os
    import json
    import uuid
    from datetime import datetime
    print("✓ Basic imports successful")
except Exception as e:
    print(f"✗ Basic imports failed: {e}")
    exit(1)

# 测试项目路径
print("\n2. Testing project path...")
try:
    current_dir = os.getcwd()
    print(f"✓ Current directory: {current_dir}")
    
    # 添加项目路径
    if '.' not in sys.path:
        sys.path.append('.')
    print("✓ Project path added to sys.path")
except Exception as e:
    print(f"✗ Project path setup failed: {e}")

# 测试工具函数
print("\n3. Testing utility functions...")
try:
    # 手动实现基础功能，避免复杂导入
    def simple_generate_id():
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"report_{timestamp}_{unique_id}"
    
    test_id = simple_generate_id()
    print(f"✓ ID generation works: {test_id}")
except Exception as e:
    print(f"✗ Utility functions failed: {e}")

# 测试简单的AI分析逻辑
print("\n4. Testing simple analysis logic...")
try:
    def simple_analyze_text(text):
        """简单的文本分析"""
        lines = text.strip().split('\n')
        work_items = []
        total_hours = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 简单的工时提取
            hours = 1.0  # 默认工时
            if '小时' in line:
                import re
                match = re.search(r'(\d+(?:\.\d+)?)\s*小时', line)
                if match:
                    hours = float(match.group(1))
            elif 'hour' in line.lower():
                import re
                match = re.search(r'(\d+(?:\.\d+)?)\s*hour', line.lower())
                if match:
                    hours = float(match.group(1))
            
            work_item = {
                "title": line[:30] + "..." if len(line) > 30 else line,
                "hours": hours,
                "category": "work"
            }
            work_items.append(work_item)
            total_hours += hours
        
        return {
            "id": simple_generate_id(),
            "work_items": work_items,
            "total_hours": total_hours,
            "analysis_time": datetime.now().isoformat()
        }
    
    # 测试分析
    test_content = """
    Complete development task, 8 hours
    Attend meeting, 2 hours
    Write documentation, 3 hours
    """
    
    result = simple_analyze_text(test_content)
    print(f"✓ Simple analysis works:")
    print(f"  - Report ID: {result['id']}")
    print(f"  - Work items: {len(result['work_items'])}")
    print(f"  - Total hours: {result['total_hours']}")
    
except Exception as e:
    print(f"✗ Simple analysis failed: {e}")

# 测试JSON序列化
print("\n5. Testing JSON serialization...")
try:
    test_data = {
        "test_id": simple_generate_id(),
        "timestamp": datetime.now().isoformat(),
        "status": "success",
        "data": result if 'result' in locals() else {}
    }
    
    json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
    print("✓ JSON serialization works")
    
    # 保存测试结果
    with open("basic_test_result.json", "w", encoding="utf-8") as f:
        f.write(json_str)
    print("✓ Test result saved to basic_test_result.json")
    
except Exception as e:
    print(f"✗ JSON serialization failed: {e}")

# 测试文件操作
print("\n6. Testing file operations...")
try:
    test_file = "test_output.txt"
    with open(test_file, "w", encoding="utf-8") as f:
        f.write(f"Test completed at {datetime.now()}\n")
        f.write(f"Python version: {sys.version}\n")
        f.write(f"Current directory: {os.getcwd()}\n")
    
    # 读取验证
    with open(test_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    print("✓ File operations work")
    print(f"✓ Test file created: {test_file}")
    
    # 清理测试文件
    os.remove(test_file)
    print("✓ Test file cleaned up")
    
except Exception as e:
    print(f"✗ File operations failed: {e}")

print("\n=== Test Summary ===")
print("Basic functionality test completed")
print(f"Test time: {datetime.now()}")
print("If all tests show ✓, the Python environment is working correctly")
print("Next step: Try running the AI analyzer modules")

# 尝试导入项目模块（可选）
print("\n7. Optional: Testing project module imports...")
try:
    from ai.analysis.utils import generate_report_id
    test_id = generate_report_id()
    print(f"✓ Project utils import works: {test_id}")
except Exception as e:
    print(f"⚠ Project utils import failed: {e}")
    print("  This is expected if modules have issues, will be fixed next")

try:
    from ai.simple_analyzer import SimpleAIAnalyzer
    analyzer = SimpleAIAnalyzer()
    print("✓ SimpleAIAnalyzer import and init works")
except Exception as e:
    print(f"⚠ SimpleAIAnalyzer import failed: {e}")
    print("  This will be addressed in the next development phase")

print("\n=== End of Basic Test ===")
