#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生产环境配置模块

模块描述: 生产环境的配置，注重安全性、性能和稳定性
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .base, os
"""

import os
from .base import BaseConfig

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Production"

class ProductionConfig(BaseConfig):
    """生产环境配置"""
    
    def __init__(self):
        super().__init__()
        self.debug = False
        self.testing = False
        
    def load_environment_specific(self) -> None:
        """加载生产环境特定配置"""
        # 数据库配置 - 从环境变量读取
        self.database.host = os.getenv('DB_HOST', 'localhost')
        self.database.port = int(os.getenv('DB_PORT', '5432'))
        self.database.database = os.getenv('DB_NAME', 'zkteci')
        self.database.username = os.getenv('DB_USER', 'postgres')
        self.database.password = os.getenv('DB_PASSWORD', '123456')
        self.database.echo = False  # 生产环境不显示SQL
        self.database.pool_size = 20  # 生产环境更大的连接池
        self.database.max_overflow = 40
        self.database.connect_timeout = 10
        
        # AI服务配置 - 生产环境可能使用不同的服务
        self.ai.base_url = os.getenv('AI_BASE_URL', 'http://localhost:11434')
        self.ai.model_name = os.getenv('AI_MODEL_NAME', 'qwen2.5:7b')
        self.ai.embedding_model = os.getenv('AI_EMBEDDING_MODEL', 'nomic-embed-text')
        self.ai.timeout = int(os.getenv('AI_TIMEOUT', '300'))
        self.ai.max_retries = int(os.getenv('AI_MAX_RETRIES', '3'))
        self.ai.temperature = float(os.getenv('AI_TEMPERATURE', '0.7'))
        
        # UI配置 - 生产环境
        self.ui.theme = os.getenv('UI_THEME', 'light')
        self.ui.page_title = "AI驱动邮件周报分析系统"
        self.ui.layout = "wide"
        self.ui.sidebar_state = "expanded"
        
        # 缓存配置 - 生产Redis
        self.cache.redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.cache.default_ttl = int(os.getenv('CACHE_TTL', '3600'))
        self.cache.max_connections = int(os.getenv('REDIS_MAX_CONNECTIONS', '20'))
        self.cache.socket_timeout = int(os.getenv('REDIS_SOCKET_TIMEOUT', '5'))
        
        # 邮件配置 - 生产邮件服务
        self.email.host = os.getenv('EMAIL_HOST', 'smtp.example.com')
        self.email.port = int(os.getenv('EMAIL_PORT', '587'))
        self.email.username = os.getenv('EMAIL_USER', '')
        self.email.password = os.getenv('EMAIL_PASSWORD', '')
        self.email.use_tls = os.getenv('EMAIL_USE_TLS', 'true').lower() == 'true'
        self.email.use_ssl = os.getenv('EMAIL_USE_SSL', 'false').lower() == 'true'
        
        # 日志配置 - 生产环境
        self.logging.level = os.getenv('LOG_LEVEL', 'INFO')
        self.logging.console_output = True
        self.logging.file_output = True
        self.logging.log_dir = os.getenv('LOG_DIR', '/var/log/app')
        self.logging.max_file_size = int(os.getenv('LOG_MAX_SIZE', str(50 * 1024 * 1024)))  # 50MB
        self.logging.backup_count = int(os.getenv('LOG_BACKUP_COUNT', '60'))  # 60天
        
        # 安全配置 - 生产环境严格设置
        self.security.secret_key = os.getenv('SECRET_KEY', 'CHANGE-ME-IN-PRODUCTION')
        self.security.session_timeout = int(os.getenv('SESSION_TIMEOUT', '3600'))
        self.security.secure_cookies = True
        self.security.csrf_protection = True
        self.security.max_login_attempts = int(os.getenv('MAX_LOGIN_ATTEMPTS', '5'))
        self.security.lockout_duration = int(os.getenv('LOCKOUT_DURATION', '900'))
        
        # 生产环境特有配置
        self.ssl_required = True
        self.force_https = True
        self.enable_monitoring = True
        self.enable_metrics = True
        self.enable_health_checks = True
        
        # 性能配置
        self.max_request_size = int(os.getenv('MAX_REQUEST_SIZE', str(16 * 1024 * 1024)))  # 16MB
        self.request_timeout = int(os.getenv('REQUEST_TIMEOUT', '30'))
        self.worker_processes = int(os.getenv('WORKER_PROCESSES', '4'))
        self.worker_connections = int(os.getenv('WORKER_CONNECTIONS', '1000'))
        
        # 监控配置
        self.sentry_dsn = os.getenv('SENTRY_DSN', '')
        self.prometheus_port = int(os.getenv('PROMETHEUS_PORT', '9090'))
        self.health_check_port = int(os.getenv('HEALTH_CHECK_PORT', '8080'))
        
        # 备份配置
        self.backup_enabled = os.getenv('BACKUP_ENABLED', 'true').lower() == 'true'
        self.backup_schedule = os.getenv('BACKUP_SCHEDULE', '0 2 * * *')  # 每天凌晨2点
        self.backup_retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))
        
    def get_production_features(self) -> dict:
        """获取生产环境特有功能配置"""
        return {
            'ssl_required': self.ssl_required,
            'force_https': self.force_https,
            'enable_monitoring': self.enable_monitoring,
            'enable_metrics': self.enable_metrics,
            'enable_health_checks': self.enable_health_checks,
            'max_request_size': self.max_request_size,
            'request_timeout': self.request_timeout,
            'worker_processes': self.worker_processes,
            'worker_connections': self.worker_connections
        }
    
    def get_monitoring_config(self) -> dict:
        """获取监控配置"""
        return {
            'sentry_dsn': self.sentry_dsn,
            'prometheus_port': self.prometheus_port,
            'health_check_port': self.health_check_port,
            'enable_monitoring': self.enable_monitoring,
            'enable_metrics': self.enable_metrics
        }
    
    def get_backup_config(self) -> dict:
        """获取备份配置"""
        return {
            'enabled': self.backup_enabled,
            'schedule': self.backup_schedule,
            'retention_days': self.backup_retention_days
        }
    
    def validate_production_config(self) -> bool:
        """验证生产环境配置"""
        # 检查必需的环境变量
        required_vars = [
            'SECRET_KEY',
            'DB_PASSWORD',
            'EMAIL_PASSWORD'
        ]
        
        for var in required_vars:
            if not os.getenv(var):
                return False
        
        # 检查安全配置
        if self.security.secret_key == 'CHANGE-ME-IN-PRODUCTION':
            return False
        
        # 检查SSL配置
        if not self.ssl_required:
            return False
        
        return super().validate_config()
    
    def get_streamlit_config(self) -> dict:
        """获取Streamlit生产配置"""
        config = self.ui.get_streamlit_config()
        config.update({
            'server.runOnSave': False,
            'server.allowRunOnSave': False,
            'browser.gatherUsageStats': False,
            'global.developmentMode': False,
            'global.showWarningOnDirectExecution': True,
            'logger.level': 'info',
            'server.enableCORS': False,
            'server.enableXsrfProtection': True,
            'server.maxUploadSize': self.max_request_size // (1024 * 1024)  # MB
        })
        return config
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ProductionConfig(ssl_required={self.ssl_required}, monitoring={self.enable_monitoring})"
