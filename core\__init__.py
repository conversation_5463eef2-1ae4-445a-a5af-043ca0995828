#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心模块包
提供系统基础组件和通用功能

模块描述: 核心基础设施模块，提供所有其他模块的基础抽象类和接口
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: typing, abc, logging
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 延迟导入，避免循环依赖
def _get_exports():
    """获取模块导出"""
    try:
        from .base import BaseComponent, BaseService, BaseRepository
        from .interfaces import IAnalyzer, IVisualizer, IDataProcessor
        from .decorators import performance_monitor, cache_result, error_handler
        from .exceptions import CoreException, ValidationError, ProcessingError
        from .types import AnalysisResult, ComponentConfig, ServiceResponse

        return {
            'BaseComponent': BaseComponent,
            'BaseService': BaseService,
            'BaseRepository': BaseRepository,
            'IAnalyzer': IAnalyzer,
            'IVisualizer': IVisualizer,
            'IDataProcessor': IDataProcessor,
            'performance_monitor': performance_monitor,
            'cache_result': cache_result,
            'error_handler': error_handler,
            'CoreException': CoreException,
            'ValidationError': ValidationError,
            'ProcessingError': ProcessingError,
            'AnalysisResult': AnalysisResult,
            'ComponentConfig': ComponentConfig,
            'ServiceResponse': ServiceResponse
        }
    except ImportError as e:
        # 开发阶段可能出现的导入错误
        import logging
        logging.warning(f"核心模块导入警告: {e}")
        return {}

# 动态设置 __all__
_exports = _get_exports()
__all__ = list(_exports.keys())

# 动态添加到模块命名空间
globals().update(_exports)
