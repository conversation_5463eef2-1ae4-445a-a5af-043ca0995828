#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
趋势分析器

模块描述: 基于时间序列的趋势分析器，支持多种趋势检测和预测
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, domain.entities, numpy, scipy
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
from scipy import stats
from scipy.signal import find_peaks
import pandas as pd

from core.interfaces import IAnalyzer
from domain.entities import WeeklyReport, AnalysisResult, TaskComplexity
from domain.value_objects import ProcessingContext


class TrendAnalyzer(IAnalyzer):
    """
    趋势分析器
    
    功能：
    - 工作时长趋势分析
    - 任务复杂度趋势
    - 效率趋势分析
    - 周期性模式检测
    - 趋势预测
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化趋势分析器
        
        Args:
            config: 配置参数
        """
        self.name = "trend_analyzer"
        self.version = "1.0.0"
        self.config = config or {}
        
        # 趋势检测参数
        self.trend_threshold = self.config.get('trend_threshold', 0.1)  # 10%变化阈值
        self.min_data_points = self.config.get('min_data_points', 3)
        self.prediction_periods = self.config.get('prediction_periods', 2)
    
    def get_name(self) -> str:
        """获取分析器名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取描述"""
        return "趋势分析器，支持工作时长、复杂度、效率等多维度趋势分析和预测"
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['weekly_report', 'time_series_data', 'batch_reports']
    
    def analyze(self, data: WeeklyReport, context: ProcessingContext = None) -> AnalysisResult:
        """
        执行趋势分析
        
        Args:
            data: 周报数据
            context: 处理上下文
            
        Returns:
            AnalysisResult: 趋势分析结果
        """
        start_time = datetime.now()
        
        try:
            results = {}
            
            # 1. 当前周报基础指标
            current_metrics = self._extract_current_metrics(data)
            results['current_metrics'] = current_metrics
            
            # 2. 历史数据趋势分析（如果有历史数据）
            if context and hasattr(context, 'historical_data'):
                historical_trends = self._analyze_historical_trends(
                    data, context.historical_data
                )
                results['historical_trends'] = historical_trends
            
            # 3. 单周内趋势分析
            intra_week_trends = self._analyze_intra_week_trends(data)
            results['intra_week_trends'] = intra_week_trends
            
            # 4. 复杂度演进分析
            complexity_trends = self._analyze_complexity_trends(data)
            results['complexity_trends'] = complexity_trends
            
            # 5. 效率趋势分析
            efficiency_trends = self._analyze_efficiency_trends(data)
            results['efficiency_trends'] = efficiency_trends
            
            # 6. 周期性模式检测
            patterns = self._detect_patterns(data)
            results['patterns'] = patterns
            
            # 7. 趋势预测
            predictions = self._generate_predictions(results)
            results['predictions'] = predictions
            
            # 8. 趋势洞察
            insights = self._generate_trend_insights(results)
            results['insights'] = insights
            
            # 计算置信度
            confidence_score = self._calculate_confidence(results)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AnalysisResult(
                result_id=f"trend_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data=results,
                confidence_score=confidence_score,
                model_version=self.version,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            return AnalysisResult(
                result_id=f"trend_error_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data={'error': str(e)},
                confidence_score=0.0,
                model_version=self.version,
                processing_time=processing_time
            )
    
    def analyze_batch_trends(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """
        批量趋势分析
        
        Args:
            reports: 周报列表（按时间排序）
            
        Returns:
            Dict[str, Any]: 批量趋势分析结果
        """
        if len(reports) < self.min_data_points:
            return {'error': f'数据点不足，至少需要{self.min_data_points}个周报'}
        
        # 按时间排序
        sorted_reports = sorted(reports, key=lambda r: r.created_at)
        
        # 提取时间序列数据
        time_series = self._extract_time_series(sorted_reports)
        
        # 趋势分析
        trends = {}
        
        # 工作时长趋势
        if 'total_hours' in time_series:
            trends['hours_trend'] = self._analyze_metric_trend(
                time_series['total_hours'], 'total_hours'
            )
        
        # 任务数量趋势
        if 'task_count' in time_series:
            trends['task_count_trend'] = self._analyze_metric_trend(
                time_series['task_count'], 'task_count'
            )
        
        # 复杂度趋势
        if 'avg_complexity' in time_series:
            trends['complexity_trend'] = self._analyze_metric_trend(
                time_series['avg_complexity'], 'avg_complexity'
            )
        
        # 效率趋势
        if 'efficiency_score' in time_series:
            trends['efficiency_trend'] = self._analyze_metric_trend(
                time_series['efficiency_score'], 'efficiency_score'
            )
        
        # 整体趋势总结
        overall_trend = self._summarize_trends(trends)
        
        return {
            'time_series': time_series,
            'individual_trends': trends,
            'overall_trend': overall_trend,
            'data_points': len(sorted_reports),
            'time_span': {
                'start': sorted_reports[0].created_at.isoformat(),
                'end': sorted_reports[-1].created_at.isoformat()
            }
        }
    
    def _extract_current_metrics(self, report: WeeklyReport) -> Dict[str, Any]:
        """提取当前周报的基础指标"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        total_hours = sum(item.duration_hours for item in report.work_items)
        task_count = len(report.work_items)
        
        # 复杂度分布
        complexity_counts = {
            'low': sum(1 for item in report.work_items if item.complexity == TaskComplexity.LOW),
            'medium': sum(1 for item in report.work_items if item.complexity == TaskComplexity.MEDIUM),
            'high': sum(1 for item in report.work_items if item.complexity == TaskComplexity.HIGH)
        }
        
        # 平均复杂度分数
        complexity_scores = {
            TaskComplexity.LOW: 1,
            TaskComplexity.MEDIUM: 2,
            TaskComplexity.HIGH: 3
        }
        avg_complexity = sum(complexity_scores[item.complexity] for item in report.work_items) / task_count
        
        # 效率指标（简化计算）
        efficiency_score = self._calculate_efficiency_score(report.work_items)
        
        return {
            'total_hours': total_hours,
            'task_count': task_count,
            'avg_hours_per_task': total_hours / task_count if task_count > 0 else 0,
            'complexity_distribution': complexity_counts,
            'avg_complexity': avg_complexity,
            'efficiency_score': efficiency_score,
            'week': report.week,
            'timestamp': report.created_at.isoformat()
        }
    
    def _analyze_historical_trends(self, current_report: WeeklyReport, 
                                  historical_data: List[WeeklyReport]) -> Dict[str, Any]:
        """分析历史趋势"""
        # 将当前报告加入历史数据
        all_reports = historical_data + [current_report]
        return self.analyze_batch_trends(all_reports)
    
    def _analyze_intra_week_trends(self, report: WeeklyReport) -> Dict[str, Any]:
        """分析单周内的趋势"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        # 按任务顺序分析（假设任务按时间顺序排列）
        tasks_by_order = list(enumerate(report.work_items))
        
        if len(tasks_by_order) < 3:
            return {'note': '任务数量不足，无法分析周内趋势'}
        
        # 分析工时变化
        hours_sequence = [item.duration_hours for _, item in tasks_by_order]
        hours_trend = self._detect_sequence_trend(hours_sequence)
        
        # 分析复杂度变化
        complexity_sequence = [self._complexity_to_score(item.complexity) for _, item in tasks_by_order]
        complexity_trend = self._detect_sequence_trend(complexity_sequence)
        
        return {
            'hours_trend': hours_trend,
            'complexity_trend': complexity_trend,
            'task_sequence_length': len(tasks_by_order),
            'patterns': self._detect_intra_week_patterns(report.work_items)
        }
    
    def _analyze_complexity_trends(self, report: WeeklyReport) -> Dict[str, Any]:
        """分析复杂度趋势"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        complexity_distribution = {
            'low': 0, 'medium': 0, 'high': 0
        }
        
        complexity_hours = {
            'low': 0, 'medium': 0, 'high': 0
        }
        
        for item in report.work_items:
            complexity_key = item.complexity.value.lower()
            complexity_distribution[complexity_key] += 1
            complexity_hours[complexity_key] += item.duration_hours
        
        total_tasks = len(report.work_items)
        total_hours = sum(complexity_hours.values())
        
        # 计算复杂度指标
        complexity_metrics = {
            'distribution_percentage': {
                k: v / total_tasks * 100 for k, v in complexity_distribution.items()
            },
            'hours_percentage': {
                k: v / total_hours * 100 if total_hours > 0 else 0 
                for k, v in complexity_hours.items()
            },
            'avg_hours_by_complexity': {
                k: complexity_hours[k] / complexity_distribution[k] if complexity_distribution[k] > 0 else 0
                for k in complexity_distribution.keys()
            }
        }
        
        # 复杂度趋势洞察
        insights = []
        if complexity_metrics['distribution_percentage']['high'] > 40:
            insights.append("高复杂度任务占比较高")
        if complexity_metrics['hours_percentage']['high'] > 50:
            insights.append("高复杂度任务耗时占主导")
        
        return {
            'complexity_metrics': complexity_metrics,
            'insights': insights
        }
    
    def _analyze_efficiency_trends(self, report: WeeklyReport) -> Dict[str, Any]:
        """分析效率趋势"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        # 计算各种效率指标
        total_hours = sum(item.duration_hours for item in report.work_items)
        task_count = len(report.work_items)
        
        # 任务密度（任务数/总工时）
        task_density = task_count / total_hours if total_hours > 0 else 0
        
        # 高价值任务比例（高复杂度任务）
        high_value_tasks = sum(1 for item in report.work_items if item.complexity == TaskComplexity.HIGH)
        high_value_ratio = high_value_tasks / task_count if task_count > 0 else 0
        
        # 平均任务完成时间
        avg_task_time = total_hours / task_count if task_count > 0 else 0
        
        # 效率分数（综合指标）
        efficiency_score = self._calculate_efficiency_score(report.work_items)
        
        return {
            'task_density': task_density,
            'high_value_ratio': high_value_ratio,
            'avg_task_time': avg_task_time,
            'efficiency_score': efficiency_score,
            'efficiency_level': self._categorize_efficiency(efficiency_score)
        }
    
    def _detect_patterns(self, report: WeeklyReport) -> Dict[str, Any]:
        """检测周期性模式"""
        if not report.work_items:
            return {'error': '无工作项数据'}
        
        patterns = []
        
        # 检测任务时长模式
        durations = [item.duration_hours for item in report.work_items]
        if len(durations) >= 3:
            # 检测是否有重复的时长
            duration_counts = {}
            for duration in durations:
                duration_counts[duration] = duration_counts.get(duration, 0) + 1
            
            common_durations = [d for d, count in duration_counts.items() if count >= 2]
            if common_durations:
                patterns.append({
                    'type': 'duration_pattern',
                    'description': f'常见任务时长: {common_durations}小时',
                    'durations': common_durations
                })
        
        # 检测复杂度模式
        complexity_sequence = [item.complexity.value for item in report.work_items]
        if len(set(complexity_sequence)) == 1:
            patterns.append({
                'type': 'complexity_consistency',
                'description': f'所有任务复杂度一致: {complexity_sequence[0]}',
                'complexity': complexity_sequence[0]
            })
        
        return {
            'detected_patterns': patterns,
            'pattern_count': len(patterns)
        }
    
    def _generate_predictions(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成趋势预测"""
        predictions = {}
        
        # 基于当前指标预测下周趋势
        if 'current_metrics' in results:
            current = results['current_metrics']
            
            # 简单的线性预测
            predictions['next_week'] = {
                'predicted_hours': current.get('total_hours', 0) * 1.05,  # 假设5%增长
                'predicted_task_count': max(1, int(current.get('task_count', 0) * 0.95)),  # 假设任务数略减
                'confidence': 0.6
            }
        
        # 基于历史趋势预测
        if 'historical_trends' in results:
            historical = results['historical_trends']
            if 'individual_trends' in historical:
                trends = historical['individual_trends']
                
                # 基于趋势方向预测
                for metric, trend_data in trends.items():
                    if 'trend_direction' in trend_data:
                        direction = trend_data['trend_direction']
                        current_value = trend_data.get('current_value', 0)
                        
                        if direction == 'increasing':
                            predicted_value = current_value * 1.1
                        elif direction == 'decreasing':
                            predicted_value = current_value * 0.9
                        else:
                            predicted_value = current_value
                        
                        predictions[f'predicted_{metric}'] = {
                            'value': predicted_value,
                            'direction': direction,
                            'confidence': trend_data.get('confidence', 0.5)
                        }
        
        return predictions
    
    def _generate_trend_insights(self, results: Dict[str, Any]) -> List[str]:
        """生成趋势洞察"""
        insights = []
        
        # 当前指标洞察
        if 'current_metrics' in results:
            current = results['current_metrics']
            
            if current.get('total_hours', 0) > 45:
                insights.append("当前工作负载较重，建议关注工作平衡")
            
            if current.get('efficiency_score', 0) < 0.6:
                insights.append("当前效率偏低，建议优化任务安排")
            
            if current.get('avg_complexity', 0) > 2.5:
                insights.append("任务复杂度较高，需要更多专注时间")
        
        # 历史趋势洞察
        if 'historical_trends' in results:
            historical = results['historical_trends']
            if 'overall_trend' in historical:
                overall = historical['overall_trend']
                if overall.get('direction') == 'increasing':
                    insights.append("整体工作量呈上升趋势")
                elif overall.get('direction') == 'decreasing':
                    insights.append("整体工作量呈下降趋势")
        
        # 效率趋势洞察
        if 'efficiency_trends' in results:
            efficiency = results['efficiency_trends']
            if efficiency.get('efficiency_level') == 'high':
                insights.append("当前工作效率良好")
            elif efficiency.get('efficiency_level') == 'low':
                insights.append("工作效率有待提升")
        
        return insights
    
    def _extract_time_series(self, reports: List[WeeklyReport]) -> Dict[str, List]:
        """提取时间序列数据"""
        time_series = {
            'timestamps': [],
            'total_hours': [],
            'task_count': [],
            'avg_complexity': [],
            'efficiency_score': []
        }
        
        for report in reports:
            time_series['timestamps'].append(report.created_at)
            
            if report.work_items:
                total_hours = sum(item.duration_hours for item in report.work_items)
                task_count = len(report.work_items)
                avg_complexity = sum(self._complexity_to_score(item.complexity) for item in report.work_items) / task_count
                efficiency = self._calculate_efficiency_score(report.work_items)
            else:
                total_hours = task_count = avg_complexity = efficiency = 0
            
            time_series['total_hours'].append(total_hours)
            time_series['task_count'].append(task_count)
            time_series['avg_complexity'].append(avg_complexity)
            time_series['efficiency_score'].append(efficiency)
        
        return time_series
    
    def _analyze_metric_trend(self, values: List[float], metric_name: str) -> Dict[str, Any]:
        """分析单个指标的趋势"""
        if len(values) < 2:
            return {'error': '数据点不足'}
        
        # 线性回归分析趋势
        x = np.arange(len(values))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)
        
        # 趋势方向
        if abs(slope) < self.trend_threshold * np.mean(values):
            direction = 'stable'
        elif slope > 0:
            direction = 'increasing'
        else:
            direction = 'decreasing'
        
        # 趋势强度
        trend_strength = abs(r_value)
        
        return {
            'metric_name': metric_name,
            'trend_direction': direction,
            'trend_strength': trend_strength,
            'slope': slope,
            'r_squared': r_value ** 2,
            'p_value': p_value,
            'current_value': values[-1],
            'change_rate': (values[-1] - values[0]) / values[0] * 100 if values[0] != 0 else 0,
            'confidence': min(1.0, trend_strength)
        }
    
    def _summarize_trends(self, trends: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """总结整体趋势"""
        directions = [trend.get('trend_direction', 'stable') for trend in trends.values()]
        
        # 统计趋势方向
        direction_counts = {
            'increasing': directions.count('increasing'),
            'decreasing': directions.count('decreasing'),
            'stable': directions.count('stable')
        }
        
        # 确定主要趋势
        main_direction = max(direction_counts.items(), key=lambda x: x[1])[0]
        
        # 计算平均置信度
        confidences = [trend.get('confidence', 0) for trend in trends.values()]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return {
            'direction': main_direction,
            'direction_distribution': direction_counts,
            'confidence': avg_confidence,
            'trend_count': len(trends)
        }
    
    def _detect_sequence_trend(self, sequence: List[float]) -> Dict[str, Any]:
        """检测序列趋势"""
        if len(sequence) < 3:
            return {'trend': 'insufficient_data'}
        
        # 计算相邻差值
        diffs = [sequence[i+1] - sequence[i] for i in range(len(sequence)-1)]
        
        # 判断趋势
        positive_diffs = sum(1 for d in diffs if d > 0)
        negative_diffs = sum(1 for d in diffs if d < 0)
        
        if positive_diffs > negative_diffs:
            trend = 'increasing'
        elif negative_diffs > positive_diffs:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'positive_changes': positive_diffs,
            'negative_changes': negative_diffs,
            'total_change': sequence[-1] - sequence[0],
            'avg_change': np.mean(diffs)
        }
    
    def _detect_intra_week_patterns(self, work_items: List) -> List[str]:
        """检测周内模式"""
        patterns = []
        
        if len(work_items) >= 3:
            # 检测工时模式
            hours = [item.duration_hours for item in work_items]
            if hours[0] < hours[-1]:
                patterns.append("工作强度递增")
            elif hours[0] > hours[-1]:
                patterns.append("工作强度递减")
        
        return patterns
    
    def _complexity_to_score(self, complexity: TaskComplexity) -> float:
        """复杂度转分数"""
        mapping = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0
        }
        return mapping.get(complexity, 1.0)
    
    def _calculate_efficiency_score(self, work_items: List) -> float:
        """计算效率分数"""
        if not work_items:
            return 0.0
        
        total_hours = sum(item.duration_hours for item in work_items)
        high_complexity_hours = sum(
            item.duration_hours for item in work_items 
            if item.complexity == TaskComplexity.HIGH
        )
        
        # 效率 = 高复杂度任务时长占比 * 任务密度调整
        efficiency_ratio = high_complexity_hours / total_hours if total_hours > 0 else 0
        task_density = len(work_items) / total_hours if total_hours > 0 else 0
        
        # 综合效率分数
        efficiency_score = efficiency_ratio * 0.7 + min(1.0, task_density * 10) * 0.3
        
        return min(1.0, efficiency_score)
    
    def _categorize_efficiency(self, efficiency_score: float) -> str:
        """效率分类"""
        if efficiency_score >= 0.8:
            return 'high'
        elif efficiency_score >= 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_confidence(self, results: Dict[str, Any]) -> float:
        """计算整体置信度"""
        confidence_scores = []
        
        # 基于数据完整性
        if 'current_metrics' in results and 'error' not in results['current_metrics']:
            confidence_scores.append(0.8)
        
        # 基于历史趋势置信度
        if 'historical_trends' in results:
            historical = results['historical_trends']
            if 'overall_trend' in historical:
                confidence_scores.append(historical['overall_trend'].get('confidence', 0.5))
        
        # 基于模式检测
        if 'patterns' in results:
            pattern_count = results['patterns'].get('pattern_count', 0)
            pattern_confidence = min(1.0, pattern_count / 3)
            confidence_scores.append(pattern_confidence)
        
        # 默认置信度
        if not confidence_scores:
            confidence_scores.append(0.6)
        
        return sum(confidence_scores) / len(confidence_scores)
