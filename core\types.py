#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心类型定义模块

模块描述: 定义系统中使用的核心数据类型和结构
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: typing, dataclasses, datetime, enum
"""

from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 枚举类型定义
class ComponentStatus(Enum):
    """组件状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

class AnalysisType(Enum):
    """分析类型枚举"""
    ML_ANALYSIS = "ml_analysis"
    CLUSTERING = "clustering"
    TREND_ANALYSIS = "trend_analysis"
    ANOMALY_DETECTION = "anomaly_detection"
    SENTIMENT_ANALYSIS = "sentiment_analysis"

class ServiceLevel(Enum):
    """服务级别枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

# 数据类定义
@dataclass
class ComponentConfig:
    """组件配置数据类"""
    name: str
    version: str
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    service_level: ServiceLevel = ServiceLevel.MEDIUM
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            raise ValueError("组件名称不能为空")
        if not self.version:
            raise ValueError("组件版本不能为空")

@dataclass
class AnalysisResult:
    """分析结果数据类"""
    result_id: str
    analysis_type: AnalysisType
    result_data: Dict[str, Any]
    confidence_score: float
    model_version: str
    processing_time: float
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.result_id:
            raise ValueError("结果ID不能为空")
        if not (0.0 <= self.confidence_score <= 1.0):
            raise ValueError("置信度分数必须在0.0到1.0之间")
        if self.processing_time < 0:
            raise ValueError("处理时间不能为负数")
    
    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """判断是否高置信度"""
        return self.confidence_score >= threshold

@dataclass
class ServiceResponse:
    """服务响应数据类"""
    success: bool
    data: Any = None
    error_message: str = ""
    error_code: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def success_response(cls, data: Any = None, execution_time: float = 0.0) -> 'ServiceResponse':
        """创建成功响应"""
        return cls(
            success=True,
            data=data,
            execution_time=execution_time
        )
    
    @classmethod
    def error_response(cls, error_message: str, error_code: str = "", execution_time: float = 0.0) -> 'ServiceResponse':
        """创建错误响应"""
        return cls(
            success=False,
            error_message=error_message,
            error_code=error_code,
            execution_time=execution_time
        )

@dataclass
class ProcessingContext:
    """处理上下文数据类"""
    user_id: str
    department: str
    role: str
    preferences: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    
    def with_updated_timestamp(self) -> 'ProcessingContext':
        """创建更新时间戳的新实例"""
        return ProcessingContext(
            user_id=self.user_id,
            department=self.department,
            role=self.role,
            preferences=self.preferences.copy(),
            timestamp=datetime.now(),
            session_id=self.session_id,
            request_id=self.request_id
        )

@dataclass
class VisualizationConfig:
    """可视化配置数据类"""
    chart_type: str
    title: str
    width: int = 800
    height: int = 600
    color_scheme: str = "default"
    interactive: bool = True
    export_formats: List[str] = field(default_factory=lambda: ["png", "html"])
    theme: str = "light"
    
    def with_size(self, width: int, height: int) -> 'VisualizationConfig':
        """创建新尺寸的配置"""
        return VisualizationConfig(
            chart_type=self.chart_type,
            title=self.title,
            width=width,
            height=height,
            color_scheme=self.color_scheme,
            interactive=self.interactive,
            export_formats=self.export_formats.copy(),
            theme=self.theme
        )

@dataclass
class FilterCriteria:
    """筛选条件数据类"""
    department: Optional[str] = None
    role: Optional[str] = None
    week: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    date_range: Optional[tuple] = None
    
    def is_empty(self) -> bool:
        """判断是否为空筛选条件"""
        return all(value is None or (isinstance(value, list) and not value) 
                  for value in [self.department, self.role, self.week, self.tags, self.date_range])

# 类型别名
ComponentDict = Dict[str, Any]
ConfigDict = Dict[str, Any]
MetricsDict = Dict[str, Union[int, float, str]]
ResultDict = Dict[str, Any]

# 常量定义
DEFAULT_TIMEOUT = 30
MAX_RETRY_COUNT = 3
DEFAULT_CACHE_TTL = 3600
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
SUPPORTED_FILE_TYPES = ['.txt', '.csv', '.xlsx', '.pdf', '.json']
