#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据分析流程优化验证器
验证数据分析流程优化的实施效果
"""
import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)


class DataAnalysisOptimizerValidator:
    """数据分析流程优化验证器"""
    
    def __init__(self):
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "未知",
            "validations": {},
            "errors": [],
            "warnings": []
        }
        self.error_count = 0
        self.warning_count = 0
    
    def log_error(self, message: str, details: str = ""):
        """记录错误"""
        self.error_count += 1
        error_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["errors"].append(error_info)
        logger.error(f"错误: {message} - {details}")
    
    def log_warning(self, message: str, details: str = ""):
        """记录警告"""
        self.warning_count += 1
        warning_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["warnings"].append(warning_info)
        logger.warning(f"警告: {message} - {details}")
    
    def validate_enhanced_analyzer_files(self) -> Dict[str, Any]:
        """验证增强分析器文件"""
        logger.info("验证增强分析器文件...")
        
        required_files = [
            "ai/analysis/enhanced_analyzer.py",
            "ai/analysis/data_flow_optimizer.py",
            "ai/analysis/smart_scheduler.py",
            "ai/analysis/integrated_analyzer.py"
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                existing_files.append(file_path)
                # 检查文件内容
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if not content:
                            self.log_warning(f"分析器文件为空", file_path)
                        elif len(content) < 1000:
                            self.log_warning(f"分析器文件内容过少", f"{file_path}: {len(content)}字符")
                except Exception as e:
                    self.log_error(f"无法读取分析器文件", f"{file_path}: {e}")
            else:
                missing_files.append(file_path)
                self.log_error(f"缺少分析器文件", file_path)
        
        return {
            "status": "通过" if not missing_files else "失败",
            "existing_files": existing_files,
            "missing_files": missing_files,
            "file_coverage": f"{len(existing_files)}/{len(required_files)}"
        }
    
    def validate_analyzer_classes(self) -> Dict[str, Any]:
        """验证分析器类结构"""
        logger.info("验证分析器类结构...")
        
        class_checks = {
            "ai/analysis/enhanced_analyzer.py": [
                "class EnhancedDataAnalyzer",
                "class AnalysisCache",
                "class ModelPerformanceTracker",
                "class AnalysisQualityEvaluator",
                "def analyze",
                "def batch_analyze"
            ],
            "ai/analysis/data_flow_optimizer.py": [
                "class DataFlowOptimizer",
                "class DataFlowMetrics",
                "class DataQualityChecker",
                "def optimize_data_processing",
                "def check_data_quality"
            ],
            "ai/analysis/smart_scheduler.py": [
                "class SmartScheduler",
                "class AnalysisTask",
                "class TaskPriority",
                "class SystemMonitor",
                "def submit_task",
                "def start"
            ],
            "ai/analysis/integrated_analyzer.py": [
                "class IntegratedAnalyzer",
                "def analyze_single",
                "def analyze_batch",
                "def get_comprehensive_statistics"
            ]
        }
        
        class_errors = []
        valid_classes = []
        
        for file_path, required_items in class_checks.items():
            if not os.path.exists(file_path):
                class_errors.append(f"文件不存在: {file_path}")
                self.log_error("分析器文件缺失", file_path)
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                missing_items = []
                for item in required_items:
                    if item not in content:
                        missing_items.append(item)
                
                if missing_items:
                    error_msg = f"{file_path} 缺少: {', '.join(missing_items)}"
                    class_errors.append(error_msg)
                    self.log_error("分析器类结构不完整", error_msg)
                else:
                    valid_classes.append(file_path)
                    
            except Exception as e:
                error_msg = f"读取分析器文件失败 {file_path}: {e}"
                class_errors.append(error_msg)
                self.log_error("分析器文件读取失败", error_msg)
        
        return {
            "status": "通过" if not class_errors else "失败",
            "valid_classes": valid_classes,
            "class_errors": class_errors,
            "checked_files": len(class_checks),
            "valid_count": len(valid_classes)
        }
    
    def validate_api_integration(self) -> Dict[str, Any]:
        """验证API集成"""
        logger.info("验证API集成...")
        
        api_file = "api/endpoints/report.py"
        if not os.path.exists(api_file):
            self.log_error("API文件缺失", api_file)
            return {"status": "失败", "error": "API文件不存在"}
        
        try:
            with open(api_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查增强分析器导入
            required_imports = [
                "from ai.analysis.integrated_analyzer import IntegratedAnalyzer",
                "from ai.analysis.smart_scheduler import TaskPriority"
            ]
            
            # 检查增强分析端点
            required_endpoints = [
                "@router.post(\"/analyze/enhanced\")",
                "@router.post(\"/analyze/batch\")",
                "@router.get(\"/analyze/statistics\")",
                "@router.get(\"/analyze/performance\")",
                "@router.post(\"/analyze/optimize\")",
                "@router.delete(\"/analyze/cache\")"
            ]
            
            # 检查请求模型
            required_models = [
                "class EnhancedAnalyzeRequest",
                "class BatchAnalyzeRequest"
            ]
            
            missing_imports = [imp for imp in required_imports if imp not in content]
            missing_endpoints = [ep for ep in required_endpoints if ep not in content]
            missing_models = [model for model in required_models if model not in content]
            
            integration_issues = []
            if missing_imports:
                integration_issues.extend([f"缺少导入: {imp}" for imp in missing_imports])
            if missing_endpoints:
                integration_issues.extend([f"缺少端点: {ep}" for ep in missing_endpoints])
            if missing_models:
                integration_issues.extend([f"缺少模型: {model}" for model in missing_models])
            
            if integration_issues:
                for issue in integration_issues:
                    self.log_error("API集成问题", issue)
                
                return {
                    "status": "失败",
                    "integration_issues": integration_issues,
                    "missing_imports": len(missing_imports),
                    "missing_endpoints": len(missing_endpoints),
                    "missing_models": len(missing_models)
                }
            else:
                return {
                    "status": "通过",
                    "message": "API集成完整",
                    "imports_count": len(required_imports),
                    "endpoints_count": len(required_endpoints),
                    "models_count": len(required_models)
                }
                
        except Exception as e:
            error_msg = f"API集成验证异常: {e}"
            self.log_error("API集成验证失败", error_msg)
            return {"status": "失败", "error": error_msg}
    
    def validate_module_init(self) -> Dict[str, Any]:
        """验证模块初始化"""
        logger.info("验证模块初始化...")
        
        init_file = "ai/analysis/__init__.py"
        if not os.path.exists(init_file):
            self.log_error("模块初始化文件缺失", init_file)
            return {"status": "失败", "error": "模块初始化文件不存在"}
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查增强组件导入
            required_imports = [
                "from .enhanced_analyzer import",
                "from .data_flow_optimizer import",
                "from .smart_scheduler import",
                "from .integrated_analyzer import IntegratedAnalyzer"
            ]
            
            # 检查__all__导出
            required_exports = [
                "EnhancedDataAnalyzer",
                "DataFlowOptimizer",
                "SmartScheduler",
                "IntegratedAnalyzer"
            ]
            
            missing_imports = [imp for imp in required_imports if imp not in content]
            missing_exports = [exp for exp in required_exports if exp not in content]
            
            init_issues = []
            if missing_imports:
                init_issues.extend([f"缺少导入: {imp}" for imp in missing_imports])
            if missing_exports:
                init_issues.extend([f"缺少导出: {exp}" for exp in missing_exports])
            
            if init_issues:
                for issue in init_issues:
                    self.log_error("模块初始化问题", issue)
                
                return {
                    "status": "失败",
                    "init_issues": init_issues,
                    "missing_imports": len(missing_imports),
                    "missing_exports": len(missing_exports)
                }
            else:
                return {
                    "status": "通过",
                    "message": "模块初始化完整",
                    "imports_count": len(required_imports),
                    "exports_count": len(required_exports)
                }
                
        except Exception as e:
            error_msg = f"模块初始化验证异常: {e}"
            self.log_error("模块初始化验证失败", error_msg)
            return {"status": "失败", "error": error_msg}
    
    def validate_code_syntax(self) -> Dict[str, Any]:
        """验证代码语法"""
        logger.info("验证代码语法...")
        
        python_files = [
            "ai/analysis/enhanced_analyzer.py",
            "ai/analysis/data_flow_optimizer.py",
            "ai/analysis/smart_scheduler.py",
            "ai/analysis/integrated_analyzer.py"
        ]
        
        syntax_errors = []
        valid_files = []
        
        for file_path in python_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # 编译检查语法
                compile(code, file_path, 'exec')
                valid_files.append(file_path)
                
            except SyntaxError as e:
                error_msg = f"语法错误在 {file_path}:{e.lineno}: {e.msg}"
                syntax_errors.append(error_msg)
                self.log_error("Python语法错误", error_msg)
            except Exception as e:
                error_msg = f"文件读取错误 {file_path}: {e}"
                syntax_errors.append(error_msg)
                self.log_error("文件读取错误", error_msg)
        
        return {
            "status": "通过" if not syntax_errors else "失败",
            "valid_files": valid_files,
            "syntax_errors": syntax_errors,
            "checked_files": len(python_files),
            "valid_count": len(valid_files)
        }
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面验证"""
        logger.info("开始数据分析流程优化验证...")
        
        # 执行所有验证
        self.validation_results["validations"]["enhanced_analyzer_files"] = self.validate_enhanced_analyzer_files()
        self.validation_results["validations"]["analyzer_classes"] = self.validate_analyzer_classes()
        self.validation_results["validations"]["api_integration"] = self.validate_api_integration()
        self.validation_results["validations"]["module_init"] = self.validate_module_init()
        self.validation_results["validations"]["code_syntax"] = self.validate_code_syntax()
        
        # 计算总体状态
        failed_validations = [name for name, result in self.validation_results["validations"].items() 
                            if result.get("status") == "失败"]
        
        if not failed_validations and self.error_count == 0:
            self.validation_results["overall_status"] = "全部通过"
        elif self.error_count == 0:
            self.validation_results["overall_status"] = "部分通过"
        else:
            self.validation_results["overall_status"] = "验证失败"
        
        # 添加统计信息
        self.validation_results["summary"] = {
            "total_validations": len(self.validation_results["validations"]),
            "passed_validations": len([v for v in self.validation_results["validations"].values() if v.get("status") == "通过"]),
            "partial_validations": len([v for v in self.validation_results["validations"].values() if v.get("status") == "部分通过"]),
            "failed_validations": failed_validations,
            "error_count": self.error_count,
            "warning_count": self.warning_count
        }
        
        logger.info(f"数据分析流程优化验证完成，状态: {self.validation_results['overall_status']}")
        return self.validation_results
    
    def print_detailed_report(self, results: Dict[str, Any]):
        """打印详细报告"""
        print("\n" + "=" * 80)
        print("数据分析流程优化验证报告")
        print("=" * 80)
        print(f"验证时间: {results['timestamp']}")
        print(f"总体状态: {results['overall_status']}")
        print(f"错误数量: {results['summary']['error_count']}")
        print(f"警告数量: {results['summary']['warning_count']}")
        
        print(f"\n验证结果详情:")
        for validation_name, validation_result in results["validations"].items():
            status = validation_result.get("status", "未知")
            status_emoji = "✅" if status == "通过" else "⚠️" if status == "部分通过" else "❌"
            print(f"  {validation_name:25} : {status_emoji} {status}")
        
        # 显示优化成果
        print(f"\n数据分析流程优化成果:")
        print(f"  - 增强分析器文件: {results['validations']['enhanced_analyzer_files']['file_coverage']}")
        print(f"  - 分析器类结构: {results['validations']['analyzer_classes']['valid_count']}个类完整")
        print(f"  - API集成: {'完成' if results['validations']['api_integration']['status'] == '通过' else '未完成'}")
        print(f"  - 模块初始化: {'完成' if results['validations']['module_init']['status'] == '通过' else '未完成'}")
        print(f"  - 代码语法: {results['validations']['code_syntax']['valid_count']}个文件通过")
        
        # 显示错误详情
        if results["errors"]:
            print(f"\n❌ 错误详情 ({len(results['errors'])}项):")
            for i, error in enumerate(results["errors"], 1):
                print(f"  {i}. {error['message']}")
                if error['details']:
                    print(f"     详情: {error['details']}")
        
        # 显示警告详情
        if results["warnings"]:
            print(f"\n⚠️  警告详情 ({len(results['warnings'])}项):")
            for i, warning in enumerate(results["warnings"], 1):
                print(f"  {i}. {warning['message']}")
                if warning['details']:
                    print(f"     详情: {warning['details']}")
        
        print("\n" + "=" * 80)
        
        # 验证结果判断
        if results["overall_status"] == "全部通过":
            print("🎉 恭喜！数据分析流程优化验证全部通过！")
        elif results["overall_status"] == "部分通过":
            print("⚠️  数据分析流程优化部分通过，存在警告但无严重错误")
        else:
            print("❌ 数据分析流程优化验证失败！存在严重错误，需要立即修复！")
        
        print("=" * 80)


def main():
    """主函数"""
    validator = DataAnalysisOptimizerValidator()
    results = validator.run_comprehensive_validation()
    
    # 保存报告
    with open("data_analysis_optimizer_validation_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 打印报告
    validator.print_detailed_report(results)
    
    # 如果有错误，返回非零退出码
    if results["summary"]["error_count"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
