#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心装饰器模块

模块描述: 提供系统中使用的通用装饰器，包括性能监控、缓存、错误处理等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: functools, time, typing, logging
"""

import functools
import time
import logging
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime

from .exceptions import ProcessingError, TimeoutError, ExceptionHandler

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

def performance_monitor(
    log_execution_time: bool = True,
    log_memory_usage: bool = False,
    threshold_seconds: float = 1.0
):
    """
    性能监控装饰器
    
    自动记录函数执行时间和性能指标
    
    Args:
        log_execution_time: 是否记录执行时间
        log_memory_usage: 是否记录内存使用
        threshold_seconds: 性能警告阈值（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取logger
            logger = getattr(args[0], 'logger', None) if args else None
            if not logger:
                logger = logging.getLogger(func.__module__)
            
            start_time = time.time()
            start_memory = None
            
            # 记录内存使用（如果需要）
            if log_memory_usage:
                try:
                    import psutil
                    process = psutil.Process()
                    start_memory = process.memory_info().rss / 1024 / 1024  # MB
                except ImportError:
                    logger.warning("psutil未安装，无法监控内存使用")
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                # 记录性能指标
                if log_execution_time:
                    if execution_time > threshold_seconds:
                        logger.warning(
                            f"函数 {func.__name__} 执行时间过长: {execution_time:.3f}秒 "
                            f"(阈值: {threshold_seconds}秒)"
                        )
                    else:
                        logger.debug(f"函数 {func.__name__} 执行时间: {execution_time:.3f}秒")
                
                # 记录内存使用
                if log_memory_usage and start_memory is not None:
                    try:
                        import psutil
                        process = psutil.Process()
                        end_memory = process.memory_info().rss / 1024 / 1024  # MB
                        memory_diff = end_memory - start_memory
                        logger.debug(
                            f"函数 {func.__name__} 内存使用: "
                            f"开始={start_memory:.2f}MB, 结束={end_memory:.2f}MB, "
                            f"差异={memory_diff:.2f}MB"
                        )
                    except ImportError:
                        pass
                
                # 更新组件指标（如果是组件方法）
                if hasattr(args[0], 'update_metric'):
                    args[0].update_metric(f"{func.__name__}_last_execution_time", execution_time)
                    args[0].update_metric(f"{func.__name__}_call_count", 
                                        args[0].get_metric(f"{func.__name__}_call_count", 0) + 1)
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"函数 {func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {e}",
                    exc_info=True
                )
                raise
        
        return wrapper
    return decorator

def cache_result(
    ttl: int = 3600,
    cache_key_func: Optional[Callable] = None,
    use_instance_cache: bool = True
):
    """
    结果缓存装饰器
    
    自动缓存函数结果
    
    Args:
        ttl: 缓存生存时间（秒）
        cache_key_func: 自定义缓存键生成函数
        use_instance_cache: 是否使用实例缓存
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cache_manager = None
            
            # 优先使用实例的缓存管理器
            if use_instance_cache and args and hasattr(args[0], 'cache_manager'):
                cache_manager = args[0].cache_manager
            
            if cache_manager:
                try:
                    cached_result = cache_manager.get(cache_key)
                    if cached_result is not None:
                        logger = getattr(args[0], 'logger', logging.getLogger(func.__module__))
                        logger.debug(f"缓存命中: {cache_key}")
                        return cached_result
                except Exception as e:
                    logger = getattr(args[0], 'logger', logging.getLogger(func.__module__))
                    logger.warning(f"缓存读取失败: {e}")
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            
            if cache_manager:
                try:
                    cache_manager.set(cache_key, result, ttl)
                    logger = getattr(args[0], 'logger', logging.getLogger(func.__module__))
                    logger.debug(f"结果已缓存: {cache_key}")
                except Exception as e:
                    logger = getattr(args[0], 'logger', logging.getLogger(func.__module__))
                    logger.warning(f"缓存写入失败: {e}")
            
            return result
        
        return wrapper
    return decorator

def error_handler(
    default_return: Any = None,
    log_error: bool = True,
    reraise: bool = True,
    error_types: tuple = None
):
    """
    错误处理装饰器
    
    统一异常处理
    
    Args:
        default_return: 发生错误时的默认返回值
        log_error: 是否记录错误日志
        reraise: 是否重新抛出异常
        error_types: 要捕获的异常类型，None表示捕获所有异常
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 检查是否是要捕获的异常类型
                if error_types and not isinstance(e, error_types):
                    raise
                
                # 记录错误日志
                if log_error:
                    logger = getattr(args[0], 'logger', None) if args else None
                    if not logger:
                        logger = logging.getLogger(func.__module__)
                    
                    context = {
                        'function': func.__name__,
                        'module': func.__module__,
                        'args': str(args)[:200],  # 限制长度
                        'kwargs': str(kwargs)[:200]
                    }
                    
                    error_info = ExceptionHandler.handle_exception(e, context, logger)
                
                # 返回默认值或重新抛出异常
                if reraise:
                    if isinstance(e, ProcessingError):
                        raise
                    else:
                        raise ProcessingError(f"函数 {func.__name__} 执行失败: {e}", cause=e)
                else:
                    return default_return
        
        return wrapper
    return decorator

def timeout(seconds: float):
    """
    超时装饰器
    
    为函数执行设置超时限制
    
    Args:
        seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"函数 {func.__name__} 执行超时: {seconds}秒")
            
            # 设置超时信号
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(seconds))
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 恢复原始信号处理器
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator

def validate_input(validator: Callable[[Any], bool], error_message: str = "输入验证失败"):
    """
    输入验证装饰器
    
    自动验证函数输入
    
    Args:
        validator: 验证函数
        error_message: 验证失败时的错误消息
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 验证输入
            if not validator(*args, **kwargs):
                from .exceptions import ValidationError
                raise ValidationError(error_message)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    重试装饰器
    
    自动重试失败的函数调用
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
        exceptions: 要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # 最后一次尝试失败，抛出异常
                        break
                    
                    # 计算延迟时间
                    wait_time = delay * (backoff ** attempt)
                    
                    # 记录重试日志
                    logger = getattr(args[0], 'logger', None) if args else None
                    if not logger:
                        logger = logging.getLogger(func.__module__)
                    
                    logger.warning(
                        f"函数 {func.__name__} 第{attempt + 1}次尝试失败，"
                        f"{wait_time:.2f}秒后重试: {e}"
                    )
                    
                    time.sleep(wait_time)
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator

def deprecated(reason: str = "", version: str = ""):
    """
    废弃警告装饰器
    
    标记废弃的函数或方法
    
    Args:
        reason: 废弃原因
        version: 废弃版本
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import warnings
            
            message = f"函数 {func.__name__} 已废弃"
            if version:
                message += f" (自版本 {version})"
            if reason:
                message += f": {reason}"
            
            warnings.warn(message, DeprecationWarning, stacklevel=2)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
