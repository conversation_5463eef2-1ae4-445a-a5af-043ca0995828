#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本

验证基本模块导入和功能
"""

def test_basic_imports():
    """测试基本导入"""
    try:
        print("测试核心模块导入...")
        from core import BaseComponent, ComponentConfig
        print("✅ 核心模块导入成功")
        
        print("测试领域模块导入...")
        from domain import Employee, EmployeeLevel
        print("✅ 领域模块导入成功")
        
        print("测试配置模块导入...")
        from config import config
        print("✅ 配置模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_employee_creation():
    """测试员工创建"""
    try:
        from domain import Employee, EmployeeLevel
        
        employee = Employee(
            email="<EMAIL>",
            name="测试员工",
            department="技术部",
            role="开发工程师",
            level=EmployeeLevel.INTERMEDIATE
        )
        
        assert employee.email == "<EMAIL>"
        assert employee.name == "测试员工"
        print("✅ 员工创建测试通过")
        return True
    except Exception as e:
        print(f"❌ 员工创建测试失败: {e}")
        return False

def test_cache_service():
    """测试缓存服务"""
    try:
        from services.data.cache_service import CacheService
        from core import ComponentConfig
        
        config = ComponentConfig(name="cache_test", version="1.0.0")
        cache = CacheService(config)
        
        # 测试基本操作
        cache.set("test_key", "test_value", 60)
        value = cache.get("test_key")
        assert value == "test_value"
        
        print("✅ 缓存服务测试通过")
        return True
    except Exception as e:
        print(f"❌ 缓存服务测试失败: {e}")
        return False

def test_visualization():
    """测试可视化"""
    try:
        from services.visualization.plotly_visualizer import PlotlyVisualizer
        
        viz = PlotlyVisualizer()
        chart_types = viz.get_supported_chart_types()
        assert len(chart_types) > 0
        assert "line" in chart_types
        
        print("✅ 可视化测试通过")
        return True
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试...")
    
    tests = [
        test_basic_imports,
        test_employee_creation,
        test_cache_service,
        test_visualization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
