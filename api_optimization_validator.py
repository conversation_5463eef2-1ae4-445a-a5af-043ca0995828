#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API服务优化验证器
验证API服务优化的实施效果
"""
import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)


class APIOptimizationValidator:
    """API服务优化验证器"""
    
    def __init__(self):
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "未知",
            "validations": {},
            "errors": [],
            "warnings": []
        }
        self.error_count = 0
        self.warning_count = 0
    
    def log_error(self, message: str, details: str = ""):
        """记录错误"""
        self.error_count += 1
        error_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["errors"].append(error_info)
        logger.error(f"错误: {message} - {details}")
    
    def log_warning(self, message: str, details: str = ""):
        """记录警告"""
        self.warning_count += 1
        warning_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["warnings"].append(warning_info)
        logger.warning(f"警告: {message} - {details}")
    
    def validate_middleware_files(self) -> Dict[str, Any]:
        """验证中间件文件"""
        logger.info("验证中间件文件...")
        
        required_middleware_files = [
            "api/middleware/__init__.py",
            "api/middleware/cache.py",
            "api/middleware/rate_limit.py",
            "api/middleware/exception_handler.py",
            "api/middleware/performance.py"
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_middleware_files:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                existing_files.append(file_path)
                # 检查文件内容
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if not content:
                            self.log_warning(f"中间件文件为空", file_path)
                        elif len(content) < 100:
                            self.log_warning(f"中间件文件内容过少", f"{file_path}: {len(content)}字符")
                except Exception as e:
                    self.log_error(f"无法读取中间件文件", f"{file_path}: {e}")
            else:
                missing_files.append(file_path)
                self.log_error(f"缺少中间件文件", file_path)
        
        return {
            "status": "通过" if not missing_files else "失败",
            "existing_files": existing_files,
            "missing_files": missing_files,
            "file_coverage": f"{len(existing_files)}/{len(required_middleware_files)}"
        }
    
    def validate_middleware_imports(self) -> Dict[str, Any]:
        """验证中间件导入"""
        logger.info("验证中间件导入...")
        
        import_tests = [
            ("api.middleware.cache", "CacheMiddleware"),
            ("api.middleware.rate_limit", "RateLimitMiddleware"),
            ("api.middleware.exception_handler", "ExceptionHandlerMiddleware"),
            ("api.middleware.performance", "PerformanceMiddleware")
        ]
        
        import_errors = []
        successful_imports = []
        
        for module_name, class_name in import_tests:
            try:
                # 动态导入模块
                module = __import__(module_name, fromlist=[class_name])
                
                # 检查类是否存在
                if hasattr(module, class_name):
                    successful_imports.append(f"{module_name}.{class_name}")
                else:
                    error_msg = f"模块 {module_name} 中缺少 {class_name}"
                    import_errors.append(error_msg)
                    self.log_error("中间件导入验证失败", error_msg)
                    
            except ImportError as e:
                error_msg = f"无法导入 {module_name}: {e}"
                import_errors.append(error_msg)
                self.log_error("中间件导入失败", error_msg)
            except Exception as e:
                error_msg = f"导入测试异常 {module_name}: {e}"
                import_errors.append(error_msg)
                self.log_error("中间件导入测试异常", error_msg)
        
        return {
            "status": "通过" if not import_errors else "失败",
            "successful_imports": successful_imports,
            "import_errors": import_errors,
            "tested_modules": len(import_tests),
            "success_count": len(successful_imports)
        }
    
    def validate_cache_functionality(self) -> Dict[str, Any]:
        """验证缓存功能"""
        logger.info("验证缓存功能...")
        
        try:
            from api.middleware.cache import CacheManager, MemoryCache
            
            # 测试内存缓存
            cache = MemoryCache(max_size=10, default_ttl=60)
            
            # 测试设置和获取
            test_key = "test_key"
            test_value = {"test": "data", "number": 123}
            
            # 设置缓存
            set_result = cache.set(test_key, test_value, 60)
            if not set_result:
                self.log_error("缓存设置失败", "MemoryCache.set返回False")
                return {"status": "失败", "error": "缓存设置失败"}
            
            # 获取缓存
            cached_value = cache.get(test_key)
            if cached_value != test_value:
                self.log_error("缓存获取失败", f"期望: {test_value}, 实际: {cached_value}")
                return {"status": "失败", "error": "缓存获取失败"}
            
            # 测试缓存管理器
            cache_manager = CacheManager()
            manager_set = cache_manager.set("manager_test", {"data": "test"}, "query_results")
            manager_get = cache_manager.get("manager_test", "query_results")
            
            if not manager_set or manager_get != {"data": "test"}:
                self.log_error("缓存管理器测试失败", "设置或获取失败")
                return {"status": "失败", "error": "缓存管理器测试失败"}
            
            # 获取统计信息
            stats = cache_manager.get_stats()
            
            return {
                "status": "通过",
                "message": "缓存功能正常",
                "features": ["内存缓存", "缓存管理器", "统计信息"],
                "stats": stats
            }
            
        except Exception as e:
            error_msg = f"缓存功能验证异常: {e}"
            self.log_error("缓存功能验证失败", error_msg)
            return {"status": "失败", "error": error_msg, "traceback": traceback.format_exc()}
    
    def validate_rate_limit_functionality(self) -> Dict[str, Any]:
        """验证限流功能"""
        logger.info("验证限流功能...")
        
        try:
            from api.middleware.rate_limit import RateLimiter, TokenBucket, SlidingWindow
            
            # 测试令牌桶
            bucket = TokenBucket(capacity=5, refill_rate=1.0)
            
            # 消费令牌
            consume_results = []
            for i in range(7):  # 超过容量
                result = bucket.consume(1)
                consume_results.append(result)
            
            # 前5次应该成功，后2次应该失败
            expected_results = [True, True, True, True, True, False, False]
            if consume_results != expected_results:
                self.log_error("令牌桶测试失败", f"期望: {expected_results}, 实际: {consume_results}")
                return {"status": "失败", "error": "令牌桶测试失败"}
            
            # 测试滑动窗口
            window = SlidingWindow(window_size=60, max_requests=3)
            
            window_results = []
            for i in range(5):  # 超过限制
                result = window.is_allowed()
                window_results.append(result)
            
            # 前3次应该成功，后2次应该失败
            expected_window = [True, True, True, False, False]
            if window_results != expected_window:
                self.log_error("滑动窗口测试失败", f"期望: {expected_window}, 实际: {window_results}")
                return {"status": "失败", "error": "滑动窗口测试失败"}
            
            # 测试限流器
            rate_limiter = RateLimiter()
            
            # 测试全局限流
            global_check = rate_limiter.check_global_limit()
            if not isinstance(global_check, bool):
                self.log_error("全局限流检查失败", f"返回类型错误: {type(global_check)}")
                return {"status": "失败", "error": "全局限流检查失败"}
            
            # 获取统计信息
            stats = rate_limiter.get_stats()
            
            return {
                "status": "通过",
                "message": "限流功能正常",
                "features": ["令牌桶算法", "滑动窗口算法", "多级限流", "统计信息"],
                "stats": stats
            }
            
        except Exception as e:
            error_msg = f"限流功能验证异常: {e}"
            self.log_error("限流功能验证失败", error_msg)
            return {"status": "失败", "error": error_msg, "traceback": traceback.format_exc()}
    
    def validate_exception_handling(self) -> Dict[str, Any]:
        """验证异常处理功能"""
        logger.info("验证异常处理功能...")
        
        try:
            from api.middleware.exception_handler import (
                ExceptionHandler, BusinessException, DatabaseException, 
                AIAnalysisException, EmailFetchException, ErrorCode
            )
            
            # 测试异常处理器
            handler = ExceptionHandler()
            
            # 测试业务异常
            business_exc = BusinessException("测试业务异常", ErrorCode.BUSINESS_ERROR, {"test": "data"})
            if business_exc.code != ErrorCode.BUSINESS_ERROR:
                self.log_error("业务异常测试失败", f"错误码不匹配: {business_exc.code}")
                return {"status": "失败", "error": "业务异常测试失败"}
            
            # 测试数据库异常
            db_exc = DatabaseException("测试数据库异常")
            if db_exc.code != ErrorCode.DATABASE_ERROR:
                self.log_error("数据库异常测试失败", f"错误码不匹配: {db_exc.code}")
                return {"status": "失败", "error": "数据库异常测试失败"}
            
            # 测试AI分析异常
            ai_exc = AIAnalysisException("测试AI分析异常")
            if ai_exc.code != ErrorCode.AI_ANALYSIS_ERROR:
                self.log_error("AI分析异常测试失败", f"错误码不匹配: {ai_exc.code}")
                return {"status": "失败", "error": "AI分析异常测试失败"}
            
            # 测试邮件获取异常
            email_exc = EmailFetchException("测试邮件获取异常")
            if email_exc.code != ErrorCode.EMAIL_FETCH_ERROR:
                self.log_error("邮件获取异常测试失败", f"错误码不匹配: {email_exc.code}")
                return {"status": "失败", "error": "邮件获取异常测试失败"}
            
            # 获取错误统计
            stats = handler.get_error_stats()
            
            return {
                "status": "通过",
                "message": "异常处理功能正常",
                "features": ["业务异常", "数据库异常", "AI分析异常", "邮件获取异常", "错误统计"],
                "exception_types": 4,
                "stats": stats
            }
            
        except Exception as e:
            error_msg = f"异常处理功能验证异常: {e}"
            self.log_error("异常处理功能验证失败", error_msg)
            return {"status": "失败", "error": error_msg, "traceback": traceback.format_exc()}
    
    def validate_api_documentation(self) -> Dict[str, Any]:
        """验证API文档"""
        logger.info("验证API文档...")
        
        try:
            # 检查README文件
            readme_file = "api/README.md"
            
            if not os.path.exists(readme_file):
                self.log_error("API文档缺失", readme_file)
                return {"status": "失败", "error": "API README文档不存在"}
            
            with open(readme_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查文档内容
            required_sections = [
                '# API服务模块',
                '## 模块概述',
                '## 系统优化特性',
                '## 接口性能优化',
                '## 并发处理增强',
                '## 异常处理完善'
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
                    self.log_warning("API文档缺少章节", section)
            
            if len(content) < 1000:
                self.log_warning("API文档内容过短", f"{len(content)}字符")
            
            return {
                "status": "通过" if not missing_sections else "部分通过",
                "content_length": len(content),
                "missing_sections": missing_sections,
                "section_coverage": f"{len(required_sections) - len(missing_sections)}/{len(required_sections)}"
            }
            
        except Exception as e:
            error_msg = f"API文档验证异常: {e}"
            self.log_error("API文档验证失败", error_msg)
            return {"status": "失败", "error": error_msg}
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面验证"""
        logger.info("开始API服务优化验证...")
        
        # 执行所有验证
        self.validation_results["validations"]["middleware_files"] = self.validate_middleware_files()
        self.validation_results["validations"]["middleware_imports"] = self.validate_middleware_imports()
        self.validation_results["validations"]["cache_functionality"] = self.validate_cache_functionality()
        self.validation_results["validations"]["rate_limit_functionality"] = self.validate_rate_limit_functionality()
        self.validation_results["validations"]["exception_handling"] = self.validate_exception_handling()
        self.validation_results["validations"]["api_documentation"] = self.validate_api_documentation()
        
        # 计算总体状态
        failed_validations = [name for name, result in self.validation_results["validations"].items() 
                            if result.get("status") != "通过"]
        
        if not failed_validations and self.error_count == 0:
            self.validation_results["overall_status"] = "全部通过"
        elif self.error_count == 0:
            self.validation_results["overall_status"] = "部分通过"
        else:
            self.validation_results["overall_status"] = "验证失败"
        
        # 添加统计信息
        self.validation_results["summary"] = {
            "total_validations": len(self.validation_results["validations"]),
            "passed_validations": len(self.validation_results["validations"]) - len(failed_validations),
            "failed_validations": failed_validations,
            "error_count": self.error_count,
            "warning_count": self.warning_count
        }
        
        logger.info(f"API服务优化验证完成，状态: {self.validation_results['overall_status']}")
        return self.validation_results
    
    def generate_validation_report(self, output_file: str = "api_optimization_validation_report.json"):
        """生成验证报告"""
        results = self.run_comprehensive_validation()
        
        # 保存JSON报告
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 打印详细报告
        self.print_detailed_report(results)
        
        return results
    
    def print_detailed_report(self, results: Dict[str, Any]):
        """打印详细报告"""
        print("\n" + "=" * 80)
        print("API服务优化验证报告")
        print("=" * 80)
        print(f"验证时间: {results['timestamp']}")
        print(f"总体状态: {results['overall_status']}")
        print(f"错误数量: {results['summary']['error_count']}")
        print(f"警告数量: {results['summary']['warning_count']}")
        
        print(f"\n验证结果详情:")
        for validation_name, validation_result in results["validations"].items():
            status = validation_result.get("status", "未知")
            status_emoji = "✅" if status == "通过" else "⚠️" if status == "部分通过" else "❌"
            print(f"  {validation_name:25} : {status_emoji} {status}")
        
        # 显示错误详情
        if results["errors"]:
            print(f"\n❌ 错误详情 ({len(results['errors'])}项):")
            for i, error in enumerate(results["errors"], 1):
                print(f"  {i}. {error['message']}")
                if error['details']:
                    print(f"     详情: {error['details']}")
        
        # 显示警告详情
        if results["warnings"]:
            print(f"\n⚠️  警告详情 ({len(results['warnings'])}项):")
            for i, warning in enumerate(results["warnings"], 1):
                print(f"  {i}. {warning['message']}")
                if warning['details']:
                    print(f"     详情: {warning['details']}")
        
        print("\n" + "=" * 80)
        
        # 验证结果判断
        if results["overall_status"] == "全部通过":
            print("🎉 恭喜！API服务优化验证全部通过！")
        elif results["overall_status"] == "部分通过":
            print("⚠️  API服务优化部分通过，存在警告但无严重错误")
        else:
            print("❌ API服务优化验证失败！存在严重错误，需要立即修复！")
        
        print("=" * 80)


def main():
    """主函数"""
    validator = APIOptimizationValidator()
    results = validator.generate_validation_report()
    
    # 如果有错误，返回非零退出码
    if results["summary"]["error_count"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
