#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Plotly可视化器

模块描述: 基于Plotly的可视化器，支持多种交互式图表
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, plotly, pandas
"""

from typing import Dict, Any, List, Optional
import plotly.graph_objects as go
import plotly.express as px
import plotly.offline as pyo
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np

from core.interfaces import IVisualizer
from core.decorators import performance_monitor, error_handler
from domain.value_objects import VisualizationConfig


class PlotlyVisualizer(IVisualizer):
    """
    Plotly可视化器
    
    功能：
    - 线图、柱状图、饼图、散点图
    - 热力图、箱线图、小提琴图
    - 3D图表、地图可视化
    - 交互式仪表盘
    """
    
    def __init__(self):
        """初始化Plotly可视化器"""
        self.name = "plotly_visualizer"
        self.version = "1.0.0"
        
        # 支持的图表类型
        self.chart_types = {
            'line': self._create_line_chart,
            'bar': self._create_bar_chart,
            'pie': self._create_pie_chart,
            'scatter': self._create_scatter_chart,
            'heatmap': self._create_heatmap,
            'box': self._create_box_chart,
            'violin': self._create_violin_chart,
            'histogram': self._create_histogram,
            'area': self._create_area_chart,
            'bubble': self._create_bubble_chart,
            'treemap': self._create_treemap,
            'sunburst': self._create_sunburst,
            'radar': self._create_radar_chart,
            'gauge': self._create_gauge_chart,
            'waterfall': self._create_waterfall_chart
        }
        
        # 默认配置
        self.default_config = {
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d']
        }
    
    def get_name(self) -> str:
        """获取可视化器名称"""
        return self.name
    
    def get_supported_chart_types(self) -> List[str]:
        """获取支持的图表类型"""
        return list(self.chart_types.keys())
    
    def validate_data(self, data: Any, chart_type: str) -> bool:
        """验证数据是否适合指定的图表类型"""
        if not data:
            return False
        
        if chart_type not in self.chart_types:
            return False
        
        # 基本数据格式验证
        if isinstance(data, list) and len(data) > 0:
            return True
        elif isinstance(data, pd.DataFrame) and not data.empty:
            return True
        elif isinstance(data, dict) and data:
            return True
        
        return False
    
    @performance_monitor
    @error_handler(reraise=True)
    def create_visualization(self, data: Any, config: VisualizationConfig) -> go.Figure:
        """
        创建可视化
        
        Args:
            data: 要可视化的数据
            config: 可视化配置
            
        Returns:
            go.Figure: Plotly图表对象
        """
        chart_type = config.chart_type
        
        if chart_type not in self.chart_types:
            raise ValueError(f"不支持的图表类型: {chart_type}")
        
        # 数据预处理
        processed_data = self._preprocess_data(data, chart_type)
        
        # 创建图表
        chart_func = self.chart_types[chart_type]
        fig = chart_func(processed_data, config)
        
        # 应用通用配置
        self._apply_common_config(fig, config)
        
        return fig
    
    def export_chart(self, chart: go.Figure, format: str, filename: str) -> bool:
        """
        导出图表
        
        Args:
            chart: Plotly图表对象
            format: 导出格式 (html, png, pdf, svg, json)
            filename: 文件名
            
        Returns:
            bool: 导出是否成功
        """
        try:
            if format.lower() == 'html':
                chart.write_html(filename, config=self.default_config)
            elif format.lower() == 'png':
                chart.write_image(filename, format='png')
            elif format.lower() == 'pdf':
                chart.write_image(filename, format='pdf')
            elif format.lower() == 'svg':
                chart.write_image(filename, format='svg')
            elif format.lower() == 'json':
                chart.write_json(filename)
            else:
                return False
            
            return True
        except Exception:
            return False
    
    def _preprocess_data(self, data: Any, chart_type: str) -> pd.DataFrame:
        """预处理数据"""
        if isinstance(data, pd.DataFrame):
            return data
        elif isinstance(data, list):
            return pd.DataFrame(data)
        elif isinstance(data, dict):
            return pd.DataFrame([data])
        else:
            raise ValueError("不支持的数据格式")
    
    def _apply_common_config(self, fig: go.Figure, config: VisualizationConfig) -> None:
        """应用通用配置"""
        # 设置标题
        fig.update_layout(
            title={
                'text': config.title,
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            width=config.width,
            height=config.height,
            showlegend=config.show_legend,
            template='plotly_white' if config.theme == 'light' else 'plotly_dark'
        )
        
        # 设置网格
        if config.show_grid:
            fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
            fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
    
    def _create_line_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建线图"""
        fig = go.Figure()
        
        # 假设数据有x和y列
        if 'x' in data.columns and 'y' in data.columns:
            fig.add_trace(go.Scatter(
                x=data['x'],
                y=data['y'],
                mode='lines+markers',
                name='数据线'
            ))
        else:
            # 使用第一列作为x轴，其他数值列作为y轴
            x_col = data.columns[0]
            for col in data.select_dtypes(include=[np.number]).columns:
                if col != x_col:
                    fig.add_trace(go.Scatter(
                        x=data[x_col],
                        y=data[col],
                        mode='lines+markers',
                        name=col
                    ))
        
        return fig
    
    def _create_bar_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建柱状图"""
        fig = go.Figure()
        
        if 'x' in data.columns and 'y' in data.columns:
            fig.add_trace(go.Bar(
                x=data['x'],
                y=data['y'],
                name='数据'
            ))
        else:
            # 使用第一列作为x轴，其他数值列作为y轴
            x_col = data.columns[0]
            for col in data.select_dtypes(include=[np.number]).columns:
                if col != x_col:
                    fig.add_trace(go.Bar(
                        x=data[x_col],
                        y=data[col],
                        name=col
                    ))
        
        return fig
    
    def _create_pie_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建饼图"""
        fig = go.Figure()
        
        if 'labels' in data.columns and 'values' in data.columns:
            fig.add_trace(go.Pie(
                labels=data['labels'],
                values=data['values'],
                hole=0.3  # 甜甜圈图
            ))
        elif len(data.columns) >= 2:
            # 使用前两列
            labels_col = data.columns[0]
            values_col = data.columns[1]
            fig.add_trace(go.Pie(
                labels=data[labels_col],
                values=data[values_col],
                hole=0.3
            ))
        
        return fig
    
    def _create_scatter_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建散点图"""
        fig = go.Figure()
        
        if 'x' in data.columns and 'y' in data.columns:
            size_col = 'size' if 'size' in data.columns else None
            color_col = 'color' if 'color' in data.columns else None
            
            fig.add_trace(go.Scatter(
                x=data['x'],
                y=data['y'],
                mode='markers',
                marker=dict(
                    size=data[size_col] if size_col else 8,
                    color=data[color_col] if color_col else 'blue',
                    opacity=0.7
                ),
                name='数据点'
            ))
        
        return fig
    
    def _create_heatmap(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建热力图"""
        fig = go.Figure()
        
        # 如果数据是相关矩阵格式
        if data.index.equals(data.columns):
            fig.add_trace(go.Heatmap(
                z=data.values,
                x=data.columns,
                y=data.index,
                colorscale='Viridis'
            ))
        else:
            # 数值数据的热力图
            numeric_data = data.select_dtypes(include=[np.number])
            if not numeric_data.empty:
                fig.add_trace(go.Heatmap(
                    z=numeric_data.values,
                    x=numeric_data.columns,
                    y=list(range(len(numeric_data))),
                    colorscale='Viridis'
                ))
        
        return fig
    
    def _create_box_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建箱线图"""
        fig = go.Figure()
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            fig.add_trace(go.Box(
                y=data[col],
                name=col,
                boxpoints='outliers'
            ))
        
        return fig
    
    def _create_violin_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建小提琴图"""
        fig = go.Figure()
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            fig.add_trace(go.Violin(
                y=data[col],
                name=col,
                box_visible=True,
                meanline_visible=True
            ))
        
        return fig
    
    def _create_histogram(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建直方图"""
        fig = go.Figure()
        
        if 'values' in data.columns:
            fig.add_trace(go.Histogram(
                x=data['values'],
                nbinsx=30,
                name='分布'
            ))
        else:
            # 使用第一个数值列
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                fig.add_trace(go.Histogram(
                    x=data[numeric_cols[0]],
                    nbinsx=30,
                    name='分布'
                ))
        
        return fig
    
    def _create_area_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建面积图"""
        fig = go.Figure()
        
        if 'x' in data.columns and 'y' in data.columns:
            fig.add_trace(go.Scatter(
                x=data['x'],
                y=data['y'],
                fill='tonexty',
                mode='lines',
                name='面积'
            ))
        
        return fig
    
    def _create_bubble_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建气泡图"""
        fig = go.Figure()
        
        if all(col in data.columns for col in ['x', 'y', 'size']):
            fig.add_trace(go.Scatter(
                x=data['x'],
                y=data['y'],
                mode='markers',
                marker=dict(
                    size=data['size'],
                    sizemode='diameter',
                    sizeref=2.*max(data['size'])/(40.**2),
                    sizemin=4,
                    opacity=0.6
                ),
                name='气泡'
            ))
        
        return fig
    
    def _create_treemap(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建树状图"""
        fig = go.Figure()
        
        if 'labels' in data.columns and 'values' in data.columns:
            parents = data.get('parents', [''] * len(data))
            fig.add_trace(go.Treemap(
                labels=data['labels'],
                values=data['values'],
                parents=parents
            ))
        
        return fig
    
    def _create_sunburst(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建旭日图"""
        fig = go.Figure()
        
        if 'labels' in data.columns and 'values' in data.columns:
            parents = data.get('parents', [''] * len(data))
            fig.add_trace(go.Sunburst(
                labels=data['labels'],
                values=data['values'],
                parents=parents
            ))
        
        return fig
    
    def _create_radar_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建雷达图"""
        fig = go.Figure()
        
        if 'categories' in data.columns and 'values' in data.columns:
            fig.add_trace(go.Scatterpolar(
                r=data['values'],
                theta=data['categories'],
                fill='toself',
                name='雷达'
            ))
        
        return fig
    
    def _create_gauge_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建仪表盘图"""
        fig = go.Figure()
        
        if 'value' in data.columns:
            value = data['value'].iloc[0] if len(data) > 0 else 0
            fig.add_trace(go.Indicator(
                mode="gauge+number+delta",
                value=value,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "指标"},
                gauge={'axis': {'range': [None, 100]},
                      'bar': {'color': "darkblue"},
                      'steps': [
                          {'range': [0, 50], 'color': "lightgray"},
                          {'range': [50, 100], 'color': "gray"}],
                      'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 90}}
            ))
        
        return fig
    
    def _create_waterfall_chart(self, data: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """创建瀑布图"""
        fig = go.Figure()
        
        if 'x' in data.columns and 'y' in data.columns:
            fig.add_trace(go.Waterfall(
                name="瀑布图",
                orientation="v",
                measure=data.get('measure', ['relative'] * len(data)),
                x=data['x'],
                y=data['y'],
                connector={"line": {"color": "rgb(63, 63, 63)"}},
            ))
        
        return fig
