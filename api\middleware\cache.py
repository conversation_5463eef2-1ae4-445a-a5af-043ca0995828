#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存中间件
实现多级缓存策略，支持内存缓存和Redis缓存
"""
import json
import hashlib
import time
import logging
from typing import Dict, Any, Optional, Union
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import asyncio
from threading import Lock

logger = logging.getLogger(__name__)


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.lock = Lock()
    
    def _generate_key(self, key: str) -> str:
        """生成缓存键"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存是否过期"""
        if key not in self.cache:
            return True
        
        cache_data = self.cache[key]
        if cache_data['expires_at'] == -1:  # 永不过期
            return False
        
        return time.time() > cache_data['expires_at']
    
    def _evict_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for key, cache_data in self.cache.items():
            if cache_data['expires_at'] != -1 and current_time > cache_data['expires_at']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        if len(self.cache) < self.max_size:
            return
        
        # 找到最久未访问的键
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        with self.lock:
            cache_key = self._generate_key(key)
            
            if self._is_expired(cache_key):
                if cache_key in self.cache:
                    del self.cache[cache_key]
                    if cache_key in self.access_times:
                        del self.access_times[cache_key]
                return None
            
            if cache_key in self.cache:
                self.access_times[cache_key] = time.time()
                return self.cache[cache_key]['data']
            
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        with self.lock:
            try:
                cache_key = self._generate_key(key)
                
                # 清理过期缓存
                self._evict_expired()
                
                # LRU淘汰
                self._evict_lru()
                
                # 计算过期时间
                if ttl is None:
                    ttl = self.default_ttl
                
                expires_at = time.time() + ttl if ttl > 0 else -1
                
                # 存储缓存
                self.cache[cache_key] = {
                    'data': value,
                    'created_at': time.time(),
                    'expires_at': expires_at
                }
                self.access_times[cache_key] = time.time()
                
                return True
            except Exception as e:
                logger.error(f"设置缓存失败: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self.lock:
            cache_key = self._generate_key(key)
            
            if cache_key in self.cache:
                del self.cache[cache_key]
                if cache_key in self.access_times:
                    del self.access_times[cache_key]
                return True
            
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                'total_keys': len(self.cache),
                'max_size': self.max_size,
                'memory_usage': len(str(self.cache)),
                'hit_rate': 0  # 需要额外跟踪
            }


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.memory_cache = MemoryCache()
        self.redis_cache = None  # 可以扩展Redis缓存
        self.cache_config = {
            # 缓存策略配置
            'query_results': {'ttl': 1800, 'level': 'memory'},  # 查询结果缓存30分钟
            'analysis_results': {'ttl': 86400, 'level': 'memory'},  # 分析结果缓存24小时
            'statistics': {'ttl': 3600, 'level': 'memory'},  # 统计数据缓存1小时
            'config_data': {'ttl': -1, 'level': 'memory'},  # 配置数据永久缓存
        }
    
    def _get_cache_config(self, cache_type: str) -> Dict[str, Any]:
        """获取缓存配置"""
        return self.cache_config.get(cache_type, {'ttl': 300, 'level': 'memory'})
    
    def get(self, key: str, cache_type: str = 'default') -> Optional[Any]:
        """获取缓存"""
        config = self._get_cache_config(cache_type)
        
        if config['level'] == 'memory':
            return self.memory_cache.get(key)
        # 可以扩展Redis缓存
        return None
    
    def set(self, key: str, value: Any, cache_type: str = 'default') -> bool:
        """设置缓存"""
        config = self._get_cache_config(cache_type)
        
        if config['level'] == 'memory':
            return self.memory_cache.set(key, value, config['ttl'])
        # 可以扩展Redis缓存
        return False
    
    def delete(self, key: str, cache_type: str = 'default') -> bool:
        """删除缓存"""
        config = self._get_cache_config(cache_type)
        
        if config['level'] == 'memory':
            return self.memory_cache.delete(key)
        # 可以扩展Redis缓存
        return False
    
    def clear_by_type(self, cache_type: str):
        """按类型清空缓存"""
        # 简化实现，清空所有内存缓存
        self.memory_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'redis_cache': None  # 可以扩展
        }


# 全局缓存管理器实例
cache_manager = CacheManager()


class CacheMiddleware(BaseHTTPMiddleware):
    """缓存中间件"""
    
    def __init__(self, app, cache_enabled: bool = True):
        super().__init__(app)
        self.cache_enabled = cache_enabled
        self.cacheable_methods = {'GET'}
        self.cacheable_paths = {
            '/api/report/query',
            '/api/email/status',
            '/api/staff/list',
            '/api/tags/list',
            '/api/anomaly/list'
        }
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        # 包含路径、查询参数、用户信息等
        key_parts = [
            request.method,
            str(request.url.path),
            str(sorted(request.query_params.items())),
            # 可以添加用户ID等信息
        ]
        key_string = '|'.join(key_parts)
        return f"api_cache:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _is_cacheable(self, request: Request) -> bool:
        """判断请求是否可缓存"""
        if not self.cache_enabled:
            return False
        
        if request.method not in self.cacheable_methods:
            return False
        
        if request.url.path not in self.cacheable_paths:
            return False
        
        return True
    
    def _determine_cache_type(self, request: Request) -> str:
        """确定缓存类型"""
        path = request.url.path
        
        if '/query' in path:
            return 'query_results'
        elif '/status' in path:
            return 'statistics'
        elif '/list' in path:
            return 'config_data'
        else:
            return 'default'
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        # 检查是否可缓存
        if not self._is_cacheable(request):
            return await call_next(request)
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        cache_type = self._determine_cache_type(request)
        
        # 尝试从缓存获取
        cached_response = cache_manager.get(cache_key, cache_type)
        if cached_response:
            logger.debug(f"缓存命中: {cache_key}")
            return JSONResponse(
                content=cached_response,
                headers={"X-Cache": "HIT"}
            )
        
        # 缓存未命中，执行请求
        response = await call_next(request)
        
        # 缓存响应（仅缓存成功响应）
        if response.status_code == 200:
            try:
                # 读取响应内容
                response_body = b""
                async for chunk in response.body_iterator:
                    response_body += chunk
                
                # 解析JSON响应
                response_data = json.loads(response_body.decode())
                
                # 存储到缓存
                cache_manager.set(cache_key, response_data, cache_type)
                logger.debug(f"缓存存储: {cache_key}")
                
                # 重新创建响应
                return JSONResponse(
                    content=response_data,
                    status_code=response.status_code,
                    headers=dict(response.headers, **{"X-Cache": "MISS"})
                )
            except Exception as e:
                logger.error(f"缓存响应失败: {e}")
        
        return response
