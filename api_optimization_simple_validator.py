#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API服务优化简化验证器
验证API服务优化的文件结构和基本功能（不依赖FastAPI）
"""
import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)


class APIOptimizationSimpleValidator:
    """API服务优化简化验证器"""
    
    def __init__(self):
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "未知",
            "validations": {},
            "errors": [],
            "warnings": []
        }
        self.error_count = 0
        self.warning_count = 0
    
    def log_error(self, message: str, details: str = ""):
        """记录错误"""
        self.error_count += 1
        error_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["errors"].append(error_info)
        logger.error(f"错误: {message} - {details}")
    
    def log_warning(self, message: str, details: str = ""):
        """记录警告"""
        self.warning_count += 1
        warning_info = {
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.validation_results["warnings"].append(warning_info)
        logger.warning(f"警告: {message} - {details}")
    
    def validate_file_structure(self) -> Dict[str, Any]:
        """验证API优化文件结构"""
        logger.info("验证API优化文件结构...")
        
        required_files = [
            "api/README.md",
            "api/main.py",
            "api/middleware/__init__.py",
            "api/middleware/cache.py",
            "api/middleware/rate_limit.py",
            "api/middleware/exception_handler.py",
            "api/middleware/performance.py",
            "api/utils/__init__.py"
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                existing_files.append(file_path)
                # 检查文件大小
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size == 0:
                        self.log_warning(f"文件为空", file_path)
                    elif file_size < 100:
                        self.log_warning(f"文件过小", f"{file_path}: {file_size}字节")
                except Exception as e:
                    self.log_error(f"无法检查文件大小", f"{file_path}: {e}")
            else:
                missing_files.append(file_path)
                self.log_error(f"缺少必需文件", file_path)
        
        return {
            "status": "通过" if not missing_files else "失败",
            "existing_files": existing_files,
            "missing_files": missing_files,
            "file_coverage": f"{len(existing_files)}/{len(required_files)}"
        }
    
    def validate_code_syntax(self) -> Dict[str, Any]:
        """验证代码语法"""
        logger.info("验证代码语法...")
        
        python_files = [
            "api/main.py",
            "api/middleware/__init__.py",
            "api/middleware/cache.py",
            "api/middleware/rate_limit.py",
            "api/middleware/exception_handler.py",
            "api/middleware/performance.py",
            "api/utils/__init__.py"
        ]
        
        syntax_errors = []
        valid_files = []
        
        for file_path in python_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # 编译检查语法
                compile(code, file_path, 'exec')
                valid_files.append(file_path)
                
            except SyntaxError as e:
                error_msg = f"语法错误在 {file_path}:{e.lineno}: {e.msg}"
                syntax_errors.append(error_msg)
                self.log_error("Python语法错误", error_msg)
            except Exception as e:
                error_msg = f"文件读取错误 {file_path}: {e}"
                syntax_errors.append(error_msg)
                self.log_error("文件读取错误", error_msg)
        
        return {
            "status": "通过" if not syntax_errors else "失败",
            "valid_files": valid_files,
            "syntax_errors": syntax_errors,
            "checked_files": len(python_files),
            "valid_count": len(valid_files)
        }
    
    def validate_middleware_content(self) -> Dict[str, Any]:
        """验证中间件内容"""
        logger.info("验证中间件内容...")
        
        middleware_checks = {
            "api/middleware/cache.py": [
                "class CacheMiddleware",
                "class MemoryCache",
                "class CacheManager",
                "def get",
                "def set"
            ],
            "api/middleware/rate_limit.py": [
                "class RateLimitMiddleware",
                "class TokenBucket",
                "class SlidingWindow",
                "class RateLimiter",
                "def consume"
            ],
            "api/middleware/exception_handler.py": [
                "class ExceptionHandlerMiddleware",
                "class ExceptionHandler",
                "class BusinessException",
                "class ErrorCode",
                "def handle_http_exception"
            ],
            "api/middleware/performance.py": [
                "class PerformanceMiddleware",
                "class PerformanceMetrics",
                "def record_request",
                "def get_overall_stats",
                "def get_endpoint_stats"
            ]
        }
        
        content_errors = []
        valid_middleware = []
        
        for file_path, required_items in middleware_checks.items():
            if not os.path.exists(file_path):
                content_errors.append(f"文件不存在: {file_path}")
                self.log_error("中间件文件缺失", file_path)
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                missing_items = []
                for item in required_items:
                    if item not in content:
                        missing_items.append(item)
                
                if missing_items:
                    error_msg = f"{file_path} 缺少: {', '.join(missing_items)}"
                    content_errors.append(error_msg)
                    self.log_error("中间件内容不完整", error_msg)
                else:
                    valid_middleware.append(file_path)
                    
            except Exception as e:
                error_msg = f"读取中间件文件失败 {file_path}: {e}"
                content_errors.append(error_msg)
                self.log_error("中间件文件读取失败", error_msg)
        
        return {
            "status": "通过" if not content_errors else "失败",
            "valid_middleware": valid_middleware,
            "content_errors": content_errors,
            "checked_files": len(middleware_checks),
            "valid_count": len(valid_middleware)
        }
    
    def validate_api_main_integration(self) -> Dict[str, Any]:
        """验证API主文件集成"""
        logger.info("验证API主文件集成...")
        
        main_file = "api/main.py"
        if not os.path.exists(main_file):
            self.log_error("API主文件缺失", main_file)
            return {"status": "失败", "error": "API主文件不存在"}
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查中间件导入
            required_imports = [
                "from api.middleware.cache import",
                "from api.middleware.rate_limit import",
                "from api.middleware.exception_handler import",
                "from api.middleware.performance import"
            ]
            
            # 检查中间件添加
            required_middleware = [
                "app.add_middleware(PerformanceMiddleware",
                "app.add_middleware(RateLimitMiddleware",
                "app.add_middleware(CacheMiddleware",
                "app.add_middleware(ExceptionHandlerMiddleware"
            ]
            
            # 检查系统监控端点
            required_endpoints = [
                "@app.get(\"/api/system/health\"",
                "@app.get(\"/api/system/metrics\"",
                "@app.get(\"/api/system/cache/stats\"",
                "@app.post(\"/api/system/cache/clear\""
            ]
            
            missing_imports = [imp for imp in required_imports if imp not in content]
            missing_middleware = [mid for mid in required_middleware if mid not in content]
            missing_endpoints = [ep for ep in required_endpoints if ep not in content]
            
            integration_issues = []
            if missing_imports:
                integration_issues.extend([f"缺少导入: {imp}" for imp in missing_imports])
            if missing_middleware:
                integration_issues.extend([f"缺少中间件: {mid}" for mid in missing_middleware])
            if missing_endpoints:
                integration_issues.extend([f"缺少端点: {ep}" for ep in missing_endpoints])
            
            if integration_issues:
                for issue in integration_issues:
                    self.log_error("API主文件集成问题", issue)
                
                return {
                    "status": "失败",
                    "integration_issues": integration_issues,
                    "missing_imports": len(missing_imports),
                    "missing_middleware": len(missing_middleware),
                    "missing_endpoints": len(missing_endpoints)
                }
            else:
                return {
                    "status": "通过",
                    "message": "API主文件集成完整",
                    "imports_count": len(required_imports),
                    "middleware_count": len(required_middleware),
                    "endpoints_count": len(required_endpoints)
                }
                
        except Exception as e:
            error_msg = f"API主文件验证异常: {e}"
            self.log_error("API主文件验证失败", error_msg)
            return {"status": "失败", "error": error_msg}
    
    def validate_documentation(self) -> Dict[str, Any]:
        """验证文档完整性"""
        logger.info("验证文档完整性...")
        
        readme_file = "api/README.md"
        if not os.path.exists(readme_file):
            self.log_error("API文档缺失", readme_file)
            return {"status": "失败", "error": "API README文档不存在"}
        
        try:
            with open(readme_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查文档章节
            required_sections = [
                "# API服务模块",
                "## 系统优化特性",
                "### 接口性能优化",
                "### 并发处理增强",
                "### 异常处理完善",
                "## 缓存策略",
                "## 限流策略",
                "## 使用示例"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
                    self.log_warning("文档缺少章节", section)
            
            # 检查文档长度
            if len(content) < 2000:
                self.log_warning("文档内容较短", f"{len(content)}字符")
            
            return {
                "status": "通过" if not missing_sections else "部分通过",
                "content_length": len(content),
                "missing_sections": missing_sections,
                "section_coverage": f"{len(required_sections) - len(missing_sections)}/{len(required_sections)}"
            }
            
        except Exception as e:
            error_msg = f"文档验证异常: {e}"
            self.log_error("文档验证失败", error_msg)
            return {"status": "失败", "error": error_msg}
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面验证"""
        logger.info("开始API服务优化简化验证...")
        
        # 执行所有验证
        self.validation_results["validations"]["file_structure"] = self.validate_file_structure()
        self.validation_results["validations"]["code_syntax"] = self.validate_code_syntax()
        self.validation_results["validations"]["middleware_content"] = self.validate_middleware_content()
        self.validation_results["validations"]["api_main_integration"] = self.validate_api_main_integration()
        self.validation_results["validations"]["documentation"] = self.validate_documentation()
        
        # 计算总体状态
        failed_validations = [name for name, result in self.validation_results["validations"].items() 
                            if result.get("status") == "失败"]
        
        if not failed_validations and self.error_count == 0:
            self.validation_results["overall_status"] = "全部通过"
        elif self.error_count == 0:
            self.validation_results["overall_status"] = "部分通过"
        else:
            self.validation_results["overall_status"] = "验证失败"
        
        # 添加统计信息
        self.validation_results["summary"] = {
            "total_validations": len(self.validation_results["validations"]),
            "passed_validations": len([v for v in self.validation_results["validations"].values() if v.get("status") == "通过"]),
            "partial_validations": len([v for v in self.validation_results["validations"].values() if v.get("status") == "部分通过"]),
            "failed_validations": failed_validations,
            "error_count": self.error_count,
            "warning_count": self.warning_count
        }
        
        logger.info(f"API服务优化验证完成，状态: {self.validation_results['overall_status']}")
        return self.validation_results
    
    def print_detailed_report(self, results: Dict[str, Any]):
        """打印详细报告"""
        print("\n" + "=" * 80)
        print("API服务优化验证报告")
        print("=" * 80)
        print(f"验证时间: {results['timestamp']}")
        print(f"总体状态: {results['overall_status']}")
        print(f"错误数量: {results['summary']['error_count']}")
        print(f"警告数量: {results['summary']['warning_count']}")
        
        print(f"\n验证结果详情:")
        for validation_name, validation_result in results["validations"].items():
            status = validation_result.get("status", "未知")
            status_emoji = "✅" if status == "通过" else "⚠️" if status == "部分通过" else "❌"
            print(f"  {validation_name:25} : {status_emoji} {status}")
        
        # 显示优化成果
        print(f"\nAPI服务优化成果:")
        print(f"  - 中间件文件: {results['validations']['file_structure']['file_coverage']}")
        print(f"  - 代码语法: {results['validations']['code_syntax']['valid_count']}个文件通过")
        print(f"  - 中间件内容: {results['validations']['middleware_content']['valid_count']}个中间件完整")
        print(f"  - 主文件集成: {'完成' if results['validations']['api_main_integration']['status'] == '通过' else '未完成'}")
        print(f"  - 文档完整性: {results['validations']['documentation']['section_coverage']}")
        
        # 显示错误详情
        if results["errors"]:
            print(f"\n❌ 错误详情 ({len(results['errors'])}项):")
            for i, error in enumerate(results["errors"], 1):
                print(f"  {i}. {error['message']}")
                if error['details']:
                    print(f"     详情: {error['details']}")
        
        # 显示警告详情
        if results["warnings"]:
            print(f"\n⚠️  警告详情 ({len(results['warnings'])}项):")
            for i, warning in enumerate(results["warnings"], 1):
                print(f"  {i}. {warning['message']}")
                if warning['details']:
                    print(f"     详情: {warning['details']}")
        
        print("\n" + "=" * 80)
        
        # 验证结果判断
        if results["overall_status"] == "全部通过":
            print("🎉 恭喜！API服务优化验证全部通过！")
        elif results["overall_status"] == "部分通过":
            print("⚠️  API服务优化部分通过，存在警告但无严重错误")
        else:
            print("❌ API服务优化验证失败！存在严重错误，需要立即修复！")
        
        print("=" * 80)


def main():
    """主函数"""
    validator = APIOptimizationSimpleValidator()
    results = validator.run_comprehensive_validation()
    
    # 保存报告
    with open("api_optimization_simple_validation_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 打印报告
    validator.print_detailed_report(results)
    
    # 如果有错误，返回非零退出码
    if results["summary"]["error_count"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
