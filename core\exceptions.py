#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心异常定义模块

模块描述: 定义系统中使用的异常类型和错误处理机制
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: typing, datetime
"""

from typing import Dict, Any, Optional
from datetime import datetime

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

class CoreException(Exception):
    """核心异常基类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = None, 
        details: Dict[str, Any] = None,
        cause: Exception = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'cause': str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"[{self.error_code}] {self.message}"

class ConfigurationError(CoreException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if config_key:
            self.details['config_key'] = config_key

class ValidationError(CoreException):
    """数据验证错误异常"""
    
    def __init__(self, message: str, field_name: str = None, field_value: Any = None, **kwargs):
        super().__init__(message, **kwargs)
        if field_name:
            self.details['field_name'] = field_name
        if field_value is not None:
            self.details['field_value'] = str(field_value)

class ProcessingError(CoreException):
    """处理错误异常"""
    
    def __init__(self, message: str, operation: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if operation:
            self.details['operation'] = operation

class ComponentInitializationError(CoreException):
    """组件初始化错误异常"""
    
    def __init__(self, message: str, component_name: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if component_name:
            self.details['component_name'] = component_name

class ServiceUnavailableError(CoreException):
    """服务不可用错误异常"""
    
    def __init__(self, message: str, service_name: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if service_name:
            self.details['service_name'] = service_name

class AnalysisError(CoreException):
    """分析错误异常"""
    
    def __init__(self, message: str, analysis_type: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if analysis_type:
            self.details['analysis_type'] = analysis_type

class VisualizationError(CoreException):
    """可视化错误异常"""
    
    def __init__(self, message: str, chart_type: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if chart_type:
            self.details['chart_type'] = chart_type

class DataAccessError(CoreException):
    """数据访问错误异常"""
    
    def __init__(self, message: str, table_name: str = None, operation: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if table_name:
            self.details['table_name'] = table_name
        if operation:
            self.details['operation'] = operation

class CacheError(CoreException):
    """缓存错误异常"""
    
    def __init__(self, message: str, cache_key: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if cache_key:
            self.details['cache_key'] = cache_key

class AuthenticationError(CoreException):
    """认证错误异常"""
    
    def __init__(self, message: str, user_id: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if user_id:
            self.details['user_id'] = user_id

class AuthorizationError(CoreException):
    """授权错误异常"""
    
    def __init__(self, message: str, user_id: str = None, resource: str = None, **kwargs):
        super().__init__(message, **kwargs)
        if user_id:
            self.details['user_id'] = user_id
        if resource:
            self.details['resource'] = resource

class TimeoutError(CoreException):
    """超时错误异常"""
    
    def __init__(self, message: str, timeout_seconds: float = None, **kwargs):
        super().__init__(message, **kwargs)
        if timeout_seconds:
            self.details['timeout_seconds'] = timeout_seconds

class RateLimitError(CoreException):
    """速率限制错误异常"""
    
    def __init__(self, message: str, limit: int = None, window: int = None, **kwargs):
        super().__init__(message, **kwargs)
        if limit:
            self.details['limit'] = limit
        if window:
            self.details['window'] = window

class ExceptionHandler:
    """统一异常处理器"""
    
    @staticmethod
    def handle_exception(
        e: Exception, 
        context: Dict[str, Any] = None,
        logger = None
    ) -> Dict[str, Any]:
        """
        处理异常并返回标准化错误信息
        
        Args:
            e: 异常对象
            context: 上下文信息
            logger: 日志记录器
            
        Returns:
            Dict[str, Any]: 标准化错误信息
        """
        error_info = {
            'error_type': type(e).__name__,
            'message': str(e),
            'timestamp': datetime.now().isoformat(),
            'context': context or {}
        }
        
        if isinstance(e, CoreException):
            error_info.update({
                'error_code': e.error_code,
                'details': e.details,
                'cause': str(e.cause) if e.cause else None
            })
        
        # 记录日志
        if logger:
            logger.error(f"异常处理: {error_info}", exc_info=True)
        
        return error_info
    
    @staticmethod
    def is_retryable_error(e: Exception) -> bool:
        """
        判断异常是否可重试
        
        Args:
            e: 异常对象
            
        Returns:
            bool: 是否可重试
        """
        retryable_errors = (
            ServiceUnavailableError,
            TimeoutError,
            ConnectionError,
            CacheError
        )
        
        return isinstance(e, retryable_errors)
    
    @staticmethod
    def get_error_severity(e: Exception) -> str:
        """
        获取错误严重程度
        
        Args:
            e: 异常对象
            
        Returns:
            str: 错误严重程度 (critical, high, medium, low)
        """
        critical_errors = (
            ComponentInitializationError,
            DataAccessError,
            AuthenticationError
        )
        
        high_errors = (
            ServiceUnavailableError,
            AnalysisError,
            ProcessingError
        )
        
        medium_errors = (
            ValidationError,
            ConfigurationError,
            VisualizationError
        )
        
        if isinstance(e, critical_errors):
            return "critical"
        elif isinstance(e, high_errors):
            return "high"
        elif isinstance(e, medium_errors):
            return "medium"
        else:
            return "low"
