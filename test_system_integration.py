#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统集成测试
验证AI分析、数据处理、前端集成等核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append('.')

def test_ai_analyzer():
    """测试AI分析器"""
    print("=" * 50)
    print("测试AI分析器")
    print("=" * 50)
    
    try:
        from ai.simple_analyzer import SimpleAIAnalyzer
        
        analyzer = SimpleAIAnalyzer()
        print("✅ AI分析器初始化成功")
        
        # 测试数据
        test_cases = [
            {
                "name": "技术支持工程师周报",
                "content": """
                本周主要工作：
                1. 处理客户A的技术问题，耗时4小时，问题已解决
                2. 参与产品培训，耗时2小时，学习新功能
                3. 编写技术文档，耗时3小时，完成用户手册
                4. 客户现场支持，耗时8小时，解决系统集成问题
                5. 团队会议，耗时1小时，讨论下周计划
                总计工时：18小时
                """,
                "department": "技术支持部",
                "role": "技术支持工程师",
                "email": "<EMAIL>",
                "name": "李技术"
            },
            {
                "name": "开发工程师周报",
                "content": """
                本周开发任务：
                1. 新功能模块开发，耗时12小时，完成度80%
                2. 代码审查和优化，耗时4小时
                3. 单元测试编写，耗时6小时
                4. 技术调研，耗时3小时，研究新技术方案
                5. Bug修复，耗时3小时，解决5个问题
                总工时：28小时
                """,
                "department": "研发部",
                "role": "开发工程师",
                "email": "<EMAIL>",
                "name": "张开发"
            }
        ]
        
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试案例 {i}: {test_case['name']} ---")
            
            result = analyzer.analyze(
                test_case["content"],
                department=test_case["department"],
                role=test_case["role"],
                employee_email=test_case["email"],
                employee_name=test_case["name"]
            )
            
            print(f"✅ 分析完成")
            print(f"   报告ID: {result.get('report_id')}")
            print(f"   员工: {result.get('employee', {}).get('name')}")
            print(f"   工作项数量: {len(result.get('work_items', []))}")
            print(f"   总工时: {result.get('metrics', {}).get('total_hours')}小时")
            print(f"   饱和度: {result.get('metrics', {}).get('saturation_tag')}")
            print(f"   标签: {', '.join(result.get('tags', []))}")
            print(f"   异常: {', '.join(result.get('anomaly_flags', []))}")
            
            results.append(result)
        
        print(f"\n✅ AI分析器测试完成，共分析 {len(results)} 个案例")
        return results
        
    except Exception as e:
        print(f"❌ AI分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_data_processing():
    """测试数据处理功能"""
    print("\n" + "=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    try:
        from ai.analysis.utils import (
            generate_report_id, 
            create_default_analysis,
            normalize_email,
            calculate_saturation_tag,
            merge_tags
        )
        
        # 测试工具函数
        print("测试工具函数...")
        
        # 测试报告ID生成
        report_id = generate_report_id()
        print(f"✅ 报告ID生成: {report_id}")
        
        # 测试邮箱标准化
        emails = ["<EMAIL>", "  <EMAIL>  ", ""]
        for email in emails:
            normalized = normalize_email(email)
            print(f"✅ 邮箱标准化: '{email}' -> '{normalized}'")
        
        # 测试饱和度标签
        saturations = [0.3, 0.7, 0.9, 1.3]
        for sat in saturations:
            tag = calculate_saturation_tag(sat)
            print(f"✅ 饱和度标签: {sat} -> {tag}")
        
        # 测试标签合并
        tag_lists = [["技术", "开发"], ["技术", "测试"], ["创新"]]
        merged = merge_tags(tag_lists)
        print(f"✅ 标签合并: {tag_lists} -> {merged}")
        
        # 测试默认分析创建
        default_analysis = create_default_analysis({
            "name": "测试用户",
            "email": "<EMAIL>",
            "department": "测试部",
            "role": "测试工程师"
        })
        print(f"✅ 默认分析创建: {default_analysis.get('report_id')}")
        
        print("✅ 数据处理功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据处理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n" + "=" * 50)
    print("测试系统集成")
    print("=" * 50)
    
    try:
        # 模拟完整的分析流程
        print("模拟完整分析流程...")
        
        # 1. AI分析
        analysis_results = test_ai_analyzer()
        if not analysis_results:
            print("❌ AI分析失败，跳过后续测试")
            return False
        
        # 2. 数据处理
        data_processing_ok = test_data_processing()
        if not data_processing_ok:
            print("❌ 数据处理失败")
            return False
        
        # 3. 数据统计和分析
        print("\n进行数据统计分析...")
        
        total_reports = len(analysis_results)
        total_work_items = sum(len(r.get('work_items', [])) for r in analysis_results)
        total_hours = sum(r.get('metrics', {}).get('total_hours', 0) for r in analysis_results)
        
        # 部门统计
        departments = {}
        for result in analysis_results:
            dept = result.get('employee', {}).get('department', '未知')
            if dept not in departments:
                departments[dept] = {'count': 0, 'hours': 0}
            departments[dept]['count'] += 1
            departments[dept]['hours'] += result.get('metrics', {}).get('total_hours', 0)
        
        # 标签统计
        all_tags = []
        for result in analysis_results:
            all_tags.extend(result.get('tags', []))
        
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        print(f"✅ 统计结果:")
        print(f"   总报告数: {total_reports}")
        print(f"   总工作项: {total_work_items}")
        print(f"   总工时: {total_hours}小时")
        print(f"   部门分布: {departments}")
        print(f"   热门标签: {sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:5]}")
        
        # 4. 生成测试报告
        test_report = {
            "test_time": datetime.now().isoformat(),
            "test_results": {
                "ai_analyzer": "通过",
                "data_processing": "通过",
                "system_integration": "通过"
            },
            "statistics": {
                "total_reports": total_reports,
                "total_work_items": total_work_items,
                "total_hours": total_hours,
                "departments": departments,
                "top_tags": dict(sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10])
            },
            "sample_results": analysis_results
        }
        
        # 保存测试报告
        with open("system_integration_test_report.json", "w", encoding="utf-8") as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 测试报告已保存到: system_integration_test_report.json")
        print("✅ 系统集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始系统集成测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    success = test_system_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 系统集成测试全部通过！")
        print("✅ AI分析引擎正常工作")
        print("✅ 数据处理功能正常")
        print("✅ 系统集成功能正常")
        print("\n📋 下一步工作建议:")
        print("1. 完善前端UI界面")
        print("2. 集成数据库存储")
        print("3. 添加API服务接口")
        print("4. 实现邮件下载功能")
        print("5. 优化AI分析算法")
    else:
        print("❌ 系统集成测试失败")
        print("请检查错误信息并修复问题")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
