#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版前端应用
集成AI分析功能的完整前端界面
"""

import streamlit as st
import pandas as pd
import json
import sys
import os
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入AI分析器
try:
    from ai.simple_analyzer import SimpleAIAnalyzer
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

# 页面配置
st.set_page_config(
    page_title="AI驱动邮件周报分析系统",
    page_icon="📧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1E88E5;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1E88E5;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #c3e6cb;
    }
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #ffeaa7;
    }
</style>
""", unsafe_allow_html=True)

# 主标题
st.markdown('<h1 class="main-header">📧 AI驱动邮件周报分析系统</h1>', unsafe_allow_html=True)

# 侧边栏导航
st.sidebar.title("🧭 导航菜单")
page = st.sidebar.radio(
    "选择功能页面",
    [
        "📊 系统概览",
        "📝 周报分析",
        "📋 任务明细",
        "🏷️ 智能标签",
        "⚠️ 异常洞察",
        "📈 趋势分析",
        "👥 员工管理",
        "📧 邮件下载",
        "⚙️ 系统设置"
    ]
)

# 初始化AI分析器
if AI_AVAILABLE:
    if 'analyzer' not in st.session_state:
        st.session_state.analyzer = SimpleAIAnalyzer()

# 初始化数据存储
if 'reports' not in st.session_state:
    st.session_state.reports = []

if 'analysis_history' not in st.session_state:
    st.session_state.analysis_history = []

# 页面路由
if page == "📊 系统概览":
    st.header("📊 系统概览")
    
    # 系统状态指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📧 总邮件数",
            value=len(st.session_state.reports),
            delta=f"+{len([r for r in st.session_state.reports if r.get('created_today', False)])}"
        )
    
    with col2:
        analyzed_count = len([r for r in st.session_state.reports if r.get('ai_analyzed', False)])
        st.metric(
            label="🤖 已分析",
            value=analyzed_count,
            delta=f"{analyzed_count/max(len(st.session_state.reports), 1)*100:.1f}%"
        )
    
    with col3:
        st.metric(
            label="⚡ AI状态",
            value="正常" if AI_AVAILABLE else "离线",
            delta="简化模式" if AI_AVAILABLE else "不可用"
        )
    
    with col4:
        st.metric(
            label="🕒 最后更新",
            value=datetime.now().strftime("%H:%M:%S"),
            delta="实时"
        )
    
    # 分析历史图表
    if st.session_state.analysis_history:
        st.subheader("📈 分析历史趋势")
        
        # 创建趋势图
        df_history = pd.DataFrame(st.session_state.analysis_history)
        if not df_history.empty:
            fig = px.line(
                df_history, 
                x='timestamp', 
                y='total_hours',
                title="工时趋势分析",
                labels={'total_hours': '总工时', 'timestamp': '时间'}
            )
            st.plotly_chart(fig, use_container_width=True)
    
    # 系统信息
    st.subheader("🔧 系统信息")
    
    system_info = {
        "AI分析引擎": "简化版 v1.0" if AI_AVAILABLE else "不可用",
        "数据存储": "内存存储（会话级别）",
        "支持格式": "文本输入、文件上传",
        "分析功能": "工作项提取、工时计算、标签生成、异常检测"
    }
    
    for key, value in system_info.items():
        st.write(f"**{key}**: {value}")

elif page == "📝 周报分析":
    st.header("📝 周报分析")
    
    if not AI_AVAILABLE:
        st.error("AI分析引擎不可用，请检查系统配置")
        st.stop()
    
    # 分析表单
    with st.form("analysis_form"):
        st.subheader("📄 输入周报内容")
        
        # 员工信息
        col1, col2 = st.columns(2)
        with col1:
            employee_name = st.text_input("员工姓名", value="张三")
            department = st.selectbox(
                "部门",
                ["技术支持部", "研发部", "客服中心", "销售部", "综合技术部"]
            )
        
        with col2:
            employee_email = st.text_input("员工邮箱", value="<EMAIL>")
            role = st.selectbox(
                "岗位",
                ["技术支持工程师", "开发工程师", "客服专员", "销售经理", "FAE工程师"]
            )
        
        # 周报内容输入
        input_method = st.radio("输入方式", ["文本输入", "文件上传"])
        
        report_text = ""
        if input_method == "文本输入":
            report_text = st.text_area(
                "周报内容",
                height=200,
                placeholder="请输入本周的工作内容...\n\n例如：\n1. 处理客户A的技术问题，耗时4小时\n2. 参与产品培训，耗时2小时\n3. 编写技术文档，耗时3小时"
            )
        else:
            uploaded_file = st.file_uploader("上传周报文件", type=['txt', 'docx', 'pdf'])
            if uploaded_file is not None:
                # 简单的文件读取
                if uploaded_file.type == "text/plain":
                    report_text = str(uploaded_file.read(), "utf-8")
                else:
                    st.warning("当前仅支持txt文件，其他格式正在开发中")
        
        # 提交按钮
        submit_button = st.form_submit_button("🚀 开始分析", type="primary")
        
        if submit_button and report_text.strip():
            with st.spinner("🤖 AI正在分析中..."):
                try:
                    # 执行AI分析
                    result = st.session_state.analyzer.analyze(
                        report_text,
                        department=department,
                        role=role,
                        employee_email=employee_email.lower(),
                        employee_name=employee_name
                    )
                    
                    # 保存结果
                    result['created_today'] = True
                    result['ai_analyzed'] = True
                    result['created_at'] = datetime.now()
                    st.session_state.reports.append(result)
                    
                    # 保存到分析历史
                    st.session_state.analysis_history.append({
                        'timestamp': datetime.now(),
                        'total_hours': result.get('metrics', {}).get('total_hours', 0),
                        'task_count': result.get('metrics', {}).get('task_count', 0),
                        'department': department,
                        'role': role
                    })
                    
                    st.success("✅ 分析完成！")
                    
                    # 显示分析结果
                    st.subheader("📊 分析结果")
                    
                    # 基础信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("总工时", f"{result.get('metrics', {}).get('total_hours', 0):.1f}小时")
                    with col2:
                        st.metric("任务数量", result.get('metrics', {}).get('task_count', 0))
                    with col3:
                        st.metric("饱和度", result.get('metrics', {}).get('saturation_tag', '未知'))
                    
                    # 工作项详情
                    if result.get('work_items'):
                        st.subheader("📋 工作项明细")
                        work_items_df = pd.DataFrame(result['work_items'])
                        st.dataframe(work_items_df, use_container_width=True)
                        
                        # 工作项分布图
                        if len(work_items_df) > 1:
                            fig = px.pie(
                                work_items_df,
                                values='duration_hours',
                                names='category',
                                title="工作项时间分布"
                            )
                            st.plotly_chart(fig, use_container_width=True)
                    
                    # 标签和异常
                    col1, col2 = st.columns(2)
                    with col1:
                        st.subheader("🏷️ 智能标签")
                        for tag in result.get('tags', []):
                            st.badge(tag)
                    
                    with col2:
                        st.subheader("⚠️ 异常标记")
                        anomalies = result.get('anomaly_flags', [])
                        if anomalies:
                            for anomaly in anomalies:
                                st.warning(f"⚠️ {anomaly}")
                        else:
                            st.success("✅ 无异常")
                    
                    # 专项分析
                    with st.expander("🔍 详细分析"):
                        st.write("**创新能力分析:**", result.get('innovation_analysis', ''))
                        st.write("**品质分析:**", result.get('quality_analysis', ''))
                        st.write("**趋势分析:**", result.get('trend_analysis', ''))
                    
                    # 原始数据
                    with st.expander("📄 完整分析结果（JSON）"):
                        st.json(result)
                
                except Exception as e:
                    st.error(f"❌ 分析失败: {str(e)}")
        
        elif submit_button and not report_text.strip():
            st.warning("⚠️ 请输入周报内容")

elif page == "📋 任务明细":
    st.header("📋 任务明细")
    
    # 收集所有任务
    all_tasks = []
    for report in st.session_state.reports:
        for task in report.get('work_items', []):
            task_with_info = task.copy()
            task_with_info['employee_name'] = report.get('employee', {}).get('name', '')
            task_with_info['employee_email'] = report.get('employee', {}).get('email', '')
            task_with_info['department'] = report.get('employee', {}).get('department', '')
            task_with_info['report_id'] = report.get('report_id', '')
            all_tasks.append(task_with_info)
    
    if all_tasks:
        # 筛选器
        with st.expander("🔍 筛选条件", expanded=True):
            col1, col2, col3 = st.columns(3)
            with col1:
                filter_department = st.selectbox("部门", ["全部"] + list(set([t['department'] for t in all_tasks if t['department']])))
            with col2:
                filter_category = st.selectbox("类别", ["全部"] + list(set([t['category'] for t in all_tasks if t['category']])))
            with col3:
                filter_complexity = st.selectbox("复杂度", ["全部", "高", "中", "低"])
        
        # 应用筛选
        filtered_tasks = all_tasks
        if filter_department != "全部":
            filtered_tasks = [t for t in filtered_tasks if t['department'] == filter_department]
        if filter_category != "全部":
            filtered_tasks = [t for t in filtered_tasks if t['category'] == filter_category]
        if filter_complexity != "全部":
            filtered_tasks = [t for t in filtered_tasks if t['complexity'] == filter_complexity]
        
        # 显示任务表格
        if filtered_tasks:
            df_tasks = pd.DataFrame(filtered_tasks)
            st.dataframe(df_tasks, use_container_width=True)
            
            # 统计图表
            col1, col2 = st.columns(2)
            with col1:
                # 类别分布
                category_counts = df_tasks['category'].value_counts()
                fig1 = px.bar(
                    x=category_counts.index,
                    y=category_counts.values,
                    title="任务类别分布",
                    labels={'x': '类别', 'y': '数量'}
                )
                st.plotly_chart(fig1, use_container_width=True)
            
            with col2:
                # 复杂度分布
                complexity_counts = df_tasks['complexity'].value_counts()
                fig2 = px.pie(
                    values=complexity_counts.values,
                    names=complexity_counts.index,
                    title="任务复杂度分布"
                )
                st.plotly_chart(fig2, use_container_width=True)
        else:
            st.info("📝 没有符合筛选条件的任务")
    else:
        st.info("📝 暂无任务数据，请先进行周报分析")

elif page == "🏷️ 智能标签":
    st.header("🏷️ 智能标签")
    
    # 收集所有标签
    all_tags = []
    for report in st.session_state.reports:
        for tag in report.get('tags', []):
            all_tags.append({
                'tag': tag,
                'report_id': report.get('report_id', ''),
                'employee_name': report.get('employee', {}).get('name', ''),
                'department': report.get('employee', {}).get('department', '')
            })
    
    if all_tags:
        # 标签统计
        df_tags = pd.DataFrame(all_tags)
        tag_counts = df_tags['tag'].value_counts()
        
        col1, col2 = st.columns([2, 1])
        with col1:
            # 标签云（用条形图模拟）
            fig = px.bar(
                x=tag_counts.values,
                y=tag_counts.index,
                orientation='h',
                title="标签使用频率",
                labels={'x': '使用次数', 'y': '标签'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("📊 标签统计")
            st.metric("总标签数", len(tag_counts))
            st.metric("唯一标签", len(tag_counts))
            st.metric("平均使用", f"{tag_counts.mean():.1f}")
        
        # 标签详情表
        st.subheader("📋 标签详情")
        st.dataframe(df_tags, use_container_width=True)
    else:
        st.info("🏷️ 暂无标签数据，请先进行周报分析")

else:
    st.header(f"{page}")
    st.info(f"🚧 {page} 功能正在开发中，敬请期待！")
    
    # 显示开发计划
    st.subheader("🗓️ 开发计划")
    development_plan = {
        "⚠️ 异常洞察": "异常检测算法优化、异常处理流程",
        "📈 趋势分析": "时间序列分析、预测模型",
        "👥 员工管理": "员工信息管理、权限控制",
        "📧 邮件下载": "IMAP连接、邮件解析、附件处理",
        "⚙️ 系统设置": "配置管理、系统监控"
    }
    
    if page in development_plan:
        st.write(f"**{page}** 将包含以下功能：")
        st.write(development_plan[page])

# 页脚
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666;'>
        📧 AI驱动邮件周报分析系统 | 
        🤖 AI分析引擎: {'正常' if AI_AVAILABLE else '离线'} | 
        📊 数据存储: 内存模式 | 
        🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    </div>
    """.format(datetime=datetime),
    unsafe_allow_html=True
)
