#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能分析调度器
根据系统负载和任务优先级智能调度分析任务
"""
import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from queue import PriorityQueue, Queue, Empty
from enum import Enum
import heapq
from concurrent.futures import ThreadPoolExecutor, Future
import psutil

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisTask:
    """分析任务"""
    
    def __init__(self, task_id: str, data: Dict[str, Any], 
                 priority: TaskPriority = TaskPriority.NORMAL,
                 callback: Optional[Callable] = None,
                 timeout: int = 300):
        self.task_id = task_id
        self.data = data
        self.priority = priority
        self.callback = callback
        self.timeout = timeout
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.result = None
        self.error = None
        self.retry_count = 0
        self.max_retries = 2
    
    def __lt__(self, other):
        """优先级比较（数值越小优先级越高）"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        # 同优先级按创建时间排序
        return self.created_at < other.created_at
    
    def start(self):
        """开始执行任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete(self, result: Any):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
        
        if self.callback:
            try:
                self.callback(self)
            except Exception as e:
                logger.error(f"任务回调执行失败: {e}")
    
    def fail(self, error: Exception):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error = error
        
        if self.callback:
            try:
                self.callback(self)
            except Exception as e:
                logger.error(f"任务回调执行失败: {e}")
    
    def cancel(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED
        self.completed_at = datetime.now()
    
    def get_execution_time(self) -> Optional[float]:
        """获取执行时间"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def get_wait_time(self) -> float:
        """获取等待时间"""
        start_time = self.started_at or datetime.now()
        return (start_time - self.created_at).total_seconds()


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, check_interval: int = 5):
        self.check_interval = check_interval
        self.cpu_threshold = 80.0  # CPU使用率阈值
        self.memory_threshold = 85.0  # 内存使用率阈值
        self.monitoring = False
        self.monitor_thread = None
        self.system_stats = {
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'disk_usage': 0.0,
            'load_level': 'normal'  # normal, high, critical
        }
        self.lock = threading.Lock()
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        logger.info("系统监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统资源使用情况
                cpu_usage = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                with self.lock:
                    self.system_stats['cpu_usage'] = cpu_usage
                    self.system_stats['memory_usage'] = memory.percent
                    self.system_stats['disk_usage'] = (disk.used / disk.total) * 100
                    
                    # 计算负载级别
                    if cpu_usage > self.cpu_threshold or memory.percent > self.memory_threshold:
                        if cpu_usage > 90 or memory.percent > 95:
                            self.system_stats['load_level'] = 'critical'
                        else:
                            self.system_stats['load_level'] = 'high'
                    else:
                        self.system_stats['load_level'] = 'normal'
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"系统监控异常: {e}")
                time.sleep(self.check_interval)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        with self.lock:
            return self.system_stats.copy()
    
    def is_system_overloaded(self) -> bool:
        """检查系统是否过载"""
        with self.lock:
            return self.system_stats['load_level'] in ['high', 'critical']


class SmartScheduler:
    """智能分析调度器"""
    
    def __init__(self, max_workers: int = 3, enable_system_monitor: bool = True):
        self.max_workers = max_workers
        self.current_workers = 0
        self.task_queue = PriorityQueue()
        self.running_tasks: Dict[str, AnalysisTask] = {}
        self.completed_tasks: Dict[str, AnalysisTask] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.futures: Dict[str, Future] = {}
        
        # 系统监控
        self.system_monitor = SystemMonitor() if enable_system_monitor else None
        
        # 调度配置
        self.scheduling_config = {
            'adaptive_workers': True,  # 自适应工作线程数
            'load_balancing': True,    # 负载均衡
            'priority_boost': True,    # 优先级提升
            'timeout_handling': True,  # 超时处理
            'retry_failed': True       # 重试失败任务
        }
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'avg_execution_time': 0.0,
            'avg_wait_time': 0.0
        }
        
        self.lock = threading.Lock()
        self.running = False
        self.scheduler_thread = None
        
        logger.info(f"智能调度器初始化完成，最大工作线程: {max_workers}")
    
    def start(self):
        """启动调度器"""
        if not self.running:
            self.running = True
            
            # 启动系统监控
            if self.system_monitor:
                self.system_monitor.start_monitoring()
            
            # 启动调度线程
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            logger.info("智能调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if self.running:
            self.running = False
            
            # 停止系统监控
            if self.system_monitor:
                self.system_monitor.stop_monitoring()
            
            # 等待调度线程结束
            if self.scheduler_thread:
                self.scheduler_thread.join(timeout=10)
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            logger.info("智能调度器已停止")
    
    def submit_task(self, task_id: str, data: Dict[str, Any], 
                   priority: TaskPriority = TaskPriority.NORMAL,
                   callback: Optional[Callable] = None,
                   timeout: int = 300) -> str:
        """提交分析任务"""
        task = AnalysisTask(task_id, data, priority, callback, timeout)
        
        with self.lock:
            self.task_queue.put(task)
            self.stats['total_tasks'] += 1
        
        logger.info(f"任务已提交: {task_id}, 优先级: {priority.name}")
        return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            # 检查运行中的任务
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.cancel()
                
                # 取消Future
                if task_id in self.futures:
                    self.futures[task_id].cancel()
                    del self.futures[task_id]
                
                # 移动到完成列表
                self.completed_tasks[task_id] = task
                del self.running_tasks[task_id]
                self.stats['cancelled_tasks'] += 1
                
                logger.info(f"任务已取消: {task_id}")
                return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            # 检查运行中的任务
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                return {
                    'task_id': task.task_id,
                    'status': task.status.value,
                    'priority': task.priority.name,
                    'created_at': task.created_at.isoformat(),
                    'started_at': task.started_at.isoformat() if task.started_at else None,
                    'wait_time': task.get_wait_time()
                }
            
            # 检查完成的任务
            if task_id in self.completed_tasks:
                task = self.completed_tasks[task_id]
                return {
                    'task_id': task.task_id,
                    'status': task.status.value,
                    'priority': task.priority.name,
                    'created_at': task.created_at.isoformat(),
                    'started_at': task.started_at.isoformat() if task.started_at else None,
                    'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                    'execution_time': task.get_execution_time(),
                    'wait_time': task.get_wait_time(),
                    'error': str(task.error) if task.error else None
                }
        
        return None
    
    def _scheduler_loop(self):
        """调度循环"""
        while self.running:
            try:
                # 自适应调整工作线程数
                if self.scheduling_config['adaptive_workers']:
                    self._adjust_workers()
                
                # 处理任务队列
                self._process_task_queue()
                
                # 检查超时任务
                if self.scheduling_config['timeout_handling']:
                    self._check_timeouts()
                
                # 清理完成的任务
                self._cleanup_completed_tasks()
                
                time.sleep(1)  # 调度间隔
                
            except Exception as e:
                logger.error(f"调度循环异常: {e}")
                time.sleep(5)
    
    def _adjust_workers(self):
        """自适应调整工作线程数"""
        if not self.system_monitor:
            return
        
        system_stats = self.system_monitor.get_system_stats()
        load_level = system_stats['load_level']
        
        with self.lock:
            current_load = len(self.running_tasks)
            queue_size = self.task_queue.qsize()
            
            # 根据系统负载调整
            if load_level == 'critical':
                # 系统过载，减少工作线程
                target_workers = max(1, self.max_workers // 2)
            elif load_level == 'high':
                # 高负载，适度减少
                target_workers = max(2, int(self.max_workers * 0.7))
            else:
                # 正常负载，根据队列情况调整
                if queue_size > 10:
                    target_workers = self.max_workers
                elif queue_size > 5:
                    target_workers = max(2, int(self.max_workers * 0.8))
                else:
                    target_workers = max(1, int(self.max_workers * 0.6))
            
            # 更新最大工作线程数（这里简化处理，实际可能需要重新创建线程池）
            if target_workers != self.current_workers:
                logger.info(f"调整工作线程数: {self.current_workers} -> {target_workers}")
                self.current_workers = target_workers
    
    def _process_task_queue(self):
        """处理任务队列"""
        with self.lock:
            # 检查是否可以启动新任务
            if len(self.running_tasks) >= self.max_workers:
                return
            
            if self.task_queue.empty():
                return
        
        try:
            # 获取优先级最高的任务
            task = self.task_queue.get_nowait()
            
            # 启动任务
            self._start_task(task)
            
        except Empty:
            pass
        except Exception as e:
            logger.error(f"处理任务队列异常: {e}")
    
    def _start_task(self, task: AnalysisTask):
        """启动任务"""
        task.start()
        
        with self.lock:
            self.running_tasks[task.task_id] = task
        
        # 提交到线程池
        future = self.executor.submit(self._execute_task, task)
        
        with self.lock:
            self.futures[task.task_id] = future
        
        logger.info(f"任务开始执行: {task.task_id}")
    
    def _execute_task(self, task: AnalysisTask):
        """执行任务"""
        try:
            # 这里应该调用实际的分析函数
            # 暂时模拟执行
            time.sleep(2)  # 模拟处理时间
            result = {'status': 'success', 'data': task.data}
            
            task.complete(result)
            
            with self.lock:
                self.completed_tasks[task.task_id] = task
                if task.task_id in self.running_tasks:
                    del self.running_tasks[task.task_id]
                if task.task_id in self.futures:
                    del self.futures[task.task_id]
                self.stats['completed_tasks'] += 1
            
            logger.info(f"任务执行完成: {task.task_id}")
            
        except Exception as e:
            task.fail(e)
            
            with self.lock:
                self.completed_tasks[task.task_id] = task
                if task.task_id in self.running_tasks:
                    del self.running_tasks[task.task_id]
                if task.task_id in self.futures:
                    del self.futures[task.task_id]
                self.stats['failed_tasks'] += 1
            
            logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")
            
            # 重试逻辑
            if (self.scheduling_config['retry_failed'] and 
                task.retry_count < task.max_retries):
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                
                with self.lock:
                    self.task_queue.put(task)
                
                logger.info(f"任务重新排队重试: {task.task_id}, 重试次数: {task.retry_count}")
    
    def _check_timeouts(self):
        """检查超时任务"""
        current_time = datetime.now()
        timeout_tasks = []
        
        with self.lock:
            for task_id, task in self.running_tasks.items():
                if task.started_at:
                    elapsed = (current_time - task.started_at).total_seconds()
                    if elapsed > task.timeout:
                        timeout_tasks.append(task_id)
        
        # 取消超时任务
        for task_id in timeout_tasks:
            self.cancel_task(task_id)
            logger.warning(f"任务超时被取消: {task_id}")
    
    def _cleanup_completed_tasks(self):
        """清理完成的任务（保留最近1000个）"""
        with self.lock:
            if len(self.completed_tasks) > 1000:
                # 按完成时间排序，删除最旧的
                sorted_tasks = sorted(
                    self.completed_tasks.items(),
                    key=lambda x: x[1].completed_at or datetime.min
                )
                
                # 保留最新的1000个
                tasks_to_keep = dict(sorted_tasks[-1000:])
                self.completed_tasks = tasks_to_keep
    
    def get_scheduler_statistics(self) -> Dict[str, Any]:
        """获取调度器统计"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'queue_size': self.task_queue.qsize(),
                'running_tasks': len(self.running_tasks),
                'completed_tasks_stored': len(self.completed_tasks),
                'max_workers': self.max_workers,
                'current_workers': self.current_workers
            })
        
        if self.system_monitor:
            stats['system_stats'] = self.system_monitor.get_system_stats()
        
        return stats
