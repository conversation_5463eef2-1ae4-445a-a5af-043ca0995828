#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理模块包

模块描述: 统一配置管理，支持多环境配置和配置工厂模式
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .base, .development, .production, os, typing
"""

import os
import logging
from typing import Type, Dict, Any

from .base import BaseConfig
from .development import DevelopmentConfig
from .production import ProductionConfig

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

class ConfigFactory:
    """
    配置工厂类
    
    负责根据环境变量创建相应的配置实例
    """
    
    _configs: Dict[str, Type[BaseConfig]] = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'testing': DevelopmentConfig,  # 测试环境使用开发配置
    }
    
    _instance: BaseConfig = None
    
    @classmethod
    def get_config(cls, environment: str = None) -> BaseConfig:
        """
        获取配置实例（单例模式）
        
        Args:
            environment: 环境名称，如果为None则从环境变量读取
            
        Returns:
            BaseConfig: 配置实例
        """
        if cls._instance is None:
            if environment is None:
                environment = os.getenv('ENVIRONMENT', 'development')
            
            cls._instance = cls._create_config(environment)
        
        return cls._instance
    
    @classmethod
    def _create_config(cls, environment: str) -> BaseConfig:
        """
        创建配置实例
        
        Args:
            environment: 环境名称
            
        Returns:
            BaseConfig: 配置实例
        """
        if environment not in cls._configs:
            available_envs = ', '.join(cls._configs.keys())
            raise ValueError(f"不支持的环境: {environment}. 可用环境: {available_envs}")
        
        config_class = cls._configs[environment]
        config_instance = config_class()
        
        # 验证配置
        if not config_instance.validate_config():
            raise ValueError(f"配置验证失败: {environment}")
        
        # 记录配置加载信息
        logger = logging.getLogger(__name__)
        logger.info(f"配置加载成功: {environment}")
        
        return config_instance
    
    @classmethod
    def register_config(cls, environment: str, config_class: Type[BaseConfig]) -> None:
        """
        注册新的配置类
        
        Args:
            environment: 环境名称
            config_class: 配置类
        """
        if not issubclass(config_class, BaseConfig):
            raise ValueError("配置类必须继承自BaseConfig")
        
        cls._configs[environment] = config_class
        
        # 如果当前实例是这个环境，重置实例
        if cls._instance and cls._instance.environment == environment:
            cls._instance = None
    
    @classmethod
    def reset(cls) -> None:
        """重置配置实例"""
        cls._instance = None
    
    @classmethod
    def list_environments(cls) -> list:
        """获取所有可用环境"""
        return list(cls._configs.keys())
    
    @classmethod
    def get_config_info(cls, environment: str = None) -> Dict[str, Any]:
        """
        获取配置信息（不包含敏感数据）
        
        Args:
            environment: 环境名称
            
        Returns:
            Dict[str, Any]: 配置信息
        """
        config = cls.get_config(environment)
        return config.get_config_dict()

# 创建全局配置实例
config = ConfigFactory.get_config()

# 导出主要类和实例
__all__ = [
    'BaseConfig',
    'DevelopmentConfig', 
    'ProductionConfig',
    'ConfigFactory',
    'config'
]

# 配置日志
def setup_logging(config_instance: BaseConfig = None) -> None:
    """
    设置日志配置
    
    Args:
        config_instance: 配置实例，如果为None则使用全局配置
    """
    if config_instance is None:
        config_instance = config
    
    import logging.config
    import os
    
    # 确保日志目录存在
    log_dir = config_instance.logging.log_dir
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 应用日志配置
    logging_config = config_instance.logging.get_logging_config()
    logging.config.dictConfig(logging_config)
    
    # 记录配置加载信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，环境: {config_instance.environment}")

# 自动设置日志
try:
    setup_logging()
except Exception as e:
    # 如果日志设置失败，使用基本配置
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s'
    )
    logging.getLogger(__name__).warning(f"日志配置失败，使用默认配置: {e}")

def get_database_url() -> str:
    """
    获取数据库连接URL
    
    Returns:
        str: 数据库连接URL
    """
    return config.database.get_connection_url()

def get_ai_config() -> Dict[str, Any]:
    """
    获取AI配置
    
    Returns:
        Dict[str, Any]: AI配置字典
    """
    return {
        'base_url': config.ai.base_url,
        'model_name': config.ai.model_name,
        'embedding_model': config.ai.embedding_model,
        'timeout': config.ai.timeout,
        'max_retries': config.ai.max_retries,
        'temperature': config.ai.temperature,
        'max_tokens': config.ai.max_tokens
    }

def get_cache_config() -> Dict[str, Any]:
    """
    获取缓存配置
    
    Returns:
        Dict[str, Any]: 缓存配置字典
    """
    return config.cache.get_redis_config()

def is_development() -> bool:
    """
    判断是否为开发环境
    
    Returns:
        bool: 是否为开发环境
    """
    return config.environment == 'development'

def is_production() -> bool:
    """
    判断是否为生产环境
    
    Returns:
        bool: 是否为生产环境
    """
    return config.environment == 'production'

def is_testing() -> bool:
    """
    判断是否为测试环境
    
    Returns:
        bool: 是否为测试环境
    """
    return config.testing or config.environment == 'testing'
