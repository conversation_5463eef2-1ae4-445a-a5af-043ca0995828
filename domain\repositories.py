#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域仓储接口模块

模块描述: 定义领域实体的仓储接口，遵循DDD设计原则
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: abc, typing, core.base
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from core.base import BaseRepository
from .entities import Employee, WorkItem, WeeklyReport
from .value_objects import FilterCriteria, TimeRange


class IEmployeeRepository(BaseRepository):
    """员工仓储接口"""
    
    @abstractmethod
    def get_by_email(self, email: str) -> Optional[Employee]:
        """根据邮箱获取员工"""
        pass
    
    @abstractmethod
    def get_by_department(self, department: str) -> List[Employee]:
        """根据部门获取员工列表"""
        pass
    
    @abstractmethod
    def get_by_role(self, role: str) -> List[Employee]:
        """根据角色获取员工列表"""
        pass
    
    @abstractmethod
    def search(self, keyword: str) -> List[Employee]:
        """搜索员工"""
        pass


class IWorkItemRepository(BaseRepository):
    """工作项仓储接口"""
    
    @abstractmethod
    def get_by_employee(self, employee_email: str, 
                       time_range: Optional[TimeRange] = None) -> List[WorkItem]:
        """根据员工获取工作项"""
        pass
    
    @abstractmethod
    def get_by_category(self, category: str) -> List[WorkItem]:
        """根据类别获取工作项"""
        pass
    
    @abstractmethod
    def get_by_complexity(self, complexity: str) -> List[WorkItem]:
        """根据复杂度获取工作项"""
        pass
    
    @abstractmethod
    def get_statistics(self, criteria: FilterCriteria) -> Dict[str, Any]:
        """获取工作项统计信息"""
        pass


class IWeeklyReportRepository(BaseRepository):
    """周报仓储接口"""
    
    @abstractmethod
    def get_by_employee(self, employee_email: str, 
                       time_range: Optional[TimeRange] = None) -> List[WeeklyReport]:
        """根据员工获取周报"""
        pass
    
    @abstractmethod
    def get_by_week(self, week: str) -> List[WeeklyReport]:
        """根据周次获取周报"""
        pass
    
    @abstractmethod
    def get_by_department(self, department: str, 
                         time_range: Optional[TimeRange] = None) -> List[WeeklyReport]:
        """根据部门获取周报"""
        pass
    
    @abstractmethod
    def get_latest(self, limit: int = 10) -> List[WeeklyReport]:
        """获取最新的周报"""
        pass
    
    @abstractmethod
    def search(self, criteria: FilterCriteria) -> List[WeeklyReport]:
        """搜索周报"""
        pass
    
    @abstractmethod
    def get_analytics(self, criteria: FilterCriteria) -> Dict[str, Any]:
        """获取分析数据"""
        pass


class IAnalysisResultRepository(BaseRepository):
    """分析结果仓储接口"""
    
    @abstractmethod
    def get_by_report_id(self, report_id: str) -> List[Dict[str, Any]]:
        """根据报告ID获取分析结果"""
        pass
    
    @abstractmethod
    def get_by_analysis_type(self, analysis_type: str) -> List[Dict[str, Any]]:
        """根据分析类型获取结果"""
        pass
    
    @abstractmethod
    def get_trends(self, criteria: FilterCriteria) -> Dict[str, Any]:
        """获取趋势数据"""
        pass


class ITagRepository(BaseRepository):
    """标签仓储接口"""
    
    @abstractmethod
    def get_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据类别获取标签"""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """获取标签统计"""
        pass
    
    @abstractmethod
    def get_popular_tags(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门标签"""
        pass


class IAnomalyRepository(BaseRepository):
    """异常仓储接口"""
    
    @abstractmethod
    def get_by_type(self, anomaly_type: str) -> List[Dict[str, Any]]:
        """根据类型获取异常"""
        pass
    
    @abstractmethod
    def get_by_severity(self, severity: str) -> List[Dict[str, Any]]:
        """根据严重程度获取异常"""
        pass
    
    @abstractmethod
    def get_unresolved(self) -> List[Dict[str, Any]]:
        """获取未解决的异常"""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """获取异常统计"""
        pass


class ITemplateRepository(BaseRepository):
    """模板仓储接口"""
    
    @abstractmethod
    def get_by_role(self, role: str) -> List[Dict[str, Any]]:
        """根据角色获取模板"""
        pass
    
    @abstractmethod
    def get_active_templates(self) -> List[Dict[str, Any]]:
        """获取活跃模板"""
        pass
    
    @abstractmethod
    def get_default_template(self, role: str) -> Optional[Dict[str, Any]]:
        """获取默认模板"""
        pass


# 仓储工厂
class RepositoryFactory:
    """仓储工厂"""
    
    _repositories: Dict[str, BaseRepository] = {}
    
    @classmethod
    def register_repository(cls, name: str, repository: BaseRepository) -> None:
        """注册仓储"""
        cls._repositories[name] = repository
    
    @classmethod
    def get_repository(cls, name: str) -> BaseRepository:
        """获取仓储"""
        if name not in cls._repositories:
            raise ValueError(f"仓储未注册: {name}")
        return cls._repositories[name]
    
    @classmethod
    def get_employee_repository(cls) -> IEmployeeRepository:
        """获取员工仓储"""
        return cls.get_repository('employee')
    
    @classmethod
    def get_work_item_repository(cls) -> IWorkItemRepository:
        """获取工作项仓储"""
        return cls.get_repository('work_item')
    
    @classmethod
    def get_weekly_report_repository(cls) -> IWeeklyReportRepository:
        """获取周报仓储"""
        return cls.get_repository('weekly_report')
    
    @classmethod
    def get_analysis_result_repository(cls) -> IAnalysisResultRepository:
        """获取分析结果仓储"""
        return cls.get_repository('analysis_result')
    
    @classmethod
    def get_tag_repository(cls) -> ITagRepository:
        """获取标签仓储"""
        return cls.get_repository('tag')
    
    @classmethod
    def get_anomaly_repository(cls) -> IAnomalyRepository:
        """获取异常仓储"""
        return cls.get_repository('anomaly')
    
    @classmethod
    def get_template_repository(cls) -> ITemplateRepository:
        """获取模板仓储"""
        return cls.get_repository('template')
