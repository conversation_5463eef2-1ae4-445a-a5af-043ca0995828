#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析服务主类

模块描述: 统一分析入口，协调各种分析器，提供高级分析功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.base, core.interfaces, domain.entities, domain.value_objects
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio
import concurrent.futures

from core.base import BaseService
from core.interfaces import IAnalyzer
from core.decorators import performance_monitor, cache_result
from domain.entities import WeeklyReport, AnalysisResult
from domain.value_objects import ProcessingContext
from domain.events import WeeklyReportAnalyzedEvent, publish_event


class AnalysisService(BaseService):
    """
    分析服务 - 统一分析入口，协调各种分析器
    
    职责：
    - 管理和协调各种分析器
    - 提供统一的分析接口
    - 支持批量和并行分析
    - 缓存分析结果
    - 发布分析事件
    """
    
    def __init__(self, config, analyzers: List[IAnalyzer] = None, cache_manager=None):
        """
        初始化分析服务
        
        Args:
            config: 服务配置
            analyzers: 分析器列表
            cache_manager: 缓存管理器
        """
        super().__init__(config)
        self.analyzers = {analyzer.get_name(): analyzer for analyzer in (analyzers or [])}
        self.cache_manager = cache_manager
        self._analysis_history: List[Dict[str, Any]] = []
    
    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化所有分析器
            for name, analyzer in self.analyzers.items():
                if hasattr(analyzer, 'initialize'):
                    if not analyzer.initialize():
                        self.logger.error(f"分析器初始化失败: {name}")
                        return False
                self.logger.info(f"分析器初始化成功: {name}")
            
            self.logger.info("分析服务初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"分析服务初始化失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.config.name:
            return False
        return True
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            'service_name': self.config.name,
            'version': self.config.version,
            'analyzers': list(self.analyzers.keys()),
            'analyzer_count': len(self.analyzers),
            'analysis_history_count': len(self._analysis_history),
            'cache_enabled': self.cache_manager is not None
        }
    
    def add_analyzer(self, analyzer: IAnalyzer) -> None:
        """添加分析器"""
        name = analyzer.get_name()
        self.analyzers[name] = analyzer
        self.logger.info(f"添加分析器: {name}")
    
    def remove_analyzer(self, analyzer_name: str) -> bool:
        """移除分析器"""
        if analyzer_name in self.analyzers:
            del self.analyzers[analyzer_name]
            self.logger.info(f"移除分析器: {analyzer_name}")
            return True
        return False
    
    @performance_monitor
    @cache_result(ttl=1800)  # 缓存30分钟
    def analyze_report(self, report: WeeklyReport, 
                      analysis_types: List[str] = None,
                      context: ProcessingContext = None) -> List[AnalysisResult]:
        """
        分析周报 - 支持多种分析类型
        
        Args:
            report: 周报对象
            analysis_types: 指定的分析类型列表，如果为None则使用所有可用分析器
            context: 处理上下文
            
        Returns:
            List[AnalysisResult]: 分析结果列表
        """
        if analysis_types is None:
            analysis_types = list(self.analyzers.keys())
        
        if context is None:
            context = self._create_context(report)
        
        results = []
        analysis_start_time = datetime.now()
        
        for analysis_type in analysis_types:
            if analysis_type in self.analyzers:
                try:
                    analyzer = self.analyzers[analysis_type]
                    
                    # 检查分析器是否支持该数据类型
                    if hasattr(analyzer, 'get_supported_types'):
                        supported_types = analyzer.get_supported_types()
                        if 'weekly_report' not in supported_types:
                            self.logger.warning(f"分析器 {analysis_type} 不支持周报分析")
                            continue
                    
                    # 执行分析
                    result = analyzer.analyze(report, context)
                    if result:
                        results.append(result)
                        self.logger.info(f"完成 {analysis_type} 分析，置信度: {result.confidence_score}")
                    
                except Exception as e:
                    self.logger.error(f"{analysis_type} 分析失败: {e}")
                    # 创建错误结果
                    error_result = AnalysisResult(
                        result_id=f"error_{analysis_type}_{datetime.now().timestamp()}",
                        report_id=report.report_id,
                        analysis_type=analysis_type,
                        result_data={'error': str(e)},
                        confidence_score=0.0,
                        model_version='error',
                        processing_time=0.0
                    )
                    results.append(error_result)
        
        # 记录分析历史
        analysis_duration = (datetime.now() - analysis_start_time).total_seconds()
        self._record_analysis_history(report, results, analysis_duration)
        
        # 发布分析完成事件
        self._publish_analysis_event(report, results)
        
        return results
    
    @performance_monitor
    def batch_analyze(self, reports: List[WeeklyReport], 
                     analysis_types: List[str] = None,
                     parallel: bool = True,
                     max_workers: int = 4) -> List[List[AnalysisResult]]:
        """
        批量分析 - 支持并行处理
        
        Args:
            reports: 周报列表
            analysis_types: 分析类型列表
            parallel: 是否并行处理
            max_workers: 最大工作线程数
            
        Returns:
            List[List[AnalysisResult]]: 批量分析结果
        """
        if not reports:
            return []
        
        if parallel and len(reports) > 1:
            return self._parallel_batch_analyze(reports, analysis_types, max_workers)
        else:
            return [self.analyze_report(report, analysis_types) for report in reports]
    
    async def analyze_report_async(self, report: WeeklyReport,
                                  analysis_types: List[str] = None) -> List[AnalysisResult]:
        """异步分析周报"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self.analyze_report, report, analysis_types
        )
    
    def get_available_analyzers(self) -> List[str]:
        """获取可用的分析器列表"""
        return list(self.analyzers.keys())
    
    def get_analyzer_info(self, analyzer_name: str) -> Optional[Dict[str, Any]]:
        """获取分析器信息"""
        if analyzer_name not in self.analyzers:
            return None
        
        analyzer = self.analyzers[analyzer_name]
        info = {
            'name': analyzer.get_name(),
            'supported_types': getattr(analyzer, 'get_supported_types', lambda: [])(),
            'description': getattr(analyzer, 'get_description', lambda: '')(),
            'version': getattr(analyzer, 'get_version', lambda: '1.0.0')()
        }
        return info
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        if not self._analysis_history:
            return {'total_analyses': 0}
        
        total_analyses = len(self._analysis_history)
        avg_duration = sum(h['duration'] for h in self._analysis_history) / total_analyses
        
        analyzer_usage = {}
        for history in self._analysis_history:
            for result in history['results']:
                analyzer_name = result.analysis_type
                if analyzer_name not in analyzer_usage:
                    analyzer_usage[analyzer_name] = 0
                analyzer_usage[analyzer_name] += 1
        
        return {
            'total_analyses': total_analyses,
            'average_duration': avg_duration,
            'analyzer_usage': analyzer_usage,
            'last_analysis': self._analysis_history[-1]['timestamp'].isoformat()
        }
    
    def _create_context(self, report: WeeklyReport) -> ProcessingContext:
        """创建处理上下文"""
        return ProcessingContext(
            user_id=report.employee.email,
            department=report.employee.department,
            role=report.employee.role,
            preferences={},
            timestamp=datetime.now()
        )
    
    def _parallel_batch_analyze(self, reports: List[WeeklyReport],
                               analysis_types: List[str] = None,
                               max_workers: int = 4) -> List[List[AnalysisResult]]:
        """并行批量分析"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = [
                executor.submit(self.analyze_report, report, analysis_types)
                for report in reports
            ]
            
            # 收集结果
            results = []
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"并行分析任务失败: {e}")
                    results.append([])
        
        return results
    
    def _record_analysis_history(self, report: WeeklyReport, 
                                results: List[AnalysisResult],
                                duration: float) -> None:
        """记录分析历史"""
        history_entry = {
            'timestamp': datetime.now(),
            'report_id': report.report_id,
            'employee_email': report.employee.email,
            'results': results,
            'duration': duration,
            'analyzer_count': len(results)
        }
        
        self._analysis_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(self._analysis_history) > 1000:
            self._analysis_history = self._analysis_history[-500:]
    
    def _publish_analysis_event(self, report: WeeklyReport, 
                               results: List[AnalysisResult]) -> None:
        """发布分析完成事件"""
        try:
            # 提取异常信息
            anomalies = []
            for result in results:
                if 'anomalies' in result.result_data:
                    anomalies.extend(result.result_data['anomalies'])
            
            # 创建并发布事件
            event = WeeklyReportAnalyzedEvent(
                report_id=report.report_id,
                employee_email=report.employee.email,
                analysis_results={r.analysis_type: r.result_data for r in results},
                anomalies_detected=anomalies,
                analysis_version=self.config.version
            )
            
            publish_event(event)
            
        except Exception as e:
            self.logger.error(f"发布分析事件失败: {e}")
    
    def _cleanup(self) -> None:
        """清理资源"""
        # 清理分析器
        for analyzer in self.analyzers.values():
            if hasattr(analyzer, 'cleanup'):
                try:
                    analyzer.cleanup()
                except Exception as e:
                    self.logger.error(f"分析器清理失败: {e}")
        
        # 清理历史记录
        self._analysis_history.clear()
        
        self.logger.info("分析服务资源清理完成")
