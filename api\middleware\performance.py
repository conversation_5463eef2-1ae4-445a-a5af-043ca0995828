#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能监控中间件
实现API性能监控、指标收集、性能分析
"""
import time
import logging
import asyncio
from typing import Dict, Any, List, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import psutil
import threading
from collections import defaultdict, deque
from statistics import mean, median

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self, max_records: int = 10000):
        self.max_records = max_records
        self.request_metrics = deque(maxlen=max_records)
        self.endpoint_metrics = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'response_times': deque(maxlen=1000),
            'error_count': 0,
            'last_request': None
        })
        self.system_metrics = {
            'cpu_usage': deque(maxlen=100),
            'memory_usage': deque(maxlen=100),
            'disk_usage': deque(maxlen=100),
            'network_io': deque(maxlen=100)
        }
        self.lock = threading.Lock()
        
        # 启动系统监控线程
        self.monitoring_thread = threading.Thread(target=self._monitor_system, daemon=True)
        self.monitoring_thread.start()
    
    def record_request(self, 
                      endpoint: str, 
                      method: str, 
                      response_time: float, 
                      status_code: int,
                      request_size: int = 0,
                      response_size: int = 0):
        """记录请求指标"""
        with self.lock:
            # 记录单次请求
            request_record = {
                'timestamp': time.time(),
                'endpoint': endpoint,
                'method': method,
                'response_time': response_time,
                'status_code': status_code,
                'request_size': request_size,
                'response_size': response_size,
                'is_error': status_code >= 400
            }
            self.request_metrics.append(request_record)
            
            # 更新端点统计
            endpoint_key = f"{method} {endpoint}"
            metrics = self.endpoint_metrics[endpoint_key]
            metrics['count'] += 1
            metrics['total_time'] += response_time
            metrics['response_times'].append(response_time)
            metrics['last_request'] = time.time()
            
            if status_code >= 400:
                metrics['error_count'] += 1
    
    def _monitor_system(self):
        """系统资源监控"""
        while True:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.system_metrics['cpu_usage'].append({
                    'timestamp': time.time(),
                    'value': cpu_percent
                })
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.system_metrics['memory_usage'].append({
                    'timestamp': time.time(),
                    'value': memory.percent,
                    'available': memory.available,
                    'total': memory.total
                })
                
                # 磁盘使用率
                disk = psutil.disk_usage('/')
                self.system_metrics['disk_usage'].append({
                    'timestamp': time.time(),
                    'value': (disk.used / disk.total) * 100,
                    'free': disk.free,
                    'total': disk.total
                })
                
                # 网络IO
                network = psutil.net_io_counters()
                self.system_metrics['network_io'].append({
                    'timestamp': time.time(),
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                })
                
                time.sleep(10)  # 每10秒收集一次
                
            except Exception as e:
                logger.error(f"系统监控异常: {e}")
                time.sleep(30)  # 异常时等待30秒
    
    def get_endpoint_stats(self, endpoint: Optional[str] = None) -> Dict[str, Any]:
        """获取端点统计信息"""
        with self.lock:
            if endpoint:
                if endpoint in self.endpoint_metrics:
                    metrics = self.endpoint_metrics[endpoint]
                    response_times = list(metrics['response_times'])
                    
                    return {
                        'endpoint': endpoint,
                        'total_requests': metrics['count'],
                        'error_count': metrics['error_count'],
                        'error_rate': metrics['error_count'] / metrics['count'] if metrics['count'] > 0 else 0,
                        'avg_response_time': metrics['total_time'] / metrics['count'] if metrics['count'] > 0 else 0,
                        'min_response_time': min(response_times) if response_times else 0,
                        'max_response_time': max(response_times) if response_times else 0,
                        'median_response_time': median(response_times) if response_times else 0,
                        'p95_response_time': self._calculate_percentile(response_times, 95),
                        'p99_response_time': self._calculate_percentile(response_times, 99),
                        'last_request': metrics['last_request']
                    }
                else:
                    return {'error': 'Endpoint not found'}
            else:
                # 返回所有端点的汇总统计
                all_stats = {}
                for endpoint_key, metrics in self.endpoint_metrics.items():
                    response_times = list(metrics['response_times'])
                    all_stats[endpoint_key] = {
                        'total_requests': metrics['count'],
                        'error_count': metrics['error_count'],
                        'error_rate': metrics['error_count'] / metrics['count'] if metrics['count'] > 0 else 0,
                        'avg_response_time': metrics['total_time'] / metrics['count'] if metrics['count'] > 0 else 0,
                        'p95_response_time': self._calculate_percentile(response_times, 95),
                        'last_request': metrics['last_request']
                    }
                
                return all_stats
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        with self.lock:
            stats = {}
            
            for metric_name, metric_data in self.system_metrics.items():
                if metric_data:
                    if metric_name in ['cpu_usage', 'memory_usage', 'disk_usage']:
                        values = [item['value'] for item in metric_data]
                        stats[metric_name] = {
                            'current': values[-1] if values else 0,
                            'avg': mean(values) if values else 0,
                            'max': max(values) if values else 0,
                            'min': min(values) if values else 0
                        }
                    elif metric_name == 'network_io':
                        latest = metric_data[-1] if metric_data else {}
                        stats[metric_name] = {
                            'bytes_sent': latest.get('bytes_sent', 0),
                            'bytes_recv': latest.get('bytes_recv', 0),
                            'packets_sent': latest.get('packets_sent', 0),
                            'packets_recv': latest.get('packets_recv', 0)
                        }
            
            return stats
    
    def get_overall_stats(self) -> Dict[str, Any]:
        """获取整体统计信息"""
        with self.lock:
            if not self.request_metrics:
                return {'message': 'No requests recorded'}
            
            total_requests = len(self.request_metrics)
            error_requests = sum(1 for req in self.request_metrics if req['is_error'])
            response_times = [req['response_time'] for req in self.request_metrics]
            
            # 计算QPS（最近1分钟）
            current_time = time.time()
            recent_requests = [req for req in self.request_metrics 
                             if current_time - req['timestamp'] <= 60]
            qps = len(recent_requests) / 60 if recent_requests else 0
            
            return {
                'total_requests': total_requests,
                'error_requests': error_requests,
                'error_rate': error_requests / total_requests,
                'avg_response_time': mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': median(response_times),
                'p95_response_time': self._calculate_percentile(response_times, 95),
                'p99_response_time': self._calculate_percentile(response_times, 99),
                'qps': qps,
                'total_endpoints': len(self.endpoint_metrics)
            }
    
    def _calculate_percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        if index >= len(sorted_data):
            index = len(sorted_data) - 1
        
        return sorted_data[index]
    
    def get_slow_requests(self, threshold: float = 1.0, limit: int = 10) -> List[Dict[str, Any]]:
        """获取慢请求列表"""
        with self.lock:
            slow_requests = [
                req for req in self.request_metrics 
                if req['response_time'] > threshold
            ]
            
            # 按响应时间降序排序
            slow_requests.sort(key=lambda x: x['response_time'], reverse=True)
            
            return slow_requests[:limit]
    
    def clear_metrics(self):
        """清空指标数据"""
        with self.lock:
            self.request_metrics.clear()
            self.endpoint_metrics.clear()
            for metric_data in self.system_metrics.values():
                metric_data.clear()


# 全局性能指标收集器
performance_metrics = PerformanceMetrics()


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app, enabled: bool = True):
        super().__init__(app)
        self.enabled = enabled
    
    def _get_request_size(self, request: Request) -> int:
        """获取请求大小"""
        try:
            content_length = request.headers.get("content-length")
            return int(content_length) if content_length else 0
        except:
            return 0
    
    def _get_response_size(self, response: Response) -> int:
        """获取响应大小"""
        try:
            content_length = response.headers.get("content-length")
            return int(content_length) if content_length else 0
        except:
            return 0
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        if not self.enabled:
            return await call_next(request)
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取请求信息
        endpoint = request.url.path
        method = request.method
        request_size = self._get_request_size(request)
        
        # 执行请求
        response = await call_next(request)
        
        # 计算响应时间
        response_time = time.time() - start_time
        
        # 获取响应信息
        status_code = response.status_code
        response_size = self._get_response_size(response)
        
        # 记录性能指标
        performance_metrics.record_request(
            endpoint=endpoint,
            method=method,
            response_time=response_time,
            status_code=status_code,
            request_size=request_size,
            response_size=response_size
        )
        
        # 添加性能信息到响应头
        response.headers["X-Response-Time"] = f"{response_time:.3f}s"
        response.headers["X-Request-ID"] = str(int(start_time * 1000000))
        
        return response
