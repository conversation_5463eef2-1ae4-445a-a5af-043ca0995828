import psycopg2
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config import DB_CONFIG

# 修正参数名
MIGRATION_DB_CONFIG = DB_CONFIG.copy()
if 'dbname' in MIGRATION_DB_CONFIG:
    MIGRATION_DB_CONFIG['database'] = MIGRATION_DB_CONFIG.pop('dbname')

SQL_FILE = os.path.join(os.path.dirname(__file__), "001_init_schema.sql")


def run_migration():
    with open(SQL_FILE, "r", encoding="utf-8") as f:
        sql = f.read()
    try:
        conn = psycopg2.connect(**MIGRATION_DB_CONFIG)
        conn.autocommit = True
        cur = conn.cursor()
        cur.execute(sql)
        print("[SUCCESS] 数据库初始化建表成功！")
        cur.close()
        conn.close()
    except Exception as e:
        print(f"[ERROR] 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    run_migration()