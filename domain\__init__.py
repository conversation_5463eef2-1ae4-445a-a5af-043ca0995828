#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域模型模块包

模块描述: 领域驱动设计的核心模块，包含实体、值对象、领域服务等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .entities, .value_objects
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 实体导入
from .entities import (
    Employee, WorkItem, WeeklyReport, AnalysisResult,
    TaskComplexity, TaskCategory, TaskStatus, EmployeeLevel
)

# 值对象导入
from .value_objects import (
    ProcessingContext, VisualizationConfig, FilterCriteria,
    MetricThresholds, AnalysisParameters, TimeRange
)

# 仓储接口导入
from .repositories import (
    IEmployeeRepository, IWorkItemRepository, IWeeklyReportRepository,
    IAnalysisResultRepository, ITagRepository, IAnomalyRepository,
    ITemplateRepository, RepositoryFactory
)

# 领域服务导入
from .services import (
    EmployeeDomainService, WorkItemDomainService,
    WeeklyReportDomainService, AnalyticsDomainService
)

# 领域事件导入
from .events import (
    DomainEvent, EmployeeCreatedEvent, EmployeeUpdatedEvent,
    WeeklyReportSubmittedEvent, WeeklyReportAnalyzedEvent,
    AnomalyDetectedEvent, WorkItemCreatedEvent,
    PerformanceThresholdExceededEvent, IEventHandler, EventBus,
    get_event_bus, publish_event, register_event_handler
)

__all__ = [
    # 实体
    'Employee',
    'WorkItem',
    'WeeklyReport',
    'AnalysisResult',
    # 枚举
    'TaskComplexity',
    'TaskCategory',
    'TaskStatus',
    'EmployeeLevel',
    # 值对象
    'ProcessingContext',
    'VisualizationConfig',
    'FilterCriteria',
    'MetricThresholds',
    'AnalysisParameters',
    'TimeRange',
    # 仓储接口
    'IEmployeeRepository',
    'IWorkItemRepository',
    'IWeeklyReportRepository',
    'IAnalysisResultRepository',
    'ITagRepository',
    'IAnomalyRepository',
    'ITemplateRepository',
    'RepositoryFactory',
    # 领域服务
    'EmployeeDomainService',
    'WorkItemDomainService',
    'WeeklyReportDomainService',
    'AnalyticsDomainService',
    # 领域事件
    'DomainEvent',
    'EmployeeCreatedEvent',
    'EmployeeUpdatedEvent',
    'WeeklyReportSubmittedEvent',
    'WeeklyReportAnalyzedEvent',
    'AnomalyDetectedEvent',
    'WorkItemCreatedEvent',
    'PerformanceThresholdExceededEvent',
    'IEventHandler',
    'EventBus',
    'get_event_bus',
    'publish_event',
    'register_event_handler'
]
