# API服务模块 (api)

## 模块概述

API服务模块是AI驱动邮件周报分析系统的核心接口层，提供RESTful API服务，支持邮件采集、周报分析、数据查询等功能。

## 模块结构

```
api/
├── README.md                    # 模块说明文档
├── main.py                      # FastAPI主应用
├── middleware/                  # 中间件模块
│   ├── __init__.py              # 中间件初始化
│   ├── cache.py                 # 缓存中间件
│   ├── rate_limit.py            # 限流中间件
│   ├── exception_handler.py     # 异常处理中间件
│   └── performance.py           # 性能监控中间件
├── endpoints/                   # API端点
│   ├── __init__.py              # 端点初始化
│   ├── email.py                 # 邮件相关API
│   ├── report.py                # 周报分析API
│   ├── staff.py                 # 员工管理API
│   ├── template.py              # 模板管理API
│   ├── tag.py                   # 标签管理API
│   └── anomaly.py               # 异常检测API
├── models/                      # 数据模型
│   ├── __init__.py              # 模型初始化
│   ├── request.py               # 请求模型
│   ├── response.py              # 响应模型
│   └── common.py                # 通用模型
└── utils/                       # 工具模块
    ├── __init__.py              # 工具初始化
    ├── cache_manager.py         # 缓存管理器
    ├── rate_limiter.py          # 限流器
    └── performance_monitor.py   # 性能监控器
```

## 核心功能

### 1. 邮件采集API (email.py)
- **邮件获取**：支持IMAP邮件采集
- **过滤采集**：只采集人员表中存在的发件人邮件
- **附件下载**：自动下载和处理邮件附件
- **状态查询**：查询邮件采集状态和统计信息

### 2. 周报分析API (report.py)
- **智能分析**：AI驱动的周报内容分析
- **数据查询**：支持多条件查询和过滤
- **结果存储**：分析结果自动入库
- **标签管理**：智能标签分配和管理

### 3. 系统监控API
- **健康检查**：系统健康状态监控
- **性能指标**：Prometheus指标收集
- **请求统计**：API请求次数和耗时统计

## 系统优化特性

### 接口性能优化
- **智能缓存**：多级缓存策略，支持内存和Redis缓存
- **数据压缩**：响应数据自动压缩，减少传输量
- **分页优化**：大数据量查询自动分页处理
- **查询优化**：数据库查询优化和索引使用

### 并发处理增强
- **请求限流**：基于IP和用户的智能限流
- **线程池优化**：异步处理和线程池管理
- **连接池管理**：数据库连接池优化
- **负载均衡**：支持多实例负载均衡

### 异常处理完善
- **全局异常处理**：统一异常处理机制
- **错误码体系**：标准化错误码和错误信息
- **异常监控**：异常情况实时监控和告警
- **优雅降级**：服务异常时的优雅降级处理

## API接口规范

### 1. 请求格式
```json
{
    "method": "POST",
    "url": "/api/report/analyze",
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer <token>"
    },
    "body": {
        "report_text": "周报内容...",
        "department": "技术部",
        "role": "开发工程师"
    }
}
```

### 2. 响应格式
```json
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
        "report_id": "r001",
        "analysis_result": {...}
    },
    "timestamp": "2024-05-25T10:30:00Z"
}
```

### 3. 错误响应格式
```json
{
    "success": false,
    "code": 400,
    "message": "请求参数错误",
    "error": {
        "type": "ValidationError",
        "details": "report_text字段不能为空"
    },
    "timestamp": "2024-05-25T10:30:00Z"
}
```

## 性能指标

### 目标性能
- **响应时间**：平均响应时间 < 200ms
- **并发处理**：支持1000+并发请求
- **缓存命中率**：> 80%
- **错误率**：< 0.1%

### 监控指标
- **QPS**：每秒查询数
- **响应时间分布**：P50, P95, P99响应时间
- **错误率统计**：按接口和错误类型统计
- **缓存性能**：命中率、穿透率、更新频率

## 缓存策略

### 1. 多级缓存
- **L1缓存**：内存缓存，存储热点数据
- **L2缓存**：Redis缓存，存储共享数据
- **L3缓存**：数据库查询结果缓存

### 2. 缓存策略
- **查询结果缓存**：周报查询结果缓存30分钟
- **分析结果缓存**：AI分析结果缓存24小时
- **统计数据缓存**：统计数据缓存1小时
- **配置数据缓存**：系统配置缓存永久有效

### 3. 缓存更新
- **主动更新**：数据变更时主动清除相关缓存
- **定时更新**：定时刷新长期缓存数据
- **懒加载**：缓存失效时懒加载最新数据

## 限流策略

### 1. 限流规则
- **全局限流**：系统总QPS限制
- **接口限流**：单个接口QPS限制
- **用户限流**：单用户请求频率限制
- **IP限流**：单IP请求频率限制

### 2. 限流算法
- **令牌桶算法**：平滑限流，允许突发流量
- **滑动窗口**：精确控制时间窗口内的请求数
- **漏桶算法**：恒定速率处理请求

### 3. 限流响应
- **快速失败**：超出限制时立即返回错误
- **排队等待**：请求排队等待处理
- **优雅降级**：降级到简化功能

## 使用示例

### 1. 邮件采集
```python
import requests

# 过滤邮件采集
response = requests.post("/api/email/filter", json={
    "days": 7,
    "max_emails": 50,
    "mailbox": "INBOX"
})

print(response.json())
```

### 2. 周报分析
```python
# 周报分析
response = requests.post("/api/report/analyze", json={
    "report_text": "本周完成了项目开发...",
    "department": "技术部",
    "role": "开发工程师"
})

print(response.json())
```

### 3. 数据查询
```python
# 查询周报
response = requests.get("/api/report/query", params={
    "department": "技术部",
    "week": "2024-W21",
    "limit": 10
})

print(response.json())
```

## 部署配置

### 1. 环境变量
```bash
# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60
```

### 2. 启动命令
```bash
# 开发环境
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境
gunicorn api.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 测试覆盖

### 1. 单元测试
- API端点功能测试
- 中间件功能测试
- 缓存机制测试
- 限流机制测试

### 2. 集成测试
- 端到端API测试
- 数据库集成测试
- 缓存集成测试
- 性能压力测试

### 3. 性能测试
- 并发性能测试
- 缓存性能测试
- 限流效果测试
- 内存使用测试

## 注意事项

1. **安全性**：确保API接口的安全性和权限控制
2. **性能监控**：持续监控API性能指标
3. **错误处理**：完善的错误处理和日志记录
4. **版本管理**：API版本管理和向后兼容

## 更新日志

### [2024-05-25] API服务优化
- 实现接口级缓存机制
- 增强并发处理能力
- 完善全局异常处理
- 添加性能监控和限流功能

### [2024-05-25] 模块文档规范化
- 创建模块README文档
- 规范化API接口文档
- 添加使用示例和部署指南
