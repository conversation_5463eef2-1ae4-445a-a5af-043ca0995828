# 数据库模块 (db)

## 模块概述

数据库模块负责系统的数据存储、管理和优化，提供高性能、高可靠性的数据访问服务。

## 模块结构

```
db/
├── README.md                    # 模块说明文档
├── __init__.py                  # 模块初始化
├── orm.py                       # ORM模型定义（已增强）
├── fix_email_case_and_uniqueness.py  # 邮箱规范化脚本
└── migrations/                  # 数据库迁移脚本
    ├── __init__.py              # 迁移模块初始化
    ├── init_db.py               # 数据库初始化脚本
    ├── 001_init_schema.sql      # 初始化Schema
    ├── 002_add_email_type.sql   # 添加邮件类型
    └── 003_fix_email_attachments.sql  # 修复邮件附件
```

## 核心功能

### 1. ORM模型 (orm.py)
- **智能数据库连接**：支持PostgreSQL和SQLite自动切换
- **连接池管理**：优化数据库连接池配置
- **模型定义**：完整的数据模型定义和关系映射
- **事务管理**：完善的事务处理和回滚机制

### 2. 数据模型

#### 主要表结构
- **weekly_report_analysis**：周报分析主表
- **task_items**：任务明细表
- **staff_info**：员工信息表
- **department_dict**：部门字典表
- **product_dict**：产品字典表
- **analysis_templates**：分析模板表
- **email_fetch_tasks**：邮件采集任务表

### 3. 数据库迁移
- **版本控制**：数据库Schema版本控制
- **自动迁移**：支持自动执行迁移脚本
- **回滚支持**：支持迁移回滚操作
- **数据完整性**：确保迁移过程中的数据完整性

## 系统优化特性

### 索引优化
- 分析查询模式，创建针对性复合索引
- 优化现有索引，移除低效索引
- 实现自动索引优化，根据查询统计动态调整

### 查询优化
- 重构复杂查询，拆分为多个简单查询
- 实现查询结果缓存，减少重复查询
- 优化ORM映射，减少不必要的字段加载

### 连接池优化
- 实现动态连接池，根据负载自动调整连接数
- 优化连接回收策略，及时释放空闲连接
- 增加连接健康检查，主动替换异常连接

## 数据库配置

### 连接配置
```python
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "dbname": "zkteco",
    "user": "postgres",
    "password": "postgres",
    # SQLite备选配置
    "sqlite_path": "zkteco.db",
    "use_sqlite": True,  # 当PostgreSQL不可用时自动切换
}
```

### 连接池配置
```python
ENGINE_CONFIG = {
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True
}
```

## 使用示例

### 基本数据操作
```python
from db.orm import SessionLocal, WeeklyReportAnalysis, save_report_analysis

# 保存分析结果
result = {
    "report_id": "r001",
    "employee": {"name": "张三", "email": "<EMAIL>"},
    "week": "2024-W21",
    "metrics": {"total_hours": 40},
    # ... 其他字段
}

save_report_analysis(result)
```

### 查询数据
```python
from db.orm import query_report_analysis

# 查询特定部门的报告
reports = query_report_analysis(
    department="技术部",
    week="2024-W21"
)

for report in reports:
    print(f"员工: {report['employee_name']}, 工时: {report['total_hours']}")
```

### 使用会话管理
```python
from db.orm import SessionLocal, WeeklyReportAnalysis

with SessionLocal() as session:
    # 查询数据
    reports = session.query(WeeklyReportAnalysis).filter(
        WeeklyReportAnalysis.department == "技术部"
    ).all()
    
    # 更新数据
    for report in reports:
        report.updated_at = datetime.now()
    
    session.commit()
```

## 数据库Schema

### 主表结构
```sql
-- 周报分析主表
CREATE TABLE weekly_report_analysis (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR UNIQUE NOT NULL,
    employee_name VARCHAR,
    employee_email VARCHAR NOT NULL,
    department VARCHAR,
    role VARCHAR,
    week VARCHAR,
    work_items JSON,
    summary JSON,
    metrics JSON,
    tags JSON,
    anomaly_flags JSON,
    total_hours FLOAT,
    task_count INTEGER,
    saturation FLOAT,
    saturation_tag VARCHAR,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 任务明细表
CREATE TABLE task_items (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR REFERENCES weekly_report_analysis(report_id) ON DELETE CASCADE,
    title VARCHAR,
    description TEXT,
    duration_hours FLOAT,
    complexity VARCHAR,
    category VARCHAR,
    date DATE,
    extra JSON,
    tags JSON,
    anomaly_flags JSON
);
```

### 索引设计
```sql
-- 常用查询索引
CREATE INDEX idx_weekly_report_email ON weekly_report_analysis(employee_email);
CREATE INDEX idx_weekly_report_department ON weekly_report_analysis(department);
CREATE INDEX idx_weekly_report_week ON weekly_report_analysis(week);
CREATE INDEX idx_weekly_report_created_at ON weekly_report_analysis(created_at);
CREATE INDEX idx_task_items_report_id ON task_items(report_id);
CREATE INDEX idx_task_items_category ON task_items(category);
```

## 性能指标

### 目标性能
- **数据库查询响应**：平均50ms
- **连接池利用率**：70-80%
- **索引命中率**：>95%
- **事务成功率**：>99.9%

### 监控指标
- 查询执行时间
- 连接池状态
- 索引使用情况
- 死锁和阻塞情况

## 数据完整性

### 约束规则
- **主键约束**：确保记录唯一性
- **外键约束**：维护表间关系完整性
- **唯一约束**：防止重复数据
- **检查约束**：确保数据有效性

### 数据验证
```python
def validate_report_data(data):
    """验证报告数据完整性"""
    required_fields = ['report_id', 'employee', 'week']
    for field in required_fields:
        if field not in data:
            raise ValueError(f"缺少必需字段: {field}")
    
    # 邮箱格式验证
    email = data.get('employee', {}).get('email', '')
    if not email or '@' not in email:
        raise ValueError("无效的邮箱格式")
    
    return True
```

## 备份与恢复

### 备份策略
- **全量备份**：每日凌晨执行全量备份
- **增量备份**：每小时执行增量备份
- **事务日志备份**：实时备份事务日志

### 恢复流程
1. 停止应用服务
2. 恢复数据库备份
3. 应用事务日志
4. 验证数据完整性
5. 重启应用服务

## 测试覆盖

### 单元测试
- ORM模型测试
- 数据验证测试
- 连接池测试
- 迁移脚本测试

### 集成测试
- 数据库连接测试
- 事务处理测试
- 性能压力测试
- 数据一致性测试

## 注意事项

1. **数据安全**：确保敏感数据的加密存储
2. **性能监控**：定期监控数据库性能指标
3. **容量规划**：根据业务增长规划存储容量
4. **备份验证**：定期验证备份文件的完整性

## 更新日志

### [2024-05-25] 数据库连接优化
- 增强ORM模块，支持PostgreSQL和SQLite自动切换
- 实现智能数据库引擎创建和连接管理
- 添加连接健康检查和异常处理
- 优化连接池配置和性能监控

### [2024-05-25] 模块文档规范化
- 创建模块README文档
- 规范化目录结构说明
- 添加使用示例和配置说明
