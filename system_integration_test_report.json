{"test_time": "2025-05-25T23:54:16.465087", "test_results": {"ai_analyzer": "通过", "data_processing": "通过", "system_integration": "通过"}, "statistics": {"total_reports": 2, "total_work_items": 6, "total_hours": 23.0, "departments": {"技术支持部": {"count": 1, "hours": 4.0}, "研发部": {"count": 1, "hours": 19.0}}, "top_tags": {"创新": 2, "技术": 2, "学习": 2, "部门-技术支持部": 1, "岗位-技术支持工程师": 1, "客户": 1, "部门-研发部": 1, "质量": 1, "岗位-开发工程师": 1}}, "sample_results": [{"report_id": "report_20250525235416_28f85602", "employee": {"name": "李技术", "email": "<EMAIL>", "department": "技术支持部", "role": "技术支持工程师"}, "week": "2025-W21", "work_items": [{"title": "本周主要工作：", "description": "本周主要工作：", "duration_hours": 1.0, "complexity": "中", "category": "其他", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}, {"title": "2. 参与产品培训，耗时2小时，学习新功能", "description": "2. 参与产品培训，耗时2小时，学习新功能", "duration_hours": 2.0, "complexity": "中", "category": "开发", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}, {"title": "5. 团队会议，耗时1小时，讨论下周计划", "description": "5. 团队会议，耗时1小时，讨论下周计划", "duration_hours": 1.0, "complexity": "中", "category": "会议", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}], "summary": {"total_hours": 13.5, "main_achievements": "完成了多项工作任务", "risks": "无明显风险", "suggestions": "继续保持良好的工作状态"}, "metrics": {"total_hours": 4.0, "task_count": 3, "saturation": 0.1, "saturation_tag": "不足"}, "tags": ["部门-技术支持部", "岗位-技术支持工程师", "创新", "技术", "客户", "学习"], "anomaly_flags": ["包含问题描述"], "innovation_analysis": "暂未发现明显创新点", "quality_analysis": "品质相关信息较少", "trend_analysis": "基于单次分析，暂无趋势数据", "ai_version": "simple_v1.0", "raw_text": "\n                本周主要工作：\n                1. 处理客户A的技术问题，耗时4小时，问题已解决\n                2. 参与产品培训，耗时2小时，学习新功能\n                3. 编写技术文档，耗时3小时，完成用户手册\n                4. 客户现场支持，耗时8小时，解决系统集成问题\n                5. 团队会议，耗时1小时，讨论下周计划\n                总计工时：18小时\n                "}, {"report_id": "report_20250525235416_dcaad52e", "employee": {"name": "张开发", "email": "<EMAIL>", "department": "研发部", "role": "开发工程师"}, "week": "2025-W21", "work_items": [{"title": "本周开发任务：", "description": "本周开发任务：", "duration_hours": 1.0, "complexity": "中", "category": "开发", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}, {"title": "1. 新功能模块开发，耗时12小时，完成度80%", "description": "1. 新功能模块开发，耗时12小时，完成度80%", "duration_hours": 12.0, "complexity": "中", "category": "开发", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}, {"title": "3. 单元测试编写，耗时6小时", "description": "3. 单元测试编写，耗时6小时", "duration_hours": 6.0, "complexity": "中", "category": "测试", "date": "2025-05-25", "extra": {}, "tags": [], "anomaly_flags": []}], "summary": {"total_hours": 13.5, "main_achievements": "完成了多项工作任务", "risks": "无明显风险", "suggestions": "继续保持良好的工作状态"}, "metrics": {"total_hours": 19.0, "task_count": 3, "saturation": 0.475, "saturation_tag": "不足"}, "tags": ["部门-研发部", "质量", "创新", "技术", "岗位-开发工程师", "学习"], "anomaly_flags": ["包含问题描述"], "innovation_analysis": "发现创新点：优化", "quality_analysis": "关注品质方面：测试", "trend_analysis": "基于单次分析，暂无趋势数据", "ai_version": "simple_v1.0", "raw_text": "\n                本周开发任务：\n                1. 新功能模块开发，耗时12小时，完成度80%\n                2. 代码审查和优化，耗时4小时\n                3. 单元测试编写，耗时6小时\n                4. 技术调研，耗时3小时，研究新技术方案\n                5. Bug修复，耗时3小时，解决5个问题\n                总工时：28小时\n                "}]}