#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域事件模块

模块描述: 定义领域事件和事件处理机制，支持事件驱动架构
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: abc, typing, datetime, dataclasses
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime
from dataclasses import dataclass, field
import logging
import uuid


@dataclass
class DomainEvent(ABC):
    """领域事件基类"""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    occurred_at: datetime = field(default_factory=datetime.now)
    event_type: str = field(init=False)
    version: int = 1
    
    def __post_init__(self):
        self.event_type = self.__class__.__name__


@dataclass
class EmployeeCreatedEvent(DomainEvent):
    """员工创建事件"""
    employee_email: str
    employee_name: str
    department: str
    role: str


@dataclass
class EmployeeUpdatedEvent(DomainEvent):
    """员工更新事件"""
    employee_email: str
    changed_fields: Dict[str, Any]
    old_values: Dict[str, Any]
    new_values: Dict[str, Any]


@dataclass
class WeeklyReportSubmittedEvent(DomainEvent):
    """周报提交事件"""
    report_id: str
    employee_email: str
    week: str
    work_item_count: int
    total_hours: float


@dataclass
class WeeklyReportAnalyzedEvent(DomainEvent):
    """周报分析完成事件"""
    report_id: str
    employee_email: str
    analysis_results: Dict[str, Any]
    anomalies_detected: List[Dict[str, Any]]
    analysis_version: str


@dataclass
class AnomalyDetectedEvent(DomainEvent):
    """异常检测事件"""
    report_id: str
    employee_email: str
    anomaly_type: str
    severity: str
    description: str
    metrics: Dict[str, Any]


@dataclass
class WorkItemCreatedEvent(DomainEvent):
    """工作项创建事件"""
    work_item_id: str
    employee_email: str
    title: str
    category: str
    complexity: str
    duration_hours: float


@dataclass
class PerformanceThresholdExceededEvent(DomainEvent):
    """性能阈值超出事件"""
    employee_email: str
    metric_name: str
    current_value: float
    threshold_value: float
    severity: str


class IEventHandler(ABC):
    """事件处理器接口"""
    
    @abstractmethod
    def handle(self, event: DomainEvent) -> None:
        """处理事件"""
        pass
    
    @abstractmethod
    def can_handle(self, event_type: str) -> bool:
        """检查是否能处理指定类型的事件"""
        pass


class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._handlers: Dict[str, List[IEventHandler]] = {}
        self._event_store: List[DomainEvent] = []
        self.logger = logging.getLogger(__name__)
    
    def register_handler(self, event_type: str, handler: IEventHandler) -> None:
        """注册事件处理器"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        if handler not in self._handlers[event_type]:
            self._handlers[event_type].append(handler)
            self.logger.info(f"注册事件处理器: {event_type} -> {handler.__class__.__name__}")
    
    def unregister_handler(self, event_type: str, handler: IEventHandler) -> None:
        """注销事件处理器"""
        if event_type in self._handlers and handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            self.logger.info(f"注销事件处理器: {event_type} -> {handler.__class__.__name__}")
    
    def publish(self, event: DomainEvent) -> None:
        """发布事件"""
        # 存储事件
        self._event_store.append(event)
        
        # 分发事件
        event_type = event.event_type
        if event_type in self._handlers:
            for handler in self._handlers[event_type]:
                try:
                    handler.handle(event)
                    self.logger.debug(f"事件处理成功: {event_type} by {handler.__class__.__name__}")
                except Exception as e:
                    self.logger.error(f"事件处理失败: {event_type} by {handler.__class__.__name__}, 错误: {e}")
        
        self.logger.info(f"事件已发布: {event_type} (ID: {event.event_id})")
    
    def get_events(self, event_type: Optional[str] = None, 
                   since: Optional[datetime] = None) -> List[DomainEvent]:
        """获取事件"""
        events = self._event_store
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if since:
            events = [e for e in events if e.occurred_at >= since]
        
        return events
    
    def clear_events(self) -> None:
        """清空事件存储"""
        self._event_store.clear()
        self.logger.info("事件存储已清空")


# 具体事件处理器示例
class AnomalyNotificationHandler(IEventHandler):
    """异常通知处理器"""
    
    def __init__(self, notification_service=None):
        self.notification_service = notification_service
        self.logger = logging.getLogger(__name__)
    
    def can_handle(self, event_type: str) -> bool:
        return event_type == 'AnomalyDetectedEvent'
    
    def handle(self, event: DomainEvent) -> None:
        if isinstance(event, AnomalyDetectedEvent):
            self._send_anomaly_notification(event)
    
    def _send_anomaly_notification(self, event: AnomalyDetectedEvent) -> None:
        """发送异常通知"""
        message = f"检测到异常: {event.description} (员工: {event.employee_email}, 严重程度: {event.severity})"
        self.logger.warning(message)
        
        # 这里可以集成实际的通知服务
        if self.notification_service:
            self.notification_service.send_notification(
                recipient=event.employee_email,
                subject="工作异常提醒",
                message=message,
                severity=event.severity
            )


class PerformanceMetricsHandler(IEventHandler):
    """性能指标处理器"""
    
    def __init__(self, metrics_service=None):
        self.metrics_service = metrics_service
        self.logger = logging.getLogger(__name__)
    
    def can_handle(self, event_type: str) -> bool:
        return event_type in ['WeeklyReportAnalyzedEvent', 'PerformanceThresholdExceededEvent']
    
    def handle(self, event: DomainEvent) -> None:
        if isinstance(event, WeeklyReportAnalyzedEvent):
            self._update_performance_metrics(event)
        elif isinstance(event, PerformanceThresholdExceededEvent):
            self._handle_threshold_exceeded(event)
    
    def _update_performance_metrics(self, event: WeeklyReportAnalyzedEvent) -> None:
        """更新性能指标"""
        self.logger.info(f"更新性能指标: {event.employee_email}")
        
        if self.metrics_service:
            self.metrics_service.update_employee_metrics(
                employee_email=event.employee_email,
                metrics=event.analysis_results
            )
    
    def _handle_threshold_exceeded(self, event: PerformanceThresholdExceededEvent) -> None:
        """处理阈值超出"""
        self.logger.warning(f"性能阈值超出: {event.employee_email} - {event.metric_name}")
        
        if self.metrics_service:
            self.metrics_service.record_threshold_violation(
                employee_email=event.employee_email,
                metric_name=event.metric_name,
                current_value=event.current_value,
                threshold_value=event.threshold_value,
                severity=event.severity
            )


class AuditLogHandler(IEventHandler):
    """审计日志处理器"""
    
    def __init__(self, audit_service=None):
        self.audit_service = audit_service
        self.logger = logging.getLogger(__name__)
    
    def can_handle(self, event_type: str) -> bool:
        # 记录所有事件
        return True
    
    def handle(self, event: DomainEvent) -> None:
        """记录审计日志"""
        audit_entry = {
            'event_id': event.event_id,
            'event_type': event.event_type,
            'occurred_at': event.occurred_at.isoformat(),
            'event_data': self._serialize_event(event)
        }
        
        self.logger.info(f"审计日志: {event.event_type}")
        
        if self.audit_service:
            self.audit_service.log_event(audit_entry)
    
    def _serialize_event(self, event: DomainEvent) -> Dict[str, Any]:
        """序列化事件数据"""
        # 简单的序列化实现
        result = {}
        for key, value in event.__dict__.items():
            if isinstance(value, (str, int, float, bool, type(None))):
                result[key] = value
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = str(value)
        return result


# 全局事件总线实例
_event_bus = EventBus()


def get_event_bus() -> EventBus:
    """获取全局事件总线"""
    return _event_bus


def publish_event(event: DomainEvent) -> None:
    """发布事件到全局事件总线"""
    _event_bus.publish(event)


def register_event_handler(event_type: str, handler: IEventHandler) -> None:
    """注册事件处理器到全局事件总线"""
    _event_bus.register_handler(event_type, handler)
