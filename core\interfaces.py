#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心接口定义模块

模块描述: 定义系统中各种组件和服务的标准接口
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: abc, typing
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union

from .types import (
    AnalysisResult, AnalysisType, ProcessingContext, 
    VisualizationConfig, FilterCriteria, ServiceResponse
)

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

class IAnalyzer(ABC):
    """
    分析器接口
    
    定义所有分析组件必须实现的标准接口
    """
    
    @abstractmethod
    def analyze(self, data: Any, context: ProcessingContext) -> AnalysisResult:
        """
        执行分析
        
        Args:
            data: 要分析的数据
            context: 处理上下文
            
        Returns:
            AnalysisResult: 分析结果
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[AnalysisType]:
        """
        获取支持的分析类型
        
        Returns:
            List[AnalysisType]: 支持的分析类型列表
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取分析器名称
        
        Returns:
            str: 分析器名称
        """
        pass
    
    @abstractmethod
    def validate_input(self, data: Any) -> bool:
        """
        验证输入数据
        
        Args:
            data: 输入数据
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    def get_version(self) -> str:
        """
        获取分析器版本
        
        Returns:
            str: 版本号
        """
        return "1.0.0"
    
    def get_description(self) -> str:
        """
        获取分析器描述
        
        Returns:
            str: 描述信息
        """
        return f"{self.get_name()} 分析器"

class IVisualizer(ABC):
    """
    可视化器接口
    
    定义所有可视化组件必须实现的标准接口
    """
    
    @abstractmethod
    def create_visualization(self, data: Any, config: VisualizationConfig) -> Any:
        """
        创建可视化
        
        Args:
            data: 要可视化的数据
            config: 可视化配置
            
        Returns:
            Any: 可视化对象
        """
        pass
    
    @abstractmethod
    def get_supported_chart_types(self) -> List[str]:
        """
        获取支持的图表类型
        
        Returns:
            List[str]: 支持的图表类型列表
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取可视化器名称
        
        Returns:
            str: 可视化器名称
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: Any, chart_type: str) -> bool:
        """
        验证数据是否适合指定的图表类型
        
        Args:
            data: 数据
            chart_type: 图表类型
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    def export_chart(self, chart: Any, format: str, filename: str) -> bool:
        """
        导出图表
        
        Args:
            chart: 图表对象
            format: 导出格式
            filename: 文件名
            
        Returns:
            bool: 导出是否成功
        """
        # 默认实现，子类可重写
        return False

class IDataProcessor(ABC):
    """
    数据处理器接口
    
    定义数据处理组件的标准接口
    """
    
    @abstractmethod
    def process(self, data: Any, context: ProcessingContext) -> Any:
        """
        处理数据
        
        Args:
            data: 要处理的数据
            context: 处理上下文
            
        Returns:
            Any: 处理后的数据
        """
        pass
    
    @abstractmethod
    def validate(self, data: Any) -> bool:
        """
        验证数据
        
        Args:
            data: 要验证的数据
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的数据格式
        
        Returns:
            List[str]: 支持的格式列表
        """
        pass
    
    def preprocess(self, data: Any) -> Any:
        """
        预处理数据
        
        Args:
            data: 原始数据
            
        Returns:
            Any: 预处理后的数据
        """
        # 默认实现，直接返回原数据
        return data
    
    def postprocess(self, data: Any) -> Any:
        """
        后处理数据
        
        Args:
            data: 处理后的数据
            
        Returns:
            Any: 最终数据
        """
        # 默认实现，直接返回数据
        return data

class ICacheManager(ABC):
    """
    缓存管理器接口
    
    定义缓存操作的标准接口
    """
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示永不过期
            
        Returns:
            bool: 设置是否成功
        """
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 删除是否成功
        """
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 缓存是否存在
        """
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            bool: 清空是否成功
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {}

class IEventBus(ABC):
    """
    事件总线接口
    
    支持组件间解耦通信
    """
    
    @abstractmethod
    def publish(self, event_type: str, data: Any, metadata: Dict[str, Any] = None) -> None:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            metadata: 元数据
        """
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, handler: callable) -> str:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理器
            
        Returns:
            str: 订阅ID
        """
        pass
    
    @abstractmethod
    def unsubscribe(self, subscription_id: str) -> bool:
        """
        取消订阅
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            bool: 取消是否成功
        """
        pass
    
    def get_subscribers(self, event_type: str) -> List[str]:
        """
        获取事件订阅者
        
        Args:
            event_type: 事件类型
            
        Returns:
            List[str]: 订阅者ID列表
        """
        return []

class IConfigManager(ABC):
    """
    配置管理器接口
    
    统一配置管理
    """
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        pass
    
    @abstractmethod
    def reload(self) -> bool:
        """
        重新加载配置
        
        Returns:
            bool: 重新加载是否成功
        """
        pass
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            Dict[str, Any]: 所有配置
        """
        return {}

class IMetricsCollector(ABC):
    """
    指标收集器接口
    
    收集和报告系统指标
    """
    
    @abstractmethod
    def record_metric(self, name: str, value: Union[int, float], tags: Dict[str, str] = None) -> None:
        """
        记录指标
        
        Args:
            name: 指标名称
            value: 指标值
            tags: 标签
        """
        pass
    
    @abstractmethod
    def increment_counter(self, name: str, tags: Dict[str, str] = None) -> None:
        """
        递增计数器
        
        Args:
            name: 计数器名称
            tags: 标签
        """
        pass
    
    @abstractmethod
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取所有指标
        
        Returns:
            Dict[str, Any]: 指标数据
        """
        pass
