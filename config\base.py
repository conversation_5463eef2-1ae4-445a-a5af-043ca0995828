#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础配置模块

模块描述: 定义系统配置的基础类和通用配置项
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: abc, dataclasses, os, typing
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import os

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    database: str = "zkteci"
    username: str = "postgres"
    password: str = "123456"
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False
    connect_timeout: int = 30
    
    def get_connection_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def get_async_connection_url(self) -> str:
        """获取异步数据库连接URL"""
        return f"postgresql+asyncpg://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

@dataclass
class AIConfig:
    """AI服务配置"""
    base_url: str = "http://localhost:11434"
    model_name: str = "qwen2.5:7b"
    embedding_model: str = "nomic-embed-text"
    timeout: int = 300
    max_retries: int = 3
    temperature: float = 0.7
    max_tokens: int = 4096
    top_p: float = 0.9
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

@dataclass
class UIConfig:
    """UI组件配置"""
    theme: str = "light"
    page_title: str = "AI驱动邮件周报分析系统"
    page_icon: str = "📊"
    layout: str = "wide"
    sidebar_state: str = "expanded"
    primary_color: str = "#1f77b4"
    background_color: str = "#ffffff"
    text_color: str = "#262730"
    font_family: str = "sans-serif"
    
    def get_streamlit_config(self) -> Dict[str, Any]:
        """获取Streamlit配置"""
        return {
            "page_title": self.page_title,
            "page_icon": self.page_icon,
            "layout": self.layout,
            "initial_sidebar_state": self.sidebar_state
        }

@dataclass
class CacheConfig:
    """缓存配置"""
    redis_url: str = "redis://localhost:6379/0"
    default_ttl: int = 3600
    max_connections: int = 10
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    retry_on_timeout: bool = True
    health_check_interval: int = 30
    
    def get_redis_config(self) -> Dict[str, Any]:
        """获取Redis配置"""
        return {
            "url": self.redis_url,
            "max_connections": self.max_connections,
            "socket_timeout": self.socket_timeout,
            "socket_connect_timeout": self.socket_connect_timeout,
            "retry_on_timeout": self.retry_on_timeout,
            "health_check_interval": self.health_check_interval
        }

@dataclass
class EmailConfig:
    """邮件配置"""
    host: str = "smtp.example.com"
    port: int = 587
    username: str = "<EMAIL>"
    password: str = "your_password"
    use_tls: bool = True
    use_ssl: bool = False
    timeout: int = 30
    
    def get_smtp_config(self) -> Dict[str, Any]:
        """获取SMTP配置"""
        return {
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "use_tls": self.use_tls,
            "use_ssl": self.use_ssl,
            "timeout": self.timeout
        }

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    log_dir: str = "logs"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 30
    console_output: bool = True
    file_output: bool = True
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": self.format,
                    "datefmt": self.date_format
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": self.level,
                    "formatter": "default",
                    "stream": "ext://sys.stdout"
                },
                "file": {
                    "class": "logging.handlers.TimedRotatingFileHandler",
                    "level": self.level,
                    "formatter": "default",
                    "filename": f"{self.log_dir}/app.log",
                    "when": "midnight",
                    "interval": 1,
                    "backupCount": self.backup_count,
                    "encoding": "utf-8"
                }
            },
            "root": {
                "level": self.level,
                "handlers": ["console", "file"] if self.file_output else ["console"]
            }
        }

@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = "your-secret-key-change-in-production"
    session_timeout: int = 3600
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration: int = 900  # 15分钟
    csrf_protection: bool = True
    secure_cookies: bool = True
    
    def get_security_headers(self) -> Dict[str, str]:
        """获取安全头部"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }

class BaseConfig(ABC):
    """配置基类"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.ai = AIConfig()
        self.ui = UIConfig()
        self.cache = CacheConfig()
        self.email = EmailConfig()
        self.logging = LoggingConfig()
        self.security = SecurityConfig()
        self.environment = os.getenv('ENVIRONMENT', 'development')
        self.debug = False
        self.testing = False
        
        # 加载环境特定配置
        self.load_environment_specific()
        
        # 从环境变量加载敏感配置
        self.load_from_environment()
    
    @abstractmethod
    def load_environment_specific(self) -> None:
        """加载环境特定配置"""
        pass
    
    def load_from_environment(self) -> None:
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv('DB_HOST'):
            self.database.host = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            self.database.port = int(os.getenv('DB_PORT'))
        if os.getenv('DB_NAME'):
            self.database.database = os.getenv('DB_NAME')
        if os.getenv('DB_USER'):
            self.database.username = os.getenv('DB_USER')
        if os.getenv('DB_PASSWORD'):
            self.database.password = os.getenv('DB_PASSWORD')
        
        # AI配置
        if os.getenv('AI_BASE_URL'):
            self.ai.base_url = os.getenv('AI_BASE_URL')
        if os.getenv('AI_MODEL_NAME'):
            self.ai.model_name = os.getenv('AI_MODEL_NAME')
        
        # 缓存配置
        if os.getenv('REDIS_URL'):
            self.cache.redis_url = os.getenv('REDIS_URL')
        
        # 邮件配置
        if os.getenv('EMAIL_HOST'):
            self.email.host = os.getenv('EMAIL_HOST')
        if os.getenv('EMAIL_PORT'):
            self.email.port = int(os.getenv('EMAIL_PORT'))
        if os.getenv('EMAIL_USER'):
            self.email.username = os.getenv('EMAIL_USER')
        if os.getenv('EMAIL_PASSWORD'):
            self.email.password = os.getenv('EMAIL_PASSWORD')
        
        # 安全配置
        if os.getenv('SECRET_KEY'):
            self.security.secret_key = os.getenv('SECRET_KEY')
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            'database': {
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database,
                'username': self.database.username,
                # 不包含密码等敏感信息
            },
            'ai': {
                'base_url': self.ai.base_url,
                'model_name': self.ai.model_name,
                'timeout': self.ai.timeout,
                'temperature': self.ai.temperature
            },
            'ui': {
                'theme': self.ui.theme,
                'page_title': self.ui.page_title,
                'layout': self.ui.layout
            },
            'environment': self.environment,
            'debug': self.debug,
            'testing': self.testing
        }
    
    def validate_config(self) -> bool:
        """验证配置"""
        try:
            # 验证数据库配置
            if not self.database.host or not self.database.database:
                return False
            
            # 验证AI配置
            if not self.ai.base_url or not self.ai.model_name:
                return False
            
            # 验证UI配置
            if not self.ui.page_title:
                return False
            
            return True
        except Exception:
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(environment={self.environment}, debug={self.debug})"
    
    def __repr__(self) -> str:
        """开发者表示"""
        return f"BaseConfig(environment='{self.environment}', debug={self.debug}, testing={self.testing})"
