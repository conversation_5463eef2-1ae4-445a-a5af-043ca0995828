#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
员工数据仓储

模块描述: 员工数据的持久化操作，包括CRUD和查询功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.base, domain.entities
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from core.base import BaseRepository
from core.decorators import performance_monitor, error_handler
from core.exceptions import DataAccessError
from domain.entities import Employee, EmployeeLevel


class EmployeeRepository(BaseRepository):
    """
    员工数据仓储
    
    功能：
    - 员工的CRUD操作
    - 按部门、角色查询
    - 技能管理
    - 层级关系管理
    """
    
    def __init__(self, config, database_session=None):
        """
        初始化员工仓储
        
        Args:
            config: 仓储配置
            database_session: 数据库会话
        """
        super().__init__(config)
        self.db_session = database_session
        self._employees_cache: Dict[str, Employee] = {}
        
        # 模拟数据存储（实际应该连接数据库）
        self._employees_storage: Dict[str, Dict[str, Any]] = {}
        
        # 初始化一些测试数据
        self._initialize_test_data()
    
    def initialize(self) -> bool:
        """初始化仓储"""
        try:
            # 这里应该初始化数据库连接、创建表等
            self.logger.info("员工仓储初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"员工仓储初始化失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True
    
    @performance_monitor
    @error_handler(reraise=True)
    def create(self, employee: Employee) -> Employee:
        """
        创建员工
        
        Args:
            employee: 员工对象
            
        Returns:
            Employee: 创建后的员工对象
        """
        try:
            if employee.email in self._employees_storage:
                raise DataAccessError(f"员工已存在: {employee.email}")
            
            # 序列化员工数据
            employee_data = self._serialize_employee(employee)
            
            # 存储到模拟存储
            self._employees_storage[employee.email] = employee_data
            
            # 更新缓存
            self._employees_cache[employee.email] = employee
            
            self.logger.info(f"创建员工成功: {employee.email}")
            return employee
            
        except Exception as e:
            raise DataAccessError(f"创建员工失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def get_by_id(self, employee_email: str) -> Optional[Employee]:
        """
        根据邮箱获取员工
        
        Args:
            employee_email: 员工邮箱
            
        Returns:
            Optional[Employee]: 员工对象，如果不存在则返回None
        """
        try:
            # 先检查缓存
            if employee_email in self._employees_cache:
                return self._employees_cache[employee_email]
            
            # 从存储获取
            if employee_email in self._employees_storage:
                employee_data = self._employees_storage[employee_email]
                employee = self._deserialize_employee(employee_data)
                
                # 更新缓存
                self._employees_cache[employee_email] = employee
                return employee
            
            return None
            
        except Exception as e:
            raise DataAccessError(f"获取员工失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def update(self, employee: Employee) -> Employee:
        """
        更新员工
        
        Args:
            employee: 员工对象
            
        Returns:
            Employee: 更新后的员工对象
        """
        try:
            if employee.email not in self._employees_storage:
                raise DataAccessError(f"员工不存在: {employee.email}")
            
            # 更新时间戳
            employee.updated_at = datetime.now()
            
            # 序列化并存储
            employee_data = self._serialize_employee(employee)
            self._employees_storage[employee.email] = employee_data
            
            # 更新缓存
            self._employees_cache[employee.email] = employee
            
            self.logger.info(f"更新员工成功: {employee.email}")
            return employee
            
        except Exception as e:
            raise DataAccessError(f"更新员工失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def delete(self, employee_email: str) -> bool:
        """
        删除员工
        
        Args:
            employee_email: 员工邮箱
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if employee_email not in self._employees_storage:
                return False
            
            # 从存储删除
            del self._employees_storage[employee_email]
            
            # 从缓存删除
            if employee_email in self._employees_cache:
                del self._employees_cache[employee_email]
            
            self.logger.info(f"删除员工成功: {employee_email}")
            return True
            
        except Exception as e:
            raise DataAccessError(f"删除员工失败: {e}")
    
    @performance_monitor
    @error_handler(reraise=True)
    def query(self, criteria: Dict[str, Any]) -> List[Employee]:
        """
        查询员工
        
        Args:
            criteria: 查询条件
            
        Returns:
            List[Employee]: 查询结果列表
        """
        try:
            results = []
            
            for employee_email, employee_data in self._employees_storage.items():
                if self._matches_criteria(employee_data, criteria):
                    # 检查缓存
                    if employee_email in self._employees_cache:
                        employee = self._employees_cache[employee_email]
                    else:
                        employee = self._deserialize_employee(employee_data)
                        self._employees_cache[employee_email] = employee
                    
                    results.append(employee)
            
            return results
            
        except Exception as e:
            raise DataAccessError(f"查询员工失败: {e}")
    
    @performance_monitor
    def count(self, criteria: Dict[str, Any] = None) -> int:
        """
        统计员工数量
        
        Args:
            criteria: 查询条件
            
        Returns:
            int: 员工数量
        """
        if criteria is None:
            return len(self._employees_storage)
        
        count = 0
        for employee_data in self._employees_storage.values():
            if self._matches_criteria(employee_data, criteria):
                count += 1
        
        return count
    
    def get_by_department(self, department: str) -> List[Employee]:
        """
        按部门获取员工列表
        
        Args:
            department: 部门名称
            
        Returns:
            List[Employee]: 员工列表
        """
        return self.query({'department': department})
    
    def get_by_role(self, role: str) -> List[Employee]:
        """
        按角色获取员工列表
        
        Args:
            role: 角色名称
            
        Returns:
            List[Employee]: 员工列表
        """
        return self.query({'role': role})
    
    def get_by_skill(self, skill: str) -> List[Employee]:
        """
        按技能获取员工列表
        
        Args:
            skill: 技能名称
            
        Returns:
            List[Employee]: 员工列表
        """
        return self.query({'skill': skill})
    
    def get_by_manager(self, manager_email: str) -> List[Employee]:
        """
        获取某个经理的下属员工
        
        Args:
            manager_email: 经理邮箱
            
        Returns:
            List[Employee]: 员工列表
        """
        return self.query({'manager_email': manager_email})
    
    def get_active_employees(self) -> List[Employee]:
        """
        获取活跃员工列表
        
        Returns:
            List[Employee]: 活跃员工列表
        """
        return self.query({'is_active': True})
    
    def get_departments(self) -> List[str]:
        """
        获取所有部门列表
        
        Returns:
            List[str]: 部门列表
        """
        departments = set()
        for employee_data in self._employees_storage.values():
            departments.add(employee_data['department'])
        return list(departments)
    
    def get_roles(self) -> List[str]:
        """
        获取所有角色列表
        
        Returns:
            List[str]: 角色列表
        """
        roles = set()
        for employee_data in self._employees_storage.values():
            roles.add(employee_data['role'])
        return list(roles)
    
    def get_skills(self) -> List[str]:
        """
        获取所有技能列表
        
        Returns:
            List[str]: 技能列表
        """
        skills = set()
        for employee_data in self._employees_storage.values():
            skills.update(employee_data['skills'])
        return list(skills)
    
    def _serialize_employee(self, employee: Employee) -> Dict[str, Any]:
        """序列化员工对象"""
        return {
            'email': employee.email,
            'name': employee.name,
            'department': employee.department,
            'role': employee.role,
            'level': employee.level.value if employee.level else None,
            'skills': employee.skills,
            'manager_email': employee.manager_email,
            'hire_date': employee.hire_date.isoformat() if employee.hire_date else None,
            'is_active': employee.is_active,
            'metadata': employee.metadata,
            'created_at': employee.created_at.isoformat(),
            'updated_at': employee.updated_at.isoformat()
        }
    
    def _deserialize_employee(self, data: Dict[str, Any]) -> Employee:
        """反序列化员工对象"""
        employee = Employee(
            email=data['email'],
            name=data['name'],
            department=data['department'],
            role=data['role'],
            level=EmployeeLevel(data['level']) if data['level'] else None,
            skills=data['skills'],
            manager_email=data['manager_email'],
            hire_date=datetime.fromisoformat(data['hire_date']) if data['hire_date'] else None,
            is_active=data['is_active'],
            metadata=data['metadata']
        )
        employee.created_at = datetime.fromisoformat(data['created_at'])
        employee.updated_at = datetime.fromisoformat(data['updated_at'])
        
        return employee
    
    def _matches_criteria(self, employee_data: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查员工数据是否匹配查询条件"""
        for key, value in criteria.items():
            if key == 'department':
                if employee_data['department'] != value:
                    return False
            elif key == 'role':
                if employee_data['role'] != value:
                    return False
            elif key == 'level':
                if employee_data['level'] != value:
                    return False
            elif key == 'skill':
                if value not in employee_data['skills']:
                    return False
            elif key == 'manager_email':
                if employee_data['manager_email'] != value:
                    return False
            elif key == 'is_active':
                if employee_data['is_active'] != value:
                    return False
        
        return True
    
    def _initialize_test_data(self) -> None:
        """初始化测试数据"""
        test_employees = [
            Employee(
                email="<EMAIL>",
                name="张三",
                department="技术部",
                role="高级开发工程师",
                level=EmployeeLevel.SENIOR,
                skills=["Python", "JavaScript", "React", "Django"],
                manager_email="<EMAIL>"
            ),
            Employee(
                email="<EMAIL>",
                name="李四",
                department="技术部",
                role="技术经理",
                level=EmployeeLevel.LEAD,
                skills=["Python", "Java", "架构设计", "团队管理"],
                manager_email="<EMAIL>"
            ),
            Employee(
                email="<EMAIL>",
                name="王五",
                department="技术部",
                role="技术总监",
                level=EmployeeLevel.EXPERT,
                skills=["架构设计", "技术战略", "团队管理", "产品规划"]
            ),
            Employee(
                email="<EMAIL>",
                name="赵六",
                department="产品部",
                role="产品经理",
                level=EmployeeLevel.INTERMEDIATE,
                skills=["产品设计", "用户研究", "数据分析", "项目管理"],
                manager_email="<EMAIL>"
            ),
            Employee(
                email="<EMAIL>",
                name="钱七",
                department="产品部",
                role="产品总监",
                level=EmployeeLevel.EXPERT,
                skills=["产品战略", "市场分析", "团队管理", "商业模式"]
            )
        ]
        
        for employee in test_employees:
            try:
                employee_data = self._serialize_employee(employee)
                self._employees_storage[employee.email] = employee_data
                self._employees_cache[employee.email] = employee
            except Exception as e:
                self.logger.error(f"初始化测试员工数据失败: {employee.email}, 错误: {e}")
    
    def cleanup(self) -> None:
        """清理资源"""
        self._employees_cache.clear()
        self.logger.info("员工仓储资源清理完成")
