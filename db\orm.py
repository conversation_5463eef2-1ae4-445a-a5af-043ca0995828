from sqlalchemy import create_engine, Column, Integer, String, Float, Text, Date, DateTime, JSON, Boolean, ForeignKey, text
from sqlalchemy.orm import declarative_base, sessionmaker, relationship
from sqlalchemy.sql import func
import os
import datetime
import logging
from config import DB_CONFIG

logger = logging.getLogger(__name__)

def create_database_engine():
    """创建PostgreSQL数据库引擎 - 统一使用PostgreSQL，禁止SQLite"""
    try:
        database_url = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}"
        engine = create_engine(database_url, echo=False, future=True)
        # 测试连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info(f"使用PostgreSQL数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}")
        return engine
    except Exception as e:
        logger.error(f"PostgreSQL连接失败: {e}")
        logger.error("请确保PostgreSQL服务正在运行，数据库配置正确")
        raise ConnectionError(f"无法连接到PostgreSQL数据库: {e}")

# 创建数据库引擎和会话
engine = create_database_engine()
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False)
Base = declarative_base()

class WeeklyReportAnalysis(Base):
    __tablename__ = 'weekly_report_analysis'
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(String, unique=True, nullable=False)
    employee_name = Column(String)
    employee_email = Column(String, nullable=False, index=True)
    department = Column(String)
    role = Column(String)
    week = Column(String)
    work_items = Column(JSON)
    summary = Column(JSON)
    metrics = Column(JSON)
    ai_version = Column(String)
    raw_text = Column(Text)
    tags = Column(JSON)
    anomaly_flags = Column(JSON)
    total_hours = Column(Float)
    task_count = Column(Integer)
    saturation = Column(Float)
    saturation_tag = Column(String)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    tasks = relationship('TaskItem', back_populates='report', cascade='all, delete-orphan')

class TaskItem(Base):
    __tablename__ = 'task_items'
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(String, ForeignKey('weekly_report_analysis.report_id', ondelete='CASCADE'))
    title = Column(String)
    description = Column(Text)
    duration_hours = Column(Float)
    complexity = Column(String)
    category = Column(String)
    date = Column(Date)
    extra = Column(JSON)
    tags = Column(JSON)
    anomaly_flags = Column(JSON)
    report = relationship('WeeklyReportAnalysis', back_populates='tasks')

def save_report_analysis(result: dict):
    session = SessionLocal()
    try:
        # 主表
        report = WeeklyReportAnalysis(
            report_id=result.get('report_id'),
            employee_name=result.get('employee', {}).get('name'),
            employee_email=result.get('employee', {}).get('email'),
            department=result.get('employee', {}).get('department'),
            role=result.get('employee', {}).get('role'),
            week=result.get('week'),
            work_items=result.get('work_items'),
            summary=result.get('summary'),
            metrics=result.get('metrics'),
            ai_version=result.get('ai_version'),
            raw_text=result.get('raw_text'),
            tags=result.get('tags'),
            anomaly_flags=result.get('anomaly_flags'),
            total_hours=result.get('metrics', {}).get('total_hours'),
            task_count=result.get('metrics', {}).get('task_count'),
            saturation=result.get('metrics', {}).get('saturation'),
            saturation_tag=result.get('metrics', {}).get('saturation_tag'),
        )
        session.add(report)
        session.flush()
        # 明细表
        for item in result.get('work_items', []):
            task = TaskItem(
                report_id=report.report_id,
                title=item.get('title'),
                description=item.get('description'),
                duration_hours=item.get('duration_hours'),
                complexity=item.get('complexity'),
                category=item.get('category'),
                date=item.get('date'),
                extra=item.get('extra'),
                tags=item.get('tags'),
                anomaly_flags=item.get('anomaly_flags'),
            )
            session.add(task)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

def query_report_analysis(email=None, department=None, week=None, tag=None, anomaly=None):
    session = SessionLocal()
    try:
        q = session.query(WeeklyReportAnalysis)
        if email:
            q = q.filter(WeeklyReportAnalysis.employee_email == email)
        if department:
            q = q.filter(WeeklyReportAnalysis.department == department)
        if week:
            q = q.filter(WeeklyReportAnalysis.week == week)
        if tag:
            q = q.filter(WeeklyReportAnalysis.tags.contains([tag]))
        if anomaly:
            q = q.filter(WeeklyReportAnalysis.anomaly_flags.contains([anomaly]))
        results = q.order_by(WeeklyReportAnalysis.created_at.desc()).all()
        return [r.__dict__ for r in results]
    finally:
        session.close()

# 创建表
Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()