#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据分析流程优化器
优化数据处理流程，提升分析效率和准确性
"""
import logging
import time
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class DataFlowMetrics:
    """数据流程指标收集器"""
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.processing_metrics = deque(maxlen=max_records)
        self.stage_metrics = defaultdict(lambda: {
            'total_time': 0,
            'count': 0,
            'errors': 0,
            'avg_time': 0
        })
        self.lock = threading.Lock()
    
    def record_stage(self, stage_name: str, execution_time: float, success: bool = True):
        """记录处理阶段指标"""
        with self.lock:
            # 记录单次处理
            record = {
                'timestamp': time.time(),
                'stage': stage_name,
                'execution_time': execution_time,
                'success': success
            }
            self.processing_metrics.append(record)
            
            # 更新阶段统计
            stage_stats = self.stage_metrics[stage_name]
            stage_stats['count'] += 1
            stage_stats['total_time'] += execution_time
            stage_stats['avg_time'] = stage_stats['total_time'] / stage_stats['count']
            
            if not success:
                stage_stats['errors'] += 1
    
    def get_stage_statistics(self) -> Dict[str, Any]:
        """获取阶段统计"""
        with self.lock:
            return dict(self.stage_metrics)
    
    def get_overall_statistics(self) -> Dict[str, Any]:
        """获取整体统计"""
        with self.lock:
            if not self.processing_metrics:
                return {'message': 'No processing records'}
            
            total_records = len(self.processing_metrics)
            successful_records = sum(1 for r in self.processing_metrics if r['success'])
            total_time = sum(r['execution_time'] for r in self.processing_metrics)
            
            return {
                'total_processed': total_records,
                'success_rate': successful_records / total_records,
                'avg_processing_time': total_time / total_records,
                'total_stages': len(self.stage_metrics),
                'throughput_per_minute': total_records / max(total_time / 60, 1)
            }


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.quality_rules = {
            'required_fields': [
                'employee', 'week', 'work_items', 'summary', 'metrics'
            ],
            'employee_fields': ['name', 'email', 'department', 'role'],
            'metrics_fields': ['total_hours', 'task_count'],
            'work_item_fields': ['title', 'description', 'duration_hours']
        }
    
    def check_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """检查数据质量"""
        quality_report = {
            'overall_score': 0.0,
            'issues': [],
            'warnings': [],
            'passed_checks': []
        }
        
        total_checks = 0
        passed_checks = 0
        
        # 检查必需字段
        for field in self.quality_rules['required_fields']:
            total_checks += 1
            if field in data and data[field]:
                passed_checks += 1
                quality_report['passed_checks'].append(f"必需字段 {field} 存在")
            else:
                quality_report['issues'].append(f"缺少必需字段: {field}")
        
        # 检查员工信息
        if 'employee' in data and isinstance(data['employee'], dict):
            for field in self.quality_rules['employee_fields']:
                total_checks += 1
                if field in data['employee'] and data['employee'][field]:
                    passed_checks += 1
                    quality_report['passed_checks'].append(f"员工字段 {field} 存在")
                else:
                    quality_report['warnings'].append(f"员工信息缺少字段: {field}")
        
        # 检查指标数据
        if 'metrics' in data and isinstance(data['metrics'], dict):
            for field in self.quality_rules['metrics_fields']:
                total_checks += 1
                if field in data['metrics'] and isinstance(data['metrics'][field], (int, float)):
                    passed_checks += 1
                    quality_report['passed_checks'].append(f"指标字段 {field} 有效")
                else:
                    quality_report['issues'].append(f"指标数据缺少或无效: {field}")
        
        # 检查工作项
        if 'work_items' in data and isinstance(data['work_items'], list):
            if len(data['work_items']) > 0:
                quality_report['passed_checks'].append("工作项列表非空")
                
                # 检查工作项结构
                for i, item in enumerate(data['work_items']):
                    if isinstance(item, dict):
                        for field in self.quality_rules['work_item_fields']:
                            if field not in item:
                                quality_report['warnings'].append(f"工作项 {i+1} 缺少字段: {field}")
            else:
                quality_report['warnings'].append("工作项列表为空")
        
        # 计算总体评分
        quality_report['overall_score'] = passed_checks / total_checks if total_checks > 0 else 0.0
        
        return quality_report
    
    def suggest_improvements(self, quality_report: Dict[str, Any]) -> List[str]:
        """建议改进措施"""
        suggestions = []
        
        if quality_report['overall_score'] < 0.7:
            suggestions.append("数据质量较低，建议检查数据采集流程")
        
        if len(quality_report['issues']) > 0:
            suggestions.append("存在严重数据问题，需要立即修复")
        
        if len(quality_report['warnings']) > 3:
            suggestions.append("存在多个警告，建议优化数据完整性")
        
        return suggestions


class DataFlowOptimizer:
    """数据流程优化器"""
    
    def __init__(self, enable_metrics: bool = True, enable_quality_check: bool = True):
        self.enable_metrics = enable_metrics
        self.enable_quality_check = enable_quality_check
        
        self.metrics = DataFlowMetrics() if enable_metrics else None
        self.quality_checker = DataQualityChecker() if enable_quality_check else None
        
        # 优化配置
        self.optimization_config = {
            'batch_size': 10,
            'max_workers': 3,
            'cache_enabled': True,
            'quality_threshold': 0.7,
            'retry_failed': True,
            'max_retries': 2
        }
    
    def optimize_data_processing(self, data_items: List[Dict[str, Any]], 
                                processor_func, **kwargs) -> List[Dict[str, Any]]:
        """优化数据处理流程"""
        if not data_items:
            return []
        
        logger.info(f"开始优化处理 {len(data_items)} 个数据项")
        start_time = time.time()
        
        # 数据预处理
        preprocessed_data = self._preprocess_data(data_items)
        
        # 批量处理
        if len(preprocessed_data) > self.optimization_config['batch_size']:
            results = self._batch_process(preprocessed_data, processor_func, **kwargs)
        else:
            results = self._sequential_process(preprocessed_data, processor_func, **kwargs)
        
        # 后处理
        final_results = self._postprocess_results(results)
        
        total_time = time.time() - start_time
        logger.info(f"数据处理优化完成，总耗时: {total_time:.2f}秒")
        
        if self.metrics:
            self.metrics.record_stage('overall_processing', total_time, True)
        
        return final_results
    
    def _preprocess_data(self, data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据预处理"""
        start_time = time.time()
        
        preprocessed = []
        for item in data_items:
            # 数据清洗
            cleaned_item = self._clean_data_item(item)
            
            # 数据验证
            if self._validate_data_item(cleaned_item):
                preprocessed.append(cleaned_item)
            else:
                logger.warning(f"数据项验证失败，跳过处理: {item.get('id', 'unknown')}")
        
        processing_time = time.time() - start_time
        if self.metrics:
            self.metrics.record_stage('preprocessing', processing_time, True)
        
        logger.info(f"预处理完成，有效数据: {len(preprocessed)}/{len(data_items)}")
        return preprocessed
    
    def _clean_data_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """清洗单个数据项"""
        cleaned = item.copy()
        
        # 清理空白字符
        for key, value in cleaned.items():
            if isinstance(value, str):
                cleaned[key] = value.strip()
        
        # 标准化邮箱
        if 'employee' in cleaned and isinstance(cleaned['employee'], dict):
            if 'email' in cleaned['employee']:
                cleaned['employee']['email'] = cleaned['employee']['email'].lower().strip()
        
        return cleaned
    
    def _validate_data_item(self, item: Dict[str, Any]) -> bool:
        """验证数据项"""
        # 基本验证
        if not isinstance(item, dict):
            return False
        
        # 检查必需字段
        required_fields = ['employee', 'week']
        for field in required_fields:
            if field not in item or not item[field]:
                return False
        
        return True
    
    def _batch_process(self, data_items: List[Dict[str, Any]], 
                      processor_func, **kwargs) -> List[Dict[str, Any]]:
        """批量处理"""
        start_time = time.time()
        
        # 分批处理
        batch_size = self.optimization_config['batch_size']
        batches = [data_items[i:i + batch_size] for i in range(0, len(data_items), batch_size)]
        
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.optimization_config['max_workers']) as executor:
            # 提交批次任务
            future_to_batch = {}
            for i, batch in enumerate(batches):
                future = executor.submit(self._process_batch, batch, processor_func, **kwargs)
                future_to_batch[future] = i
            
            # 收集结果
            for future in as_completed(future_to_batch):
                batch_index = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    logger.info(f"批次 {batch_index + 1}/{len(batches)} 处理完成")
                except Exception as e:
                    logger.error(f"批次 {batch_index + 1} 处理失败: {e}")
        
        processing_time = time.time() - start_time
        if self.metrics:
            self.metrics.record_stage('batch_processing', processing_time, True)
        
        return all_results
    
    def _process_batch(self, batch: List[Dict[str, Any]], 
                      processor_func, **kwargs) -> List[Dict[str, Any]]:
        """处理单个批次"""
        results = []
        
        for item in batch:
            try:
                result = processor_func(item, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"处理数据项失败: {e}")
                # 可以选择重试或跳过
                if self.optimization_config['retry_failed']:
                    # 简单重试逻辑
                    try:
                        result = processor_func(item, **kwargs)
                        results.append(result)
                    except Exception as retry_e:
                        logger.error(f"重试失败: {retry_e}")
                        results.append({'error': str(retry_e), 'original_item': item})
                else:
                    results.append({'error': str(e), 'original_item': item})
        
        return results
    
    def _sequential_process(self, data_items: List[Dict[str, Any]], 
                           processor_func, **kwargs) -> List[Dict[str, Any]]:
        """顺序处理"""
        start_time = time.time()
        
        results = []
        for i, item in enumerate(data_items):
            try:
                result = processor_func(item, **kwargs)
                results.append(result)
                logger.debug(f"处理项目 {i+1}/{len(data_items)} 完成")
            except Exception as e:
                logger.error(f"处理项目 {i+1} 失败: {e}")
                results.append({'error': str(e), 'original_item': item})
        
        processing_time = time.time() - start_time
        if self.metrics:
            self.metrics.record_stage('sequential_processing', processing_time, True)
        
        return results
    
    def _postprocess_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """后处理结果"""
        start_time = time.time()
        
        processed_results = []
        quality_reports = []
        
        for result in results:
            if 'error' in result:
                # 错误结果直接跳过或记录
                logger.warning(f"跳过错误结果: {result['error']}")
                continue
            
            # 质量检查
            if self.enable_quality_check and self.quality_checker:
                quality_report = self.quality_checker.check_data_quality(result)
                quality_reports.append(quality_report)
                
                # 根据质量阈值决定是否保留
                if quality_report['overall_score'] >= self.optimization_config['quality_threshold']:
                    result['quality_report'] = quality_report
                    processed_results.append(result)
                else:
                    logger.warning(f"数据质量不达标，跳过: 评分 {quality_report['overall_score']}")
            else:
                processed_results.append(result)
        
        processing_time = time.time() - start_time
        if self.metrics:
            self.metrics.record_stage('postprocessing', processing_time, True)
        
        # 生成质量报告摘要
        if quality_reports:
            avg_quality = sum(r['overall_score'] for r in quality_reports) / len(quality_reports)
            logger.info(f"平均数据质量评分: {avg_quality:.2f}")
        
        logger.info(f"后处理完成，有效结果: {len(processed_results)}/{len(results)}")
        return processed_results
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = {
            'optimization_config': self.optimization_config,
            'metrics_enabled': self.enable_metrics,
            'quality_check_enabled': self.enable_quality_check
        }
        
        if self.metrics:
            stats['stage_metrics'] = self.metrics.get_stage_statistics()
            stats['overall_metrics'] = self.metrics.get_overall_statistics()
        
        return stats
    
    def update_optimization_config(self, new_config: Dict[str, Any]):
        """更新优化配置"""
        self.optimization_config.update(new_config)
        logger.info(f"优化配置已更新: {new_config}")
    
    def reset_metrics(self):
        """重置指标"""
        if self.metrics:
            with self.metrics.lock:
                self.metrics.processing_metrics.clear()
                self.metrics.stage_metrics.clear()
            logger.info("数据流程指标已重置")
