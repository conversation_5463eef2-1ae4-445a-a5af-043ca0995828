---
description:
globs:
alwaysApply: false
---
# API全链路真实落地与禁止伪实现规范

- 所有API接口、参数、返回结构、异常码、权限、日志、标签、异常处理等，必须全链路真实落地，严禁mock、伪实现、假数据。
- 所有接口必须与数据库、AI分析、前端等全链路真实集成，数据可查、可追溯。
- 所有接口参数、返回、异常、权限、日志等，必须严格校验，禁止任何形式的伪实现。
- 允许直接迁移dataann_email目录下的API相关代码，迁移后需严格按zkteco_js目录结构、命名、依赖、测试等规范适配。
- 所有API接口、参数、返回、异常、主链路规则等全局规范内容，必须实时写入mdc文档，任何未写入mdc的变更禁止上线。
- 所有API变更、测试、部署等过程，必须实时写入mdc文档，确保可追溯。

# AI分析主流程与邮箱唯一性API规范补充

- 所有API涉及邮箱的参数、返回，必须全链路小写唯一，接口层和业务逻辑层强制转小写并校验唯一性。
- 所有AI分析、标签、异常、趋势等API接口，需严格参数校验、异常处理、权限校验，返回结构与数据库、前端、AI分析全链路一致。
- 所有结构化输出、标签、异常等字段必须严格Schema校验，缺失用null或空字符串。
- 自动化测试需覆盖AI分析主流程、API接口、Schema校验、标签与异常、邮箱唯一性、异常场景。
- 所有API变更、测试、部署等过程，必须实时写入mdc文档，确保可追溯。

## [2024-05-06] AI分析API真实对接OpenAI
- /api/ai/analyze等AI分析相关接口已对接OpenAI官方API，支持API Key、模型名、超时、异常回退、日志。
- 支持多模型切换，配置项可热更新。
- 禁止伪实现，所有API调用、异常、日志、参数、返回结构等全链路真实落地。
- 自动化测试用例补全中，覆盖主流程、异常、回退。
- 变更可追溯，所有代码/文档/测试同步闭环。

## [2024-05-06] 监控与可观测性API
- /metrics端点已上线，Prometheus可采集API请求总数、耗时等核心指标。
- 支持Grafana可视化监控大盘配置。
- 监控端点、指标、可观测性规范已同步。
