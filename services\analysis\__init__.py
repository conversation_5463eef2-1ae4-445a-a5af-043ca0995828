#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析服务模块包

模块描述: 提供统一的数据分析服务接口，包含ML分析、聚类分析、趋势分析等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .analysis_service, .ml_analyzer, .clustering_analyzer, .trend_analyzer, .anomaly_analyzer
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

from .analysis_service import AnalysisService
from .ml_analyzer import MLAnalyzer
from .clustering_analyzer import ClusteringAnalyzer
from .trend_analyzer import TrendAnalyzer
from .anomaly_analyzer import AnomalyAnalyzer

__all__ = [
    'AnalysisService',
    'MLAnalyzer',
    'ClusteringAnalyzer',
    'TrendAnalyzer',
    'AnomalyAnalyzer'
]
