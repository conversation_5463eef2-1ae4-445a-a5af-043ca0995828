# AI分析模块 (ai)

## 模块概述

AI分析模块是AI驱动邮件周报分析系统的核心智能分析引擎，负责对邮件内容进行智能分析、标签分配、异常检测等功能。

## 模块结构

```
ai/
├── README.md                    # 模块说明文档
├── __init__.py                  # 模块初始化
├── adapter.py                   # AI模型适配器
├── analyzer.py                  # 分析主流程
├── schema.py                    # 结构化输出Schema定义
├── prompt_loader.py             # 提示词模板加载器
├── tagger.py                    # 智能标签分配器
├── anomaly_detector.py          # 异常检测器
├── ai_config.json               # AI配置文件
└── analysis/                    # 分析子模块
    ├── __init__.py              # 子模块初始化
    ├── analysis_engine.py       # 分析引擎
    ├── model_api.py             # 模型API接口
    ├── prompt_generator.py      # 提示词生成器
    ├── rag_adapter.py           # RAG检索增强适配器
    └── result_processor.py      # 结果处理器
```

## 核心功能

### 1. AI模型适配 (adapter.py)
- **多模型支持**：支持OpenAI、Ollama等多种AI模型
- **自动切换**：模型故障时自动切换到备用模型
- **性能监控**：跟踪模型调用性能和成功率
- **配置管理**：灵活的模型配置和参数调整

### 2. 智能分析 (analyzer.py)
- **多岗位适配**：支持不同岗位的专门分析模板
- **结构化输出**：严格的Schema校验和数据结构化
- **专项分析**：创新能力、品质分析、趋势分析
- **RAG增强**：检索增强生成，提高分析准确性

### 3. 标签系统 (tagger.py)
- **智能标签**：基于内容自动生成标签
- **关键词匹配**：预定义关键词标签分配
- **标签去重**：自动去除重复和相似标签
- **标签统计**：标签使用频率和分布统计

### 4. 异常检测 (anomaly_detector.py)
- **多维度检测**：工作量、绩效、内容等多维度异常检测
- **智能学习**：基于历史数据学习异常模式
- **实时预警**：异常情况实时检测和预警
- **分级处理**：不同严重程度的异常分级处理

## 系统优化特性

### 模型适配增强
- 实现插件式模型适配器，支持更多AI服务提供商
- 开发模型性能评估机制，自动选择最优模型
- 实现模型参数动态调整，根据内容特征优化调用参数

### 异常检测增强
- 引入机器学习模型，学习历史数据中的异常模式
- 实现多维度异常检测，覆盖内容、结构、时间等方面
- 开发异常自动分类机制，提供针对性处理建议

### 性能优化
- 实现批量处理机制，合并相似请求
- 优化提示词模板，减少token消耗
- 引入结果缓存，避免重复分析
- 实现并行处理流水线，提高吞吐量

## 配置说明

### AI模型配置 (ai_config.json)
```json
{
    "models": [
        {
            "name": "openai-gpt4",
            "type": "openai",
            "api_key": "${OPENAI_API_KEY}",
            "model": "gpt-4",
            "max_tokens": 2000,
            "temperature": 0.1,
            "priority": 1
        },
        {
            "name": "ollama-llama2",
            "type": "ollama",
            "base_url": "http://localhost:11434",
            "model": "llama2",
            "priority": 2
        }
    ],
    "default_model": "openai-gpt4",
    "retry_count": 3,
    "timeout": 30
}
```

### RAG配置
```python
RAG_CONFIG = {
    "enabled": True,
    "api_url": "https://api.fastgpt.in/api/v1/chat/completions",
    "api_key": "your_fastgpt_key",
    "knowledge_base_id": "your_kb_id",
    "top_k": 5,
    "score_threshold": 0.7
}
```

## 使用示例

### 基本分析
```python
from ai.analyzer import AIAnalyzer

# 创建分析器
analyzer = AIAnalyzer()

# 分析周报
result = analyzer.analyze(
    report_text="本周完成了项目开发...",
    department="技术部",
    role="开发工程师"
)

print(f"分析结果: {result['summary']}")
print(f"标签: {result['tags']}")
print(f"异常: {result['anomaly_flags']}")
```

### 批量分析
```python
from ai.enhanced_analyzer import EnhancedAIAnalyzer

# 创建增强分析器
analyzer = EnhancedAIAnalyzer(enable_cache=True)

# 批量分析
reports = [
    {"text": "周报1...", "department": "技术部", "role": "工程师"},
    {"text": "周报2...", "department": "销售部", "role": "销售"}
]

results = analyzer.batch_analyze(reports, use_parallel=True)
```

### RAG增强分析
```python
# 启用RAG增强
analyzer = AIAnalyzer(rag_config=RAG_CONFIG)

result = analyzer.analyze(
    report_text="遇到技术难题...",
    department="技术部",
    role="工程师",
    use_rag=True
)
```

## 性能指标

### 目标性能
- **AI分析耗时**：平均5秒/封
- **模型调用成功率**：>99.5%
- **Schema校验通过率**：>99%
- **缓存命中率**：>60%

### 监控指标
- 模型调用次数和耗时
- 分析成功率和失败率
- 标签分配准确率
- 异常检测准确率

## 提示词模板

### 模板结构
```
prompt_templates/
├── default.json                 # 默认模板
├── 开发工程师.json              # 开发工程师专用模板
├── 技术支持工程师.json          # 技术支持专用模板
├── 销售.json                    # 销售专用模板
├── tag_dictionary.json          # 标签字典
└── anomaly_dictionary.json      # 异常字典
```

### 模板示例
```json
{
    "template": "请分析以下周报内容...",
    "ai_version": "gpt-4",
    "name": "张三",
    "email": "<EMAIL>",
    "week": "2024-W21",
    "role_specific_prompts": {
        "工程师": "重点关注技术实现和代码质量...",
        "销售": "重点关注销售业绩和客户关系..."
    }
}
```

## 测试覆盖

### 单元测试
- AI适配器测试
- Schema验证测试
- 标签分配测试
- 异常检测测试

### 集成测试
- 端到端分析流程
- RAG增强测试
- 多模型切换测试
- 性能压力测试

## 注意事项

1. **API密钥安全**：确保AI模型API密钥的安全存储
2. **成本控制**：监控AI模型调用成本，避免过度消耗
3. **数据隐私**：确保分析数据的隐私和安全
4. **模型更新**：定期更新模型版本和配置

## 更新日志

### [2024-05-25] AI分析引擎优化
- 增强模型适配器，支持插件式架构
- 实现性能监控和自动模型选择
- 添加结果缓存和批量处理功能
- 优化异常检测算法

### [2024-05-25] 模块文档规范化
- 创建模块README文档
- 规范化目录结构说明
- 添加使用示例和配置说明
