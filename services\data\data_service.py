#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据服务主类

模块描述: 统一数据访问入口，协调各种数据仓储，提供高级数据操作功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.base, core.interfaces, domain.entities, domain.value_objects
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import asyncio

from core.base import BaseService, BaseRepository
from core.decorators import performance_monitor, cache_result, error_handler
from core.exceptions import DataAccessError, ValidationError
from domain.entities import WeeklyReport, Employee, WorkItem, AnalysisResult
from domain.value_objects import FilterCriteria, ProcessingContext


class DataService(BaseService):
    """
    数据服务 - 统一数据访问入口，协调各种数据仓储
    
    职责：
    - 管理和协调各种数据仓储
    - 提供统一的数据访问接口
    - 支持事务管理
    - 缓存数据操作结果
    - 数据验证和清理
    """
    
    def __init__(self, config, repositories: Dict[str, BaseRepository] = None, 
                 cache_manager=None, transaction_manager=None):
        """
        初始化数据服务
        
        Args:
            config: 服务配置
            repositories: 数据仓储字典
            cache_manager: 缓存管理器
            transaction_manager: 事务管理器
        """
        super().__init__(config)
        self.repositories = repositories or {}
        self.cache_manager = cache_manager
        self.transaction_manager = transaction_manager
        self._operation_history: List[Dict[str, Any]] = []
    
    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化所有仓储
            for name, repository in self.repositories.items():
                if hasattr(repository, 'initialize'):
                    if not repository.initialize():
                        self.logger.error(f"数据仓储初始化失败: {name}")
                        return False
                self.logger.info(f"数据仓储初始化成功: {name}")
            
            self.logger.info("数据服务初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"数据服务初始化失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.config.name:
            return False
        return True
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            'service_name': self.config.name,
            'version': self.config.version,
            'repositories': list(self.repositories.keys()),
            'repository_count': len(self.repositories),
            'operation_history_count': len(self._operation_history),
            'cache_enabled': self.cache_manager is not None,
            'transaction_enabled': self.transaction_manager is not None
        }
    
    def add_repository(self, name: str, repository: BaseRepository) -> None:
        """添加数据仓储"""
        self.repositories[name] = repository
        self.logger.info(f"添加数据仓储: {name}")
    
    def get_repository(self, name: str) -> Optional[BaseRepository]:
        """获取数据仓储"""
        return self.repositories.get(name)
    
    # 员工数据操作
    @performance_monitor
    @cache_result(ttl=3600)  # 缓存1小时
    @error_handler(reraise=True)
    def get_employee(self, email: str) -> Optional[Employee]:
        """
        获取员工信息
        
        Args:
            email: 员工邮箱
            
        Returns:
            Optional[Employee]: 员工对象
        """
        employee_repo = self.get_repository('employee')
        if not employee_repo:
            raise DataAccessError("员工仓储未配置")
        
        employee = employee_repo.get_by_id(email)
        self._record_operation('get_employee', {'email': email}, employee is not None)
        return employee
    
    @performance_monitor
    @error_handler(reraise=True)
    def save_employee(self, employee: Employee) -> Employee:
        """
        保存员工信息
        
        Args:
            employee: 员工对象
            
        Returns:
            Employee: 保存后的员工对象
        """
        if not self._validate_employee(employee):
            raise ValidationError("员工数据验证失败")
        
        employee_repo = self.get_repository('employee')
        if not employee_repo:
            raise DataAccessError("员工仓储未配置")
        
        # 检查是否已存在
        existing = employee_repo.get_by_id(employee.email)
        if existing:
            saved_employee = employee_repo.update(employee)
        else:
            saved_employee = employee_repo.create(employee)
        
        # 清除相关缓存
        if self.cache_manager:
            cache_key = f"employee:{employee.email}"
            self.cache_manager.delete(cache_key)
        
        self._record_operation('save_employee', {'email': employee.email}, True)
        return saved_employee
    
    @performance_monitor
    @cache_result(ttl=1800)  # 缓存30分钟
    def get_employees_by_department(self, department: str) -> List[Employee]:
        """
        按部门获取员工列表
        
        Args:
            department: 部门名称
            
        Returns:
            List[Employee]: 员工列表
        """
        employee_repo = self.get_repository('employee')
        if not employee_repo:
            raise DataAccessError("员工仓储未配置")
        
        employees = employee_repo.query({'department': department})
        self._record_operation('get_employees_by_department', {'department': department}, True)
        return employees
    
    # 周报数据操作
    @performance_monitor
    @cache_result(ttl=1800)  # 缓存30分钟
    @error_handler(reraise=True)
    def get_weekly_report(self, report_id: str) -> Optional[WeeklyReport]:
        """
        获取周报
        
        Args:
            report_id: 周报ID
            
        Returns:
            Optional[WeeklyReport]: 周报对象
        """
        report_repo = self.get_repository('report')
        if not report_repo:
            raise DataAccessError("周报仓储未配置")
        
        report = report_repo.get_by_id(report_id)
        self._record_operation('get_weekly_report', {'report_id': report_id}, report is not None)
        return report
    
    @performance_monitor
    @error_handler(reraise=True)
    def save_weekly_report(self, report: WeeklyReport) -> WeeklyReport:
        """
        保存周报
        
        Args:
            report: 周报对象
            
        Returns:
            WeeklyReport: 保存后的周报对象
        """
        if not self._validate_weekly_report(report):
            raise ValidationError("周报数据验证失败")
        
        report_repo = self.get_repository('report')
        if not report_repo:
            raise DataAccessError("周报仓储未配置")
        
        # 检查是否已存在
        existing = report_repo.get_by_id(report.report_id)
        if existing:
            saved_report = report_repo.update(report)
        else:
            saved_report = report_repo.create(report)
        
        # 清除相关缓存
        if self.cache_manager:
            cache_keys = [
                f"report:{report.report_id}",
                f"reports_by_employee:{report.employee.email}",
                f"reports_by_week:{report.week}"
            ]
            for key in cache_keys:
                self.cache_manager.delete(key)
        
        self._record_operation('save_weekly_report', {'report_id': report.report_id}, True)
        return saved_report
    
    @performance_monitor
    @cache_result(ttl=1800)
    def get_reports_by_criteria(self, criteria: FilterCriteria) -> List[WeeklyReport]:
        """
        按条件获取周报列表
        
        Args:
            criteria: 筛选条件
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        report_repo = self.get_repository('report')
        if not report_repo:
            raise DataAccessError("周报仓储未配置")
        
        # 构建查询条件
        query_dict = {}
        if criteria.department:
            query_dict['employee.department'] = criteria.department
        if criteria.role:
            query_dict['employee.role'] = criteria.role
        if criteria.week:
            query_dict['week'] = criteria.week
        if criteria.date_range:
            query_dict['date_range'] = criteria.date_range
        
        reports = report_repo.query(query_dict)
        
        # 应用额外筛选
        if criteria.employee_emails:
            reports = [r for r in reports if r.employee.email in criteria.employee_emails]
        
        if criteria.tags:
            reports = [r for r in reports if any(tag in r.tags for tag in criteria.tags)]
        
        self._record_operation('get_reports_by_criteria', {'criteria': str(criteria)}, True)
        return reports
    
    @performance_monitor
    @cache_result(ttl=3600)
    def get_reports_by_employee(self, employee_email: str, 
                              start_week: str = None, end_week: str = None) -> List[WeeklyReport]:
        """
        获取员工的周报列表
        
        Args:
            employee_email: 员工邮箱
            start_week: 开始周次
            end_week: 结束周次
            
        Returns:
            List[WeeklyReport]: 周报列表
        """
        report_repo = self.get_repository('report')
        if not report_repo:
            raise DataAccessError("周报仓储未配置")
        
        query_dict = {'employee.email': employee_email}
        if start_week and end_week:
            query_dict['week_range'] = (start_week, end_week)
        
        reports = report_repo.query(query_dict)
        self._record_operation('get_reports_by_employee', 
                             {'employee_email': employee_email}, True)
        return reports
    
    # 分析结果数据操作
    @performance_monitor
    @error_handler(reraise=True)
    def save_analysis_result(self, result: AnalysisResult) -> AnalysisResult:
        """
        保存分析结果
        
        Args:
            result: 分析结果对象
            
        Returns:
            AnalysisResult: 保存后的分析结果对象
        """
        analysis_repo = self.get_repository('analysis')
        if not analysis_repo:
            raise DataAccessError("分析结果仓储未配置")
        
        saved_result = analysis_repo.create(result)
        self._record_operation('save_analysis_result', {'result_id': result.result_id}, True)
        return saved_result
    
    @performance_monitor
    @cache_result(ttl=1800)
    def get_analysis_results_by_report(self, report_id: str) -> List[AnalysisResult]:
        """
        获取周报的分析结果
        
        Args:
            report_id: 周报ID
            
        Returns:
            List[AnalysisResult]: 分析结果列表
        """
        analysis_repo = self.get_repository('analysis')
        if not analysis_repo:
            raise DataAccessError("分析结果仓储未配置")
        
        results = analysis_repo.query({'report_id': report_id})
        self._record_operation('get_analysis_results_by_report', 
                             {'report_id': report_id}, True)
        return results
    
    # 批量操作
    @performance_monitor
    def batch_save_reports(self, reports: List[WeeklyReport]) -> List[WeeklyReport]:
        """
        批量保存周报
        
        Args:
            reports: 周报列表
            
        Returns:
            List[WeeklyReport]: 保存后的周报列表
        """
        if not reports:
            return []
        
        saved_reports = []
        failed_reports = []
        
        for report in reports:
            try:
                saved_report = self.save_weekly_report(report)
                saved_reports.append(saved_report)
            except Exception as e:
                self.logger.error(f"保存周报失败: {report.report_id}, 错误: {e}")
                failed_reports.append(report.report_id)
        
        self._record_operation('batch_save_reports', {
            'total_count': len(reports),
            'success_count': len(saved_reports),
            'failed_count': len(failed_reports),
            'failed_reports': failed_reports
        }, len(failed_reports) == 0)
        
        return saved_reports
    
    # 统计和聚合操作
    @performance_monitor
    @cache_result(ttl=3600)
    def get_statistics(self, criteria: FilterCriteria = None) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            criteria: 筛选条件
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        reports = self.get_reports_by_criteria(criteria or FilterCriteria())
        
        if not reports:
            return {
                'total_reports': 0,
                'total_employees': 0,
                'total_hours': 0,
                'total_tasks': 0
            }
        
        total_hours = sum(sum(item.duration_hours for item in report.work_items) 
                         for report in reports)
        total_tasks = sum(len(report.work_items) for report in reports)
        unique_employees = len(set(report.employee.email for report in reports))
        unique_departments = len(set(report.employee.department for report in reports))
        
        # 按部门统计
        dept_stats = {}
        for report in reports:
            dept = report.employee.department
            if dept not in dept_stats:
                dept_stats[dept] = {
                    'employee_count': set(),
                    'total_hours': 0,
                    'total_tasks': 0
                }
            
            dept_stats[dept]['employee_count'].add(report.employee.email)
            dept_stats[dept]['total_hours'] += sum(item.duration_hours for item in report.work_items)
            dept_stats[dept]['total_tasks'] += len(report.work_items)
        
        # 转换部门统计格式
        department_summary = {}
        for dept, stats in dept_stats.items():
            employee_count = len(stats['employee_count'])
            department_summary[dept] = {
                'employee_count': employee_count,
                'total_hours': stats['total_hours'],
                'total_tasks': stats['total_tasks'],
                'avg_hours_per_employee': stats['total_hours'] / employee_count if employee_count > 0 else 0
            }
        
        statistics = {
            'total_reports': len(reports),
            'total_employees': unique_employees,
            'total_departments': unique_departments,
            'total_hours': total_hours,
            'total_tasks': total_tasks,
            'avg_hours_per_employee': total_hours / unique_employees if unique_employees > 0 else 0,
            'avg_tasks_per_employee': total_tasks / unique_employees if unique_employees > 0 else 0,
            'department_summary': department_summary
        }
        
        self._record_operation('get_statistics', {'criteria': str(criteria)}, True)
        return statistics
    
    # 数据验证方法
    def _validate_employee(self, employee: Employee) -> bool:
        """验证员工数据"""
        if not employee.email or not employee.name:
            return False
        if not employee.department or not employee.role:
            return False
        return True
    
    def _validate_weekly_report(self, report: WeeklyReport) -> bool:
        """验证周报数据"""
        if not report.report_id or not report.week:
            return False
        if not report.employee or not self._validate_employee(report.employee):
            return False
        if not report.raw_text:
            return False
        return True
    
    def _record_operation(self, operation: str, params: Dict[str, Any], success: bool) -> None:
        """记录操作历史"""
        history_entry = {
            'timestamp': datetime.now(),
            'operation': operation,
            'params': params,
            'success': success
        }
        
        self._operation_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(self._operation_history) > 1000:
            self._operation_history = self._operation_history[-500:]
    
    def get_operation_statistics(self) -> Dict[str, Any]:
        """获取操作统计信息"""
        if not self._operation_history:
            return {'total_operations': 0}
        
        total_operations = len(self._operation_history)
        successful_operations = sum(1 for h in self._operation_history if h['success'])
        
        operation_counts = {}
        for history in self._operation_history:
            operation = history['operation']
            operation_counts[operation] = operation_counts.get(operation, 0) + 1
        
        return {
            'total_operations': total_operations,
            'successful_operations': successful_operations,
            'success_rate': successful_operations / total_operations * 100,
            'operation_counts': operation_counts,
            'last_operation': self._operation_history[-1]['timestamp'].isoformat()
        }
    
    def _cleanup(self) -> None:
        """清理资源"""
        # 清理仓储
        for repository in self.repositories.values():
            if hasattr(repository, 'cleanup'):
                try:
                    repository.cleanup()
                except Exception as e:
                    self.logger.error(f"数据仓储清理失败: {e}")
        
        # 清理历史记录
        self._operation_history.clear()
        
        self.logger.info("数据服务资源清理完成")
