#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件连接模块 - 增强版
支持智能重连、连接池管理、指数退避重试策略
"""
import imaplib
import email
import datetime
import logging
import time
import random
from typing import List, Dict, Any, Optional
from threading import Lock
import queue

class EmailConnection:
    """增强版邮件连接类 - 支持智能重连和连接池"""
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = config
        self.server = config.get("imap_server", "")
        self.port = config.get("port", 993)
        self.username = config.get("username", "")
        self.password = config.get("password", "")
        self.use_ssl = config.get("use_ssl", True)
        self.connected = False
        self.imap = None

        # 重连配置
        self.max_retries = config.get("max_retries", 5)
        self.base_delay = config.get("base_delay", 1)
        self.max_delay = config.get("max_delay", 60)
        self.retry_count = 0

        # 连接健康检查
        self.last_activity = time.time()
        self.health_check_interval = config.get("health_check_interval", 300)  # 5分钟

        # 连接锁
        self._lock = Lock()

    def _calculate_delay(self) -> float:
        """计算指数退避延迟时间"""
        delay = self.base_delay * (2 ** self.retry_count)
        # 添加随机抖动，避免雷群效应
        jitter = random.uniform(0.1, 0.3) * delay
        delay = min(delay + jitter, self.max_delay)
        return delay

    def _is_connection_healthy(self) -> bool:
        """检查连接健康状态"""
        if not self.connected or not self.imap:
            return False

        try:
            # 发送NOOP命令检查连接
            status, _ = self.imap.noop()
            if status == 'OK':
                self.last_activity = time.time()
                return True
        except Exception as e:
            self.logger.warning(f"连接健康检查失败: {e}")

        return False

    def connect(self) -> bool:
        """智能连接方法，支持自动重连和健康检查"""
        with self._lock:
            # 检查现有连接是否健康
            if self.connected and self._is_connection_healthy():
                return True

            # 如果连接不健康，先断开
            if self.connected:
                self.disconnect()

            # 尝试重新连接
            for attempt in range(self.max_retries + 1):
                try:
                    self.logger.info(f"尝试连接邮件服务器 (第{attempt + 1}次): {self.server}:{self.port}")

                    if self.use_ssl:
                        self.imap = imaplib.IMAP4_SSL(self.server, self.port)
                    else:
                        self.imap = imaplib.IMAP4(self.server, self.port)

                    self.imap.login(self.username, self.password)
                    self.connected = True
                    self.retry_count = 0  # 重置重试计数
                    self.last_activity = time.time()

                    self.logger.info(f"成功连接到邮件服务器: {self.server}:{self.port}")
                    return True

                except Exception as e:
                    self.logger.error(f"连接邮件服务器失败 (第{attempt + 1}次): {str(e)}")
                    self.connected = False
                    self.imap = None

                    if attempt < self.max_retries:
                        delay = self._calculate_delay()
                        self.logger.info(f"等待 {delay:.2f} 秒后重试...")
                        time.sleep(delay)
                        self.retry_count += 1

            self.logger.error(f"连接邮件服务器失败，已尝试 {self.max_retries + 1} 次")
            return False

    def disconnect(self) -> None:
        if self.imap:
            try:
                self.imap.logout()
                self.logger.info("已断开与邮件服务器的连接")
            except Exception as e:
                self.logger.error(f"断开连接失败: {str(e)}")
            finally:
                self.imap = None
                self.connected = False

    def search_emails(
        self,
        folder_name: str = "INBOX",
        max_emails: int = 0,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None,
        sender: Optional[str] = None,
    ) -> List[str]:
        if not self.connected and not self.connect():
            self.logger.error("无法连接到邮件服务器")
            return []
        try:
            self.imap.select(folder_name)
            search_criteria = []
            if start_date:
                search_criteria.append(f'SINCE {start_date.strftime("%d-%b-%Y")}')
            if end_date:
                search_criteria.append(f'BEFORE {(end_date + datetime.timedelta(days=1)).strftime("%d-%b-%Y")}')
            if sender:
                search_criteria.append(f'FROM "{sender}"')
            if search_criteria:
                search_str = " ".join(search_criteria)
                status, data = self.imap.search(None, search_str)
            else:
                status, data = self.imap.search(None, "ALL")
            if status != "OK":
                self.logger.error(f"搜索邮件失败: {status}")
                return []
            email_ids = data[0].split()
            if max_emails > 0 and len(email_ids) > max_emails:
                email_ids = email_ids[-max_emails:]
            email_ids = [email_id.decode("utf-8") for email_id in email_ids]
            self.logger.info(f"找到 {len(email_ids)} 封邮件")
            return email_ids
        except Exception as e:
            self.logger.error(f"搜索邮件失败: {str(e)}")
            return []

    def fetch_email(self, email_id: str, folder_name: str = "INBOX") -> Optional[bytes]:
        if not self.connected and not self.connect():
            self.logger.error("无法连接到邮件服务器")
            return None
        try:
            self.imap.select(folder_name)
            status, data = self.imap.fetch(email_id, "(RFC822)")
            if status != "OK":
                self.logger.error(f"获取邮件内容失败: {status}")
                return None
            return data[0][1]
        except Exception as e:
            self.logger.error(f"获取邮件内容失败: {str(e)}")
            return None