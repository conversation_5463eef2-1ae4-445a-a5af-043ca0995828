# 🏗️ AI驱动邮件周报分析系统 - 模块化组件化架构设计文档

## 📋 文档概述

**文档目的**: 定义高度模块化、组件化的系统架构，实现最大程度的代码复用
**设计原则**: 分层架构、组件化设计、接口抽象、依赖注入
**核心目标**: 零功能缺失、高度复用、易于维护、可扩展性强

## 🎯 核心设计原则

### 1. **SOLID原则严格遵循**
- **S** - 单一职责原则：每个组件只负责一个功能
- **O** - 开闭原则：对扩展开放，对修改关闭
- **L** - 里氏替换原则：子类可以替换父类
- **I** - 接口隔离原则：接口细粒度，避免冗余依赖
- **D** - 依赖倒置原则：依赖抽象而非具体实现

### 2. **分层架构设计**
```
┌─────────────────────────────────────────┐
│        Presentation Layer (UI)          │  ← 表现层：UI组件、页面、交互
├─────────────────────────────────────────┤
│       Application Layer (Service)       │  ← 应用层：业务服务、工作流
├─────────────────────────────────────────┤
│         Domain Layer (Core)             │  ← 领域层：业务逻辑、实体
├─────────────────────────────────────────┤
│     Infrastructure Layer (Data)         │  ← 基础设施层：数据访问、外部服务
└─────────────────────────────────────────┘
```

### 3. **组件化设计模式**
- **原子组件** (Atoms): 最小可复用单元 (按钮、输入框、图标)
- **分子组件** (Molecules): 原子组件组合 (搜索框、卡片、表单项)
- **有机体组件** (Organisms): 复杂业务组件 (数据表格、图表面板)
- **模板组件** (Templates): 页面布局模板 (仪表盘模板、分析模板)
- **页面组件** (Pages): 完整页面实现 (周报分析页、趋势分析页)

## 📦 模块化架构设计

### **1. 核心基础模块 (core/)**

#### **目录结构**
```
core/
├── __init__.py              # 模块导出
├── base.py                  # 基础抽象类
├── interfaces.py            # 接口定义
├── decorators.py            # 通用装饰器
├── exceptions.py            # 异常定义
├── types.py                 # 类型定义
├── utils.py                 # 工具函数
└── constants.py             # 常量定义
```

#### **A. 基础抽象类设计**
```python
# core/base.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

@dataclass
class ComponentConfig:
    """组件配置基类"""
    name: str
    version: str
    enabled: bool = True
    config: Dict[str, Any] = None

class BaseComponent(ABC):
    """所有组件的基类 - 提供统一的组件接口"""

    def __init__(self, config: ComponentConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._initialized = False

    @abstractmethod
    def initialize(self) -> bool:
        """初始化组件"""
        pass

    @abstractmethod
    def validate_config(self) -> bool:
        """验证配置"""
        pass

    def get_info(self) -> Dict[str, Any]:
        """获取组件信息"""
        return {
            'name': self.config.name,
            'version': self.config.version,
            'enabled': self.config.enabled,
            'initialized': self._initialized
        }

class BaseService(BaseComponent):
    """服务基类 - 提供依赖管理和生命周期管理"""

    def __init__(self, config: ComponentConfig, dependencies: List[BaseComponent] = None):
        super().__init__(config)
        self.dependencies = dependencies or []

    def check_dependencies(self) -> bool:
        """检查依赖是否满足"""
        for dep in self.dependencies:
            if not dep._initialized:
                self.logger.error(f"依赖组件 {dep.config.name} 未初始化")
                return False
        return True

class BaseRepository(BaseComponent):
    """数据访问基类 - 提供统一的CRUD接口"""

    @abstractmethod
    def create(self, entity: Any) -> Any:
        """创建实体"""
        pass

    @abstractmethod
    def get_by_id(self, entity_id: str) -> Optional[Any]:
        """根据ID获取实体"""
        pass

    @abstractmethod
    def update(self, entity: Any) -> Any:
        """更新实体"""
        pass

    @abstractmethod
    def delete(self, entity_id: str) -> bool:
        """删除实体"""
        pass

    @abstractmethod
    def query(self, criteria: Dict[str, Any]) -> List[Any]:
        """查询实体"""
        pass
```

#### **B. 接口定义**
```python
# core/interfaces.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from .types import AnalysisResult, VisualizationConfig, ProcessingContext

class IAnalyzer(ABC):
    """分析器接口 - 定义分析组件的标准接口"""

    @abstractmethod
    def analyze(self, data: Any, context: ProcessingContext) -> AnalysisResult:
        """执行分析"""
        pass

    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass

    @abstractmethod
    def get_name(self) -> str:
        """获取分析器名称"""
        pass

class IVisualizer(ABC):
    """可视化器接口 - 定义可视化组件的标准接口"""

    @abstractmethod
    def create_visualization(self, data: Any, config: VisualizationConfig) -> Any:
        """创建可视化"""
        pass

    @abstractmethod
    def get_supported_chart_types(self) -> List[str]:
        """获取支持的图表类型"""
        pass

    @abstractmethod
    def get_name(self) -> str:
        """获取可视化器名称"""
        pass

class IDataProcessor(ABC):
    """数据处理器接口 - 定义数据处理组件的标准接口"""

    @abstractmethod
    def process(self, data: Any, context: ProcessingContext) -> Any:
        """处理数据"""
        pass

    @abstractmethod
    def validate(self, data: Any) -> bool:
        """验证数据"""
        pass

class ICacheManager(ABC):
    """缓存管理器接口"""

    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        pass

    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存"""
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass

class IEventBus(ABC):
    """事件总线接口 - 支持组件间解耦通信"""

    @abstractmethod
    def publish(self, event_type: str, data: Any) -> None:
        """发布事件"""
        pass

    @abstractmethod
    def subscribe(self, event_type: str, handler: callable) -> None:
        """订阅事件"""
        pass
```

#### **C. 通用装饰器**
```python
# core/decorators.py
import time
import functools
from typing import Any, Callable
from .exceptions import ProcessingError

def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器 - 自动记录执行时间和性能指标"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            # 记录性能指标
            logger = getattr(args[0], 'logger', None)
            if logger:
                logger.info(f"{func.__name__} 执行时间: {execution_time:.3f}秒")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger = getattr(args[0], 'logger', None)
            if logger:
                logger.error(f"{func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {e}")
            raise
    return wrapper

def cache_result(ttl: int = 3600):
    """结果缓存装饰器 - 自动缓存函数结果"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cache_manager = getattr(args[0], 'cache_manager', None)
            if cache_manager:
                cached_result = cache_manager.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            if cache_manager:
                cache_manager.set(cache_key, result, ttl)

            return result
        return wrapper
    return decorator

def error_handler(default_return=None, log_error=True):
    """错误处理装饰器 - 统一异常处理"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_error:
                    logger = getattr(args[0], 'logger', None)
                    if logger:
                        logger.error(f"{func.__name__} 执行失败: {e}", exc_info=True)

                if default_return is not None:
                    return default_return
                raise ProcessingError(f"{func.__name__} 执行失败: {e}")
        return wrapper
    return decorator

def validate_input(validator: Callable):
    """输入验证装饰器 - 自动验证函数输入"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 验证输入
            if not validator(*args, **kwargs):
                raise ValueError("输入验证失败")
            return func(*args, **kwargs)
        return wrapper
    return decorator
```

### **2. 领域模型模块 (domain/)**

#### **目录结构**
```
domain/
├── __init__.py              # 模块导出
├── entities.py              # 实体定义
├── value_objects.py         # 值对象
├── repositories.py          # 仓储接口
├── services.py              # 领域服务
└── events.py                # 领域事件
```

#### **A. 实体定义**
```python
# domain/entities.py
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class TaskComplexity(Enum):
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"

class TaskCategory(Enum):
    DEVELOPMENT = "开发"
    TESTING = "测试"
    MEETING = "会议"
    TRAINING = "培训"
    SUPPORT = "支持"
    MANAGEMENT = "管理"

@dataclass
class Employee:
    """员工实体 - 核心业务实体"""
    email: str
    name: str
    department: str
    role: str
    level: Optional[str] = None
    skills: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def add_skill(self, skill: str) -> None:
        """添加技能"""
        if skill not in self.skills:
            self.skills.append(skill)
            self.updated_at = datetime.now()

    def remove_skill(self, skill: str) -> None:
        """移除技能"""
        if skill in self.skills:
            self.skills.remove(skill)
            self.updated_at = datetime.now()

@dataclass
class WorkItem:
    """工作项实体"""
    title: str
    description: str
    duration_hours: float
    complexity: TaskComplexity
    category: TaskCategory
    date: datetime
    employee_email: str
    tags: List[str] = field(default_factory=list)
    extra_data: Dict[str, Any] = field(default_factory=dict)

    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)

    def calculate_complexity_score(self) -> float:
        """计算复杂度分数"""
        complexity_scores = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0
        }
        return complexity_scores.get(self.complexity, 1.0)

@dataclass
class WeeklyReport:
    """周报实体 - 聚合根"""
    report_id: str
    employee: Employee
    week: str
    work_items: List[WorkItem]
    summary: Dict[str, Any]
    metrics: Dict[str, Any]
    ai_version: str
    raw_text: str
    tags: List[str] = field(default_factory=list)
    anomaly_flags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def calculate_total_hours(self) -> float:
        """计算总工时"""
        return sum(item.duration_hours for item in self.work_items)

    def get_work_items_by_category(self, category: TaskCategory) -> List[WorkItem]:
        """按类别获取工作项"""
        return [item for item in self.work_items if item.category == category]

    def add_anomaly_flag(self, flag: str) -> None:
        """添加异常标记"""
        if flag not in self.anomaly_flags:
            self.anomaly_flags.append(flag)
            self.updated_at = datetime.now()

@dataclass
class AnalysisResult:
    """分析结果实体"""
    result_id: str
    report_id: str
    analysis_type: str
    result_data: Dict[str, Any]
    confidence_score: float
    model_version: str
    processing_time: float
    created_at: datetime = field(default_factory=datetime.now)

    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """判断是否高置信度"""
        return self.confidence_score >= threshold
```

#### **B. 值对象定义**
```python
# domain/value_objects.py
from dataclasses import dataclass
from typing import Dict, Any, List, Tuple
from datetime import datetime

@dataclass(frozen=True)
class ProcessingContext:
    """处理上下文值对象 - 不可变对象"""
    user_id: str
    department: str
    role: str
    preferences: Dict[str, Any]
    timestamp: datetime

    def with_updated_timestamp(self) -> 'ProcessingContext':
        """创建更新时间戳的新实例"""
        return ProcessingContext(
            user_id=self.user_id,
            department=self.department,
            role=self.role,
            preferences=self.preferences,
            timestamp=datetime.now()
        )

@dataclass(frozen=True)
class VisualizationConfig:
    """可视化配置值对象"""
    chart_type: str
    title: str
    width: int
    height: int
    color_scheme: str
    interactive: bool = True
    export_formats: List[str] = None

    def with_size(self, width: int, height: int) -> 'VisualizationConfig':
        """创建新尺寸的配置"""
        return VisualizationConfig(
            chart_type=self.chart_type,
            title=self.title,
            width=width,
            height=height,
            color_scheme=self.color_scheme,
            interactive=self.interactive,
            export_formats=self.export_formats
        )

@dataclass(frozen=True)
class FilterCriteria:
    """筛选条件值对象"""
    department: str = None
    role: str = None
    week: str = None
    tags: List[str] = None
    date_range: Tuple[datetime, datetime] = None

    def is_empty(self) -> bool:
        """判断是否为空筛选条件"""
        return all(value is None for value in [
            self.department, self.role, self.week, self.tags, self.date_range
        ])

@dataclass(frozen=True)
class MetricThresholds:
    """指标阈值值对象"""
    workload_low: float = 0.5
    workload_high: float = 1.2
    quality_low: float = 0.6
    quality_high: float = 0.9
    efficiency_low: float = 0.7
    efficiency_high: float = 1.0

    def classify_workload(self, value: float) -> str:
        """分类工作负载"""
        if value < self.workload_low:
            return "不足"
        elif value > self.workload_high:
            return "过载"
        else:
            return "适中"
```

### **3. 应用服务模块 (services/)**

#### **目录结构**
```
services/
├── __init__.py              # 模块导出
├── analysis/                # 分析服务
│   ├── __init__.py
│   ├── analysis_service.py  # 分析服务主类
│   ├── ml_analyzer.py       # 机器学习分析器
│   ├── clustering_analyzer.py # 聚类分析器
│   ├── trend_analyzer.py    # 趋势分析器
│   └── anomaly_analyzer.py  # 异常分析器
├── visualization/           # 可视化服务
│   ├── __init__.py
│   ├── visualization_service.py # 可视化服务主类
│   ├── plotly_visualizer.py # Plotly可视化器
│   ├── chart_factory.py     # 图表工厂
│   └── dashboard_builder.py # 仪表盘构建器
├── data/                    # 数据服务
│   ├── __init__.py
│   ├── data_service.py      # 数据服务主类
│   ├── report_repository.py # 周报仓储
│   ├── employee_repository.py # 员工仓储
│   └── cache_service.py     # 缓存服务
└── workflow/                # 工作流服务
    ├── __init__.py
    ├── workflow_engine.py   # 工作流引擎
    ├── task_scheduler.py    # 任务调度器
    └── event_handler.py     # 事件处理器
```

#### **A. 分析服务设计**
```python
# services/analysis/analysis_service.py
from typing import List, Dict, Any, Optional
from core.base import BaseService
from core.interfaces import IAnalyzer
from core.decorators import performance_monitor, cache_result
from domain.entities import WeeklyReport, AnalysisResult
from domain.value_objects import ProcessingContext

class AnalysisService(BaseService):
    """分析服务 - 统一分析入口，协调各种分析器"""

    def __init__(self, config, analyzers: List[IAnalyzer], cache_manager=None):
        super().__init__(config)
        self.analyzers = {analyzer.get_name(): analyzer for analyzer in analyzers}
        self.cache_manager = cache_manager

    @performance_monitor
    @cache_result(ttl=1800)  # 缓存30分钟
    def analyze_report(self, report: WeeklyReport, analysis_types: List[str] = None) -> List[AnalysisResult]:
        """分析周报 - 支持多种分析类型"""
        if analysis_types is None:
            analysis_types = list(self.analyzers.keys())

        context = self._create_context(report)
        results = []

        for analysis_type in analysis_types:
            if analysis_type in self.analyzers:
                try:
                    analyzer = self.analyzers[analysis_type]
                    result = analyzer.analyze(report, context)
                    results.append(result)
                    self.logger.info(f"完成 {analysis_type} 分析，置信度: {result.confidence_score}")
                except Exception as e:
                    self.logger.error(f"{analysis_type} 分析失败: {e}")

        return results

    @performance_monitor
    def batch_analyze(self, reports: List[WeeklyReport], parallel: bool = True) -> List[List[AnalysisResult]]:
        """批量分析 - 支持并行处理"""
        if parallel:
            return self._parallel_batch_analyze(reports)
        else:
            return [self.analyze_report(report) for report in reports]

    def get_available_analyzers(self) -> List[str]:
        """获取可用的分析器列表"""
        return list(self.analyzers.keys())

    def _create_context(self, report: WeeklyReport) -> ProcessingContext:
        """创建处理上下文"""
        return ProcessingContext(
            user_id=report.employee.email,
            department=report.employee.department,
            role=report.employee.role,
            preferences={},
            timestamp=datetime.now()
        )

    def _parallel_batch_analyze(self, reports: List[WeeklyReport]) -> List[List[AnalysisResult]]:
        """并行批量分析"""
        import concurrent.futures

        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(self.analyze_report, report) for report in reports]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        return results
```

### **4. UI组件模块 (components/)**

#### **目录结构**
```
components/
├── __init__.py              # 模块导出
├── atoms/                   # 原子组件
│   ├── __init__.py
│   ├── button.py           # 按钮组件
│   ├── input.py            # 输入组件
│   ├── text.py             # 文本组件
│   ├── icon.py             # 图标组件
│   └── loading.py          # 加载组件
├── molecules/              # 分子组件
│   ├── __init__.py
│   ├── search_box.py       # 搜索框
│   ├── filter_panel.py     # 筛选面板
│   ├── metric_card.py      # 指标卡片
│   ├── data_table.py       # 数据表格
│   └── chart_container.py  # 图表容器
├── organisms/              # 有机体组件
│   ├── __init__.py
│   ├── analysis_panel.py   # 分析面板
│   ├── dashboard_header.py # 仪表盘头部
│   ├── navigation_sidebar.py # 导航侧边栏
│   └── report_viewer.py    # 报告查看器
├── templates/              # 模板组件
│   ├── __init__.py
│   ├── dashboard_template.py # 仪表盘模板
│   ├── analysis_template.py # 分析模板
│   └── report_template.py  # 报告模板
└── pages/                  # 页面组件
    ├── __init__.py
    ├── unified_dashboard.py # 统一仪表盘
    ├── analysis_workspace.py # 分析工作台
    └── insight_center.py   # 洞察中心
```

#### **A. 原子组件设计**
```python
# components/atoms/button.py
import streamlit as st
from typing import Optional, Callable, Dict, Any
from enum import Enum

class ButtonType(Enum):
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    DANGER = "danger"

class ButtonSize(Enum):
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"

class Button:
    """按钮原子组件 - 提供统一的按钮接口"""

    @staticmethod
    def render(
        label: str,
        button_type: ButtonType = ButtonType.PRIMARY,
        size: ButtonSize = ButtonSize.MEDIUM,
        disabled: bool = False,
        key: str = None,
        on_click: Callable = None,
        **kwargs
    ) -> bool:
        """渲染按钮"""
        # 根据类型和大小设置样式
        css_class = f"btn-{button_type.value} btn-{size.value}"

        # 使用Streamlit的button组件
        return st.button(
            label,
            key=key,
            disabled=disabled,
            type=button_type.value if button_type in [ButtonType.PRIMARY, ButtonType.SECONDARY] else "secondary",
            on_click=on_click,
            **kwargs
        )

    @staticmethod
    def primary(label: str, **kwargs) -> bool:
        """主要按钮"""
        return Button.render(label, ButtonType.PRIMARY, **kwargs)

    @staticmethod
    def secondary(label: str, **kwargs) -> bool:
        """次要按钮"""
        return Button.render(label, ButtonType.SECONDARY, **kwargs)

    @staticmethod
    def success(label: str, **kwargs) -> bool:
        """成功按钮"""
        return Button.render(label, ButtonType.SUCCESS, **kwargs)

# components/atoms/input.py
class Input:
    """输入框原子组件 - 提供统一的输入接口"""

    @staticmethod
    def text(
        label: str,
        value: str = "",
        placeholder: str = "",
        max_chars: int = None,
        key: str = None,
        help: str = None,
        **kwargs
    ) -> str:
        """文本输入框"""
        return st.text_input(
            label,
            value=value,
            placeholder=placeholder,
            max_chars=max_chars,
            key=key,
            help=help,
            **kwargs
        )

    @staticmethod
    def textarea(
        label: str,
        value: str = "",
        height: int = 200,
        max_chars: int = None,
        key: str = None,
        help: str = None,
        **kwargs
    ) -> str:
        """文本域"""
        return st.text_area(
            label,
            value=value,
            height=height,
            max_chars=max_chars,
            key=key,
            help=help,
            **kwargs
        )

    @staticmethod
    def number(
        label: str,
        value: float = 0.0,
        min_value: float = None,
        max_value: float = None,
        step: float = 1.0,
        key: str = None,
        help: str = None,
        **kwargs
    ) -> float:
        """数字输入框"""
        return st.number_input(
            label,
            value=value,
            min_value=min_value,
            max_value=max_value,
            step=step,
            key=key,
            help=help,
            **kwargs
        )

    @staticmethod
    def selectbox(
        label: str,
        options: list,
        index: int = 0,
        key: str = None,
        help: str = None,
        **kwargs
    ):
        """选择框"""
        return st.selectbox(
            label,
            options,
            index=index,
            key=key,
            help=help,
            **kwargs
        )

    @staticmethod
    def multiselect(
        label: str,
        options: list,
        default: list = None,
        key: str = None,
        help: str = None,
        **kwargs
    ) -> list:
        """多选框"""
        return st.multiselect(
            label,
            options,
            default=default or [],
            key=key,
            help=help,
            **kwargs
        )

# components/atoms/text.py
class Text:
    """文本原子组件 - 提供统一的文本显示接口"""

    @staticmethod
    def title(text: str, anchor: str = None) -> None:
        """标题"""
        st.title(text, anchor=anchor)

    @staticmethod
    def header(text: str, anchor: str = None) -> None:
        """头部"""
        st.header(text, anchor=anchor)

    @staticmethod
    def subheader(text: str, anchor: str = None) -> None:
        """子头部"""
        st.subheader(text, anchor=anchor)

    @staticmethod
    def markdown(text: str, unsafe_allow_html: bool = False) -> None:
        """Markdown文本"""
        st.markdown(text, unsafe_allow_html=unsafe_allow_html)

    @staticmethod
    def code(code: str, language: str = None) -> None:
        """代码块"""
        st.code(code, language=language)

    @staticmethod
    def caption(text: str) -> None:
        """说明文字"""
        st.caption(text)

    @staticmethod
    def metric(label: str, value: str, delta: str = None, delta_color: str = "normal") -> None:
        """指标显示"""
        st.metric(label, value, delta, delta_color)
```

#### **B. 分子组件设计**
```python
# components/molecules/metric_card.py
from typing import Optional, Dict, Any
from ..atoms.text import Text
from ..atoms.icon import Icon

class MetricCard:
    """指标卡片分子组件 - 组合文本和图标显示指标"""

    @staticmethod
    def render(
        title: str,
        value: str,
        delta: str = None,
        delta_color: str = "normal",
        icon: str = None,
        description: str = None,
        trend_data: list = None
    ) -> None:
        """渲染指标卡片"""
        with st.container():
            col1, col2 = st.columns([3, 1])

            with col1:
                Text.metric(title, value, delta, delta_color)
                if description:
                    Text.caption(description)

            with col2:
                if icon:
                    Icon.render(icon, size="large")

                if trend_data:
                    # 简单的趋势图
                    st.line_chart(trend_data)

# components/molecules/filter_panel.py
from typing import List, Dict, Any, Callable
from ..atoms.input import Input
from ..atoms.button import Button

class FilterPanel:
    """筛选面板分子组件 - 组合多个输入组件"""

    @staticmethod
    def render(
        filters: Dict[str, Dict[str, Any]],
        on_filter_change: Callable = None,
        key_prefix: str = "filter"
    ) -> Dict[str, Any]:
        """渲染筛选面板"""
        filter_values = {}

        with st.expander("筛选条件", expanded=True):
            cols = st.columns(len(filters))

            for i, (filter_name, filter_config) in enumerate(filters.items()):
                with cols[i]:
                    filter_type = filter_config.get('type', 'text')
                    label = filter_config.get('label', filter_name)
                    options = filter_config.get('options', [])
                    default = filter_config.get('default')

                    key = f"{key_prefix}_{filter_name}"

                    if filter_type == 'selectbox':
                        value = Input.selectbox(label, options, key=key)
                    elif filter_type == 'multiselect':
                        value = Input.multiselect(label, options, default=default, key=key)
                    elif filter_type == 'text':
                        value = Input.text(label, value=default or "", key=key)
                    elif filter_type == 'number':
                        value = Input.number(label, value=default or 0, key=key)
                    else:
                        value = Input.text(label, key=key)

                    filter_values[filter_name] = value

            # 应用筛选按钮
            if Button.primary("应用筛选", key=f"{key_prefix}_apply"):
                if on_filter_change:
                    on_filter_change(filter_values)

        return filter_values

# components/molecules/data_table.py
import pandas as pd
from typing import List, Dict, Any, Callable

class DataTable:
    """数据表格分子组件 - 提供统一的表格显示接口"""

    @staticmethod
    def render(
        data: pd.DataFrame,
        columns: List[str] = None,
        sortable: bool = True,
        filterable: bool = True,
        paginated: bool = True,
        page_size: int = 20,
        selection_mode: str = None,  # 'single', 'multiple', None
        on_row_select: Callable = None,
        key: str = "data_table"
    ) -> Dict[str, Any]:
        """渲染数据表格"""
        result = {
            'selected_rows': [],
            'filtered_data': data,
            'current_page': 1
        }

        if data.empty:
            st.info("暂无数据")
            return result

        # 列选择
        if columns:
            display_data = data[columns]
        else:
            display_data = data

        # 筛选功能
        if filterable:
            with st.expander("表格筛选"):
                filter_col = st.selectbox("筛选列", display_data.columns, key=f"{key}_filter_col")
                filter_value = st.text_input("筛选值", key=f"{key}_filter_value")

                if filter_value:
                    display_data = display_data[
                        display_data[filter_col].astype(str).str.contains(filter_value, case=False, na=False)
                    ]
                    result['filtered_data'] = display_data

        # 分页功能
        if paginated and len(display_data) > page_size:
            total_pages = (len(display_data) - 1) // page_size + 1
            current_page = st.number_input(
                "页码",
                min_value=1,
                max_value=total_pages,
                value=1,
                key=f"{key}_page"
            )

            start_idx = (current_page - 1) * page_size
            end_idx = start_idx + page_size
            display_data = display_data.iloc[start_idx:end_idx]
            result['current_page'] = current_page

        # 显示表格
        if selection_mode:
            selected_rows = st.dataframe(
                display_data,
                use_container_width=True,
                key=f"{key}_dataframe",
                on_select="rerun" if selection_mode else None,
                selection_mode=selection_mode
            )
            result['selected_rows'] = selected_rows.selection.rows if hasattr(selected_rows, 'selection') else []
        else:
            st.dataframe(display_data, use_container_width=True, key=f"{key}_dataframe")

        return result
```

## 🎯 代码复用策略

### **1. 组件复用层次**

#### **A. 水平复用 - 同层组件间复用**
```python
# 原子组件间的复用
class Icon:
    @staticmethod
    def render(name: str, size: str = "medium", color: str = "default"):
        # 图标渲染逻辑
        pass

class Button:
    @staticmethod
    def with_icon(label: str, icon_name: str, **kwargs):
        # 复用Icon组件
        col1, col2 = st.columns([1, 4])
        with col1:
            Icon.render(icon_name)
        with col2:
            return Button.render(label, **kwargs)
```

#### **B. 垂直复用 - 跨层组件复用**
```python
# 分子组件复用原子组件
class SearchBox:
    @staticmethod
    def render(placeholder: str = "搜索...", **kwargs):
        col1, col2 = st.columns([4, 1])
        with col1:
            query = Input.text("", placeholder=placeholder, **kwargs)
        with col2:
            search_clicked = Button.primary("搜索")
        return query, search_clicked

# 有机体组件复用分子组件
class AnalysisPanel:
    @staticmethod
    def render(data_source, filters_config):
        # 复用筛选面板
        filters = FilterPanel.render(filters_config)

        # 复用数据表格
        filtered_data = apply_filters(data_source, filters)
        DataTable.render(filtered_data)
```

### **2. 配置驱动复用**

#### **A. 组件配置化**
```python
# 配置驱动的图表组件
class ConfigurableChart:
    @staticmethod
    def render(data, config: Dict[str, Any]):
        chart_type = config.get('type', 'line')
        title = config.get('title', '')
        colors = config.get('colors', ['#1f77b4'])

        if chart_type == 'line':
            return PlotlyVisualizer.create_line_chart(data, title, colors)
        elif chart_type == 'bar':
            return PlotlyVisualizer.create_bar_chart(data, title, colors)
        # ... 其他图表类型
```

#### **B. 模板驱动复用**
```python
# 页面模板配置
DASHBOARD_TEMPLATE = {
    'layout': 'three_column',
    'components': [
        {
            'type': 'metric_cards',
            'position': 'top',
            'config': {'metrics': ['total_hours', 'task_count', 'efficiency']}
        },
        {
            'type': 'chart_panel',
            'position': 'left',
            'config': {'chart_type': 'line', 'data_source': 'trend_data'}
        },
        {
            'type': 'data_table',
            'position': 'right',
            'config': {'columns': ['name', 'department', 'hours'], 'paginated': True}
        }
    ]
}

class TemplateRenderer:
    @staticmethod
    def render_from_config(template_config: Dict[str, Any], data_context: Dict[str, Any]):
        """根据配置渲染页面模板"""
        layout = template_config['layout']
        components = template_config['components']

        if layout == 'three_column':
            col1, col2, col3 = st.columns([1, 2, 2])
            columns = [col1, col2, col3]

        for component in components:
            component_type = component['type']
            position = component['position']
            config = component['config']

            # 根据组件类型和配置渲染组件
            ComponentFactory.create_component(component_type, config, data_context)
```

### **3. 工厂模式复用**

#### **A. 组件工厂**
```python
class ComponentFactory:
    """组件工厂 - 统一创建各种组件"""

    _component_registry = {
        'button': Button,
        'input': Input,
        'text': Text,
        'metric_card': MetricCard,
        'filter_panel': FilterPanel,
        'data_table': DataTable,
        'chart': ConfigurableChart
    }

    @classmethod
    def register_component(cls, name: str, component_class):
        """注册新组件"""
        cls._component_registry[name] = component_class

    @classmethod
    def create_component(cls, component_type: str, config: Dict[str, Any], **kwargs):
        """创建组件实例"""
        if component_type not in cls._component_registry:
            raise ValueError(f"未知组件类型: {component_type}")

        component_class = cls._component_registry[component_type]
        return component_class.render(**config, **kwargs)

# 使用示例
ComponentFactory.create_component('button', {'label': '提交', 'type': 'primary'})
ComponentFactory.create_component('data_table', {'data': df, 'paginated': True})
```

#### **B. 服务工厂**
```python
class ServiceFactory:
    """服务工厂 - 统一创建和管理服务"""

    _services = {}

    @classmethod
    def register_service(cls, name: str, service_class, config: Dict[str, Any]):
        """注册服务"""
        service_instance = service_class(config)
        cls._services[name] = service_instance
        return service_instance

    @classmethod
    def get_service(cls, name: str):
        """获取服务实例"""
        if name not in cls._services:
            raise ValueError(f"服务 {name} 未注册")
        return cls._services[name]

    @classmethod
    def initialize_all_services(cls):
        """初始化所有服务"""
        for service in cls._services.values():
            service.initialize()

# 服务注册和使用
ServiceFactory.register_service('analysis', AnalysisService, analysis_config)
ServiceFactory.register_service('visualization', VisualizationService, viz_config)

# 获取服务
analysis_service = ServiceFactory.get_service('analysis')
viz_service = ServiceFactory.get_service('visualization')
```

## 📋 详细开发计划

### **阶段一：核心基础设施 (Week 1-2)**

#### **Week 1: 核心模块开发**
**目标**: 建立系统基础架构

**任务清单**:
1. **核心基础模块 (core/)**
   - [ ] 创建基础抽象类 (`base.py`)
   - [ ] 定义核心接口 (`interfaces.py`)
   - [ ] 实现通用装饰器 (`decorators.py`)
   - [ ] 定义异常类型 (`exceptions.py`)
   - [ ] 创建类型定义 (`types.py`)

2. **领域模型模块 (domain/)**
   - [ ] 定义核心实体 (`entities.py`)
   - [ ] 创建值对象 (`value_objects.py`)
   - [ ] 设计仓储接口 (`repositories.py`)
   - [ ] 实现领域服务 (`services.py`)

3. **依赖注入容器**
   - [ ] 实现IoC容器
   - [ ] 配置服务注册
   - [ ] 建立依赖关系

**验收标准**:
- 所有核心接口定义完成
- 基础抽象类可正常继承
- 装饰器功能测试通过
- 领域模型设计评审通过

#### **Week 2: 服务层架构**
**目标**: 实现应用服务层

**任务清单**:
1. **分析服务模块 (services/analysis/)**
   - [ ] 实现分析服务主类
   - [ ] 开发ML分析器
   - [ ] 创建聚类分析器
   - [ ] 实现趋势分析器
   - [ ] 开发异常分析器

2. **可视化服务模块 (services/visualization/)**
   - [ ] 实现可视化服务主类
   - [ ] 开发Plotly可视化器
   - [ ] 创建图表工厂
   - [ ] 实现仪表盘构建器

3. **数据服务模块 (services/data/)**
   - [ ] 实现数据服务主类
   - [ ] 开发仓储实现
   - [ ] 创建缓存服务
   - [ ] 实现数据验证

**验收标准**:
- 所有服务接口实现完成
- 服务间依赖关系正确
- 单元测试覆盖率 ≥ 80%
- 集成测试通过

### **阶段二：UI组件系统 (Week 3-4)**

#### **Week 3: 原子和分子组件**
**目标**: 构建基础UI组件库

**任务清单**:
1. **原子组件开发 (components/atoms/)**
   - [ ] 按钮组件 (`button.py`)
   - [ ] 输入组件 (`input.py`)
   - [ ] 文本组件 (`text.py`)
   - [ ] 图标组件 (`icon.py`)
   - [ ] 加载组件 (`loading.py`)

2. **分子组件开发 (components/molecules/)**
   - [ ] 搜索框组件 (`search_box.py`)
   - [ ] 筛选面板 (`filter_panel.py`)
   - [ ] 指标卡片 (`metric_card.py`)
   - [ ] 数据表格 (`data_table.py`)
   - [ ] 图表容器 (`chart_container.py`)

3. **组件测试**
   - [ ] 组件单元测试
   - [ ] 组件集成测试
   - [ ] 视觉回归测试

**验收标准**:
- 所有原子组件功能完整
- 分子组件正确组合原子组件
- 组件API设计一致
- 组件文档完整

#### **Week 4: 有机体和模板组件**
**目标**: 构建复杂业务组件

**任务清单**:
1. **有机体组件开发 (components/organisms/)**
   - [ ] 分析面板 (`analysis_panel.py`)
   - [ ] 仪表盘头部 (`dashboard_header.py`)
   - [ ] 导航侧边栏 (`navigation_sidebar.py`)
   - [ ] 报告查看器 (`report_viewer.py`)

2. **模板组件开发 (components/templates/)**
   - [ ] 仪表盘模板 (`dashboard_template.py`)
   - [ ] 分析模板 (`analysis_template.py`)
   - [ ] 报告模板 (`report_template.py`)

3. **组件工厂实现**
   - [ ] 组件注册机制
   - [ ] 动态组件创建
   - [ ] 配置驱动渲染

**验收标准**:
- 有机体组件业务逻辑正确
- 模板组件布局合理
- 组件工厂功能完整
- 组件复用率 ≥ 80%

### **阶段三：页面整合与优化 (Week 5-6)**

#### **Week 5: 统一页面开发**
**目标**: 实现统一仪表盘和核心页面

**任务清单**:
1. **页面组件开发 (components/pages/)**
   - [ ] 统一仪表盘 (`unified_dashboard.py`)
   - [ ] 分析工作台 (`analysis_workspace.py`)
   - [ ] 洞察中心 (`insight_center.py`)

2. **页面路由系统**
   - [ ] 路由配置
   - [ ] 页面导航
   - [ ] 状态管理

3. **数据流整合**
   - [ ] 页面间数据传递
   - [ ] 全局状态管理
   - [ ] 事件总线实现

**验收标准**:
- 统一仪表盘功能完整
- 页面间导航流畅
- 数据联动正确
- 用户体验良好

#### **Week 6: 性能优化与测试**
**目标**: 系统性能优化和全面测试

**任务清单**:
1. **性能优化**
   - [ ] 组件渲染优化
   - [ ] 数据加载优化
   - [ ] 缓存策略优化
   - [ ] 内存使用优化

2. **全面测试**
   - [ ] 端到端测试
   - [ ] 性能测试
   - [ ] 兼容性测试
   - [ ] 用户验收测试

3. **文档完善**
   - [ ] 组件使用文档
   - [ ] 开发者指南
   - [ ] 部署文档
   - [ ] 维护手册

**验收标准**:
- 页面加载时间 ≤ 2秒
- 组件渲染时间 ≤ 500ms
- 测试覆盖率 ≥ 90%
- 文档完整性 100%

### **阶段四：系统集成与部署 (Week 7)**

#### **Week 7: 系统集成与上线**
**目标**: 完整系统集成和生产部署

**任务清单**:
1. **系统集成**
   - [ ] 模块间集成测试
   - [ ] 数据一致性验证
   - [ ] 接口兼容性测试
   - [ ] 错误处理验证

2. **部署准备**
   - [ ] 容器化配置
   - [ ] 环境配置管理
   - [ ] 监控告警设置
   - [ ] 备份恢复策略

3. **上线发布**
   - [ ] 生产环境部署
   - [ ] 数据迁移
   - [ ] 用户培训
   - [ ] 运维交接

**验收标准**:
- 系统稳定运行
- 所有功能正常
- 性能指标达标
- 用户反馈良好

## 🔧 实施指南

### **1. 开发环境配置**

#### **A. 项目结构初始化**
```bash
# 创建项目目录结构
mkdir -p {core,domain,services,components,infrastructure,tests}
mkdir -p components/{atoms,molecules,organisms,templates,pages}
mkdir -p services/{analysis,visualization,data,workflow}

# 初始化Python包
touch {core,domain,services,components,infrastructure,tests}/__init__.py
touch components/{atoms,molecules,organisms,templates,pages}/__init__.py
touch services/{analysis,visualization,data,workflow}/__init__.py
```

#### **B. 依赖管理配置**
```python
# requirements-modular.txt
# 核心依赖
pydantic>=2.0.0
dependency-injector>=4.41.0
structlog>=23.1.0

# UI组件依赖
streamlit>=1.28.0
plotly>=5.17.0
pandas>=2.0.0

# 服务依赖
fastapi>=0.104.0
sqlalchemy>=2.0.0
redis>=5.0.0

# 测试依赖
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
```

### **2. 代码生成工具**

#### **A. 组件生成器**
```python
# tools/component_generator.py
class ComponentGenerator:
    """组件代码生成器"""

    @staticmethod
    def generate_atom(name: str, props: List[str]):
        """生成原子组件代码"""
        template = """
import streamlit as st
from typing import Optional

class {name}:
    \"\"\"原子组件 - {name}\"\"\"

    @staticmethod
    def render({props}) -> None:
        \"\"\"渲染{name}组件\"\"\"
        # TODO: 实现组件逻辑
        pass
"""
        props_str = ", ".join([f"{prop}: str" for prop in props])
        return template.format(name=name, props=props_str)

    @staticmethod
    def generate_molecule(name: str, atoms: List[str]):
        """生成分子组件代码"""
        # 生成分子组件模板
        pass

# 使用示例
generator = ComponentGenerator()
button_code = generator.generate_atom("Button", ["label", "type", "size"])
```

### **3. 质量保证**

#### **A. 代码质量检查**
```python
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3.9

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=100]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black]
```

#### **B. 测试策略**
```python
# tests/conftest.py
import pytest
from unittest.mock import Mock
from core.base import ComponentConfig

@pytest.fixture
def mock_config():
    return ComponentConfig(
        name="test_component",
        version="1.0.0",
        enabled=True,
        config={}
    )

@pytest.fixture
def mock_logger():
    return Mock()

# tests/test_components.py
def test_button_render(mock_config, mock_logger):
    """测试按钮组件渲染"""
    from components.atoms.button import Button

    # 测试基本渲染
    result = Button.render("Test Button")
    assert result is not None

    # 测试不同类型
    result = Button.primary("Primary Button")
    assert result is not None
```

## 📊 成功指标

### **1. 技术指标**
- **代码复用率**: ≥ 80%
- **组件覆盖率**: 100%
- **测试覆盖率**: ≥ 90%
- **性能指标**: 页面加载 ≤ 2秒

### **2. 质量指标**
- **代码质量**: SonarQube评分 ≥ A级
- **文档完整性**: 100%
- **接口一致性**: 100%
- **错误处理**: 100%覆盖

### **3. 维护指标**
- **新功能开发效率**: 提升 ≥ 50%
- **Bug修复时间**: 减少 ≥ 60%
- **代码维护成本**: 降低 ≥ 40%
- **团队开发效率**: 提升 ≥ 30%

这个模块化组件化架构设计确保了系统的高度可复用性、可维护性和可扩展性，为后续的功能开发和系统演进奠定了坚实的基础。
