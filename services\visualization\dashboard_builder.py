#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
仪表盘构建器

模块描述: 构建交互式仪表盘，整合多个图表和分析结果
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .chart_factory, .plotly_visualizer, domain.entities
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import json

from .chart_factory import ChartFactory
from .plotly_visualizer import PlotlyVisualizer
from domain.entities import WeeklyReport, AnalysisResult
from domain.value_objects import VisualizationConfig


class DashboardBuilder:
    """
    仪表盘构建器
    
    功能：
    - 构建多图表仪表盘
    - 生成交互式布局
    - 支持自定义主题
    - 导出仪表盘
    """
    
    def __init__(self, chart_factory: ChartFactory = None):
        """
        初始化仪表盘构建器
        
        Args:
            chart_factory: 图表工厂实例
        """
        self.chart_factory = chart_factory or ChartFactory()
        
        # 预定义的仪表盘布局
        self.dashboard_layouts = {
            'executive': self._create_executive_layout,
            'operational': self._create_operational_layout,
            'analytical': self._create_analytical_layout,
            'team_performance': self._create_team_performance_layout,
            'project_overview': self._create_project_overview_layout
        }
        
        # 仪表盘主题
        self.themes = {
            'light': {
                'background_color': '#ffffff',
                'text_color': '#333333',
                'grid_color': '#f0f0f0',
                'accent_color': '#1f77b4'
            },
            'dark': {
                'background_color': '#2c3e50',
                'text_color': '#ecf0f1',
                'grid_color': '#34495e',
                'accent_color': '#3498db'
            },
            'corporate': {
                'background_color': '#f8f9fa',
                'text_color': '#212529',
                'grid_color': '#dee2e6',
                'accent_color': '#007bff'
            }
        }
    
    def build_dashboard(self, reports: List[WeeklyReport], 
                       analysis_results: List[AnalysisResult] = None,
                       layout_type: str = 'operational',
                       theme: str = 'light',
                       custom_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        构建仪表盘
        
        Args:
            reports: 周报列表
            analysis_results: 分析结果列表
            layout_type: 布局类型
            theme: 主题
            custom_config: 自定义配置
            
        Returns:
            Dict[str, Any]: 仪表盘数据
        """
        if layout_type not in self.dashboard_layouts:
            raise ValueError(f"不支持的布局类型: {layout_type}")
        
        # 获取布局函数
        layout_func = self.dashboard_layouts[layout_type]
        
        # 构建仪表盘
        dashboard = layout_func(reports, analysis_results, theme, custom_config or {})
        
        # 添加元数据
        dashboard['metadata'] = {
            'created_at': datetime.now().isoformat(),
            'layout_type': layout_type,
            'theme': theme,
            'report_count': len(reports),
            'analysis_result_count': len(analysis_results) if analysis_results else 0,
            'builder_version': '1.0.0'
        }
        
        return dashboard
    
    def export_dashboard(self, dashboard: Dict[str, Any], 
                        format: str = 'html',
                        filename: str = None) -> str:
        """
        导出仪表盘
        
        Args:
            dashboard: 仪表盘数据
            format: 导出格式 (html, json)
            filename: 文件名
            
        Returns:
            str: 导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dashboard_{timestamp}.{format}"
        
        if format.lower() == 'html':
            return self._export_html_dashboard(dashboard, filename)
        elif format.lower() == 'json':
            return self._export_json_dashboard(dashboard, filename)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _create_executive_layout(self, reports: List[WeeklyReport],
                               analysis_results: List[AnalysisResult],
                               theme: str,
                               custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建高管仪表盘布局"""
        dashboard = {
            'title': custom_config.get('title', '高管工作分析仪表盘'),
            'layout': 'executive',
            'theme': theme,
            'sections': []
        }
        
        # 1. 关键指标概览
        kpi_section = {
            'type': 'kpi_overview',
            'title': '关键指标',
            'metrics': self._calculate_kpi_metrics(reports, analysis_results)
        }
        dashboard['sections'].append(kpi_section)
        
        # 2. 工作量趋势
        try:
            workload_chart = self.chart_factory.create_workload_trend_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '工作量趋势',
                'chart': workload_chart,
                'size': 'large'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '工作量趋势',
                'error': str(e)
            })
        
        # 3. 部门对比
        try:
            dept_chart = self.chart_factory.create_department_comparison_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '部门对比',
                'chart': dept_chart,
                'size': 'medium'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '部门对比',
                'error': str(e)
            })
        
        # 4. 效率分析（如果有分析结果）
        if analysis_results:
            try:
                efficiency_chart = self.chart_factory.create_efficiency_metrics_chart(analysis_results)
                dashboard['sections'].append({
                    'type': 'chart',
                    'title': '效率分析',
                    'chart': efficiency_chart,
                    'size': 'medium'
                })
            except Exception as e:
                dashboard['sections'].append({
                    'type': 'error',
                    'title': '效率分析',
                    'error': str(e)
                })
        
        return dashboard
    
    def _create_operational_layout(self, reports: List[WeeklyReport],
                                 analysis_results: List[AnalysisResult],
                                 theme: str,
                                 custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建运营仪表盘布局"""
        dashboard = {
            'title': custom_config.get('title', '运营工作分析仪表盘'),
            'layout': 'operational',
            'theme': theme,
            'sections': []
        }
        
        # 1. 任务分布
        try:
            task_dist_chart = self.chart_factory.create_task_distribution_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '任务分布',
                'chart': task_dist_chart,
                'size': 'medium'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '任务分布',
                'error': str(e)
            })
        
        # 2. 复杂度分析
        try:
            complexity_chart = self.chart_factory.create_complexity_analysis_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '复杂度分析',
                'chart': complexity_chart,
                'size': 'large'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '复杂度分析',
                'error': str(e)
            })
        
        # 3. 工作量趋势
        try:
            workload_chart = self.chart_factory.create_workload_trend_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '工作量趋势',
                'chart': workload_chart,
                'size': 'large'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '工作量趋势',
                'error': str(e)
            })
        
        # 4. 详细数据表
        data_table = self._create_data_table(reports)
        dashboard['sections'].append({
            'type': 'data_table',
            'title': '详细数据',
            'data': data_table,
            'size': 'large'
        })
        
        return dashboard
    
    def _create_analytical_layout(self, reports: List[WeeklyReport],
                                analysis_results: List[AnalysisResult],
                                theme: str,
                                custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建分析仪表盘布局"""
        dashboard = {
            'title': custom_config.get('title', '深度分析仪表盘'),
            'layout': 'analytical',
            'theme': theme,
            'sections': []
        }
        
        # 1. 分析结果概览
        if analysis_results:
            analysis_overview = self._create_analysis_overview(analysis_results)
            dashboard['sections'].append({
                'type': 'analysis_overview',
                'title': '分析结果概览',
                'data': analysis_overview
            })
        
        # 2. 所有图表
        all_charts = self.chart_factory.create_dashboard_charts(reports, analysis_results)
        for chart_name, chart_data in all_charts.items():
            if 'error' not in chart_data:
                dashboard['sections'].append({
                    'type': 'chart',
                    'title': chart_name.replace('_', ' ').title(),
                    'chart': chart_data,
                    'size': 'medium'
                })
            else:
                dashboard['sections'].append({
                    'type': 'error',
                    'title': chart_name.replace('_', ' ').title(),
                    'error': chart_data['error']
                })
        
        return dashboard
    
    def _create_team_performance_layout(self, reports: List[WeeklyReport],
                                      analysis_results: List[AnalysisResult],
                                      theme: str,
                                      custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建团队绩效仪表盘布局"""
        dashboard = {
            'title': custom_config.get('title', '团队绩效仪表盘'),
            'layout': 'team_performance',
            'theme': theme,
            'sections': []
        }
        
        # 1. 团队概览
        team_overview = self._create_team_overview(reports)
        dashboard['sections'].append({
            'type': 'team_overview',
            'title': '团队概览',
            'data': team_overview
        })
        
        # 2. 员工绩效对比
        try:
            performance_chart = self.chart_factory.create_chart_from_template(
                'employee_performance', reports
            )
            dashboard['sections'].append({
                'type': 'chart',
                'title': '员工绩效对比',
                'chart': performance_chart,
                'size': 'large'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '员工绩效对比',
                'error': str(e)
            })
        
        # 3. 部门对比
        try:
            dept_chart = self.chart_factory.create_department_comparison_chart(reports)
            dashboard['sections'].append({
                'type': 'chart',
                'title': '部门对比',
                'chart': dept_chart,
                'size': 'medium'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '部门对比',
                'error': str(e)
            })
        
        return dashboard
    
    def _create_project_overview_layout(self, reports: List[WeeklyReport],
                                      analysis_results: List[AnalysisResult],
                                      theme: str,
                                      custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建项目概览仪表盘布局"""
        dashboard = {
            'title': custom_config.get('title', '项目概览仪表盘'),
            'layout': 'project_overview',
            'theme': theme,
            'sections': []
        }
        
        # 1. 项目进度概览
        project_overview = self._create_project_overview(reports)
        dashboard['sections'].append({
            'type': 'project_overview',
            'title': '项目进度概览',
            'data': project_overview
        })
        
        # 2. 周度总结
        try:
            weekly_chart = self.chart_factory.create_chart_from_template(
                'weekly_summary', reports
            )
            dashboard['sections'].append({
                'type': 'chart',
                'title': '周度总结',
                'chart': weekly_chart,
                'size': 'large'
            })
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '周度总结',
                'error': str(e)
            })
        
        # 3. 任务分布和复杂度
        try:
            task_chart = self.chart_factory.create_task_distribution_chart(reports)
            complexity_chart = self.chart_factory.create_complexity_analysis_chart(reports)
            
            dashboard['sections'].extend([
                {
                    'type': 'chart',
                    'title': '任务分布',
                    'chart': task_chart,
                    'size': 'medium'
                },
                {
                    'type': 'chart',
                    'title': '复杂度分析',
                    'chart': complexity_chart,
                    'size': 'medium'
                }
            ])
        except Exception as e:
            dashboard['sections'].append({
                'type': 'error',
                'title': '任务分析',
                'error': str(e)
            })
        
        return dashboard
    
    def _calculate_kpi_metrics(self, reports: List[WeeklyReport],
                             analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """计算关键指标"""
        total_hours = sum(sum(item.duration_hours for item in report.work_items) for report in reports)
        total_tasks = sum(len(report.work_items) for report in reports)
        total_employees = len(set(report.employee.email for report in reports))
        
        metrics = {
            'total_hours': total_hours,
            'total_tasks': total_tasks,
            'total_employees': total_employees,
            'avg_hours_per_employee': total_hours / total_employees if total_employees > 0 else 0,
            'avg_tasks_per_employee': total_tasks / total_employees if total_employees > 0 else 0
        }
        
        if analysis_results:
            avg_confidence = sum(result.confidence_score for result in analysis_results) / len(analysis_results)
            metrics['avg_analysis_confidence'] = avg_confidence
        
        return metrics
    
    def _create_data_table(self, reports: List[WeeklyReport]) -> List[Dict[str, Any]]:
        """创建数据表"""
        table_data = []
        for report in reports:
            total_hours = sum(item.duration_hours for item in report.work_items)
            table_data.append({
                'employee': report.employee.name,
                'department': report.employee.department,
                'week': report.week,
                'total_hours': total_hours,
                'task_count': len(report.work_items),
                'avg_complexity': sum(item.calculate_complexity_score() for item in report.work_items) / len(report.work_items) if report.work_items else 0
            })
        return table_data
    
    def _create_analysis_overview(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """创建分析概览"""
        analysis_types = {}
        for result in analysis_results:
            analysis_type = result.analysis_type
            if analysis_type not in analysis_types:
                analysis_types[analysis_type] = {
                    'count': 0,
                    'avg_confidence': 0,
                    'avg_processing_time': 0
                }
            
            analysis_types[analysis_type]['count'] += 1
            analysis_types[analysis_type]['avg_confidence'] += result.confidence_score
            analysis_types[analysis_type]['avg_processing_time'] += result.processing_time
        
        # 计算平均值
        for analysis_type, stats in analysis_types.items():
            count = stats['count']
            stats['avg_confidence'] /= count
            stats['avg_processing_time'] /= count
        
        return {
            'total_analyses': len(analysis_results),
            'analysis_types': analysis_types,
            'overall_avg_confidence': sum(result.confidence_score for result in analysis_results) / len(analysis_results)
        }
    
    def _create_team_overview(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建团队概览"""
        departments = {}
        for report in reports:
            dept = report.employee.department
            if dept not in departments:
                departments[dept] = {
                    'employees': set(),
                    'total_hours': 0,
                    'total_tasks': 0
                }
            
            departments[dept]['employees'].add(report.employee.email)
            departments[dept]['total_hours'] += sum(item.duration_hours for item in report.work_items)
            departments[dept]['total_tasks'] += len(report.work_items)
        
        # 转换为最终格式
        dept_summary = {}
        for dept, stats in departments.items():
            employee_count = len(stats['employees'])
            dept_summary[dept] = {
                'employee_count': employee_count,
                'avg_hours_per_employee': stats['total_hours'] / employee_count if employee_count > 0 else 0,
                'avg_tasks_per_employee': stats['total_tasks'] / employee_count if employee_count > 0 else 0
            }
        
        return {
            'total_departments': len(departments),
            'total_employees': len(set(report.employee.email for report in reports)),
            'department_summary': dept_summary
        }
    
    def _create_project_overview(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建项目概览"""
        weeks = set(report.week for report in reports)
        categories = {}
        
        for report in reports:
            for item in report.work_items:
                category = item.category.value
                if category not in categories:
                    categories[category] = {
                        'total_hours': 0,
                        'task_count': 0
                    }
                categories[category]['total_hours'] += item.duration_hours
                categories[category]['task_count'] += 1
        
        return {
            'weeks_covered': len(weeks),
            'week_range': f"{min(weeks)} - {max(weeks)}" if weeks else "无数据",
            'category_breakdown': categories,
            'total_categories': len(categories)
        }
    
    def _export_html_dashboard(self, dashboard: Dict[str, Any], filename: str) -> str:
        """导出HTML仪表盘"""
        # 这里应该生成完整的HTML页面
        # 为了简化，返回基本的HTML结构
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{dashboard.get('title', '仪表盘')}</title>
            <meta charset="utf-8">
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        </head>
        <body>
            <h1>{dashboard.get('title', '仪表盘')}</h1>
            <div id="dashboard-content">
                <!-- 仪表盘内容将在这里渲染 -->
                <pre>{json.dumps(dashboard, indent=2, ensure_ascii=False)}</pre>
            </div>
        </body>
        </html>
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filename
    
    def _export_json_dashboard(self, dashboard: Dict[str, Any], filename: str) -> str:
        """导出JSON仪表盘"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dashboard, f, indent=2, ensure_ascii=False, default=str)
        
        return filename
    
    def get_available_layouts(self) -> List[str]:
        """获取可用的布局类型"""
        return list(self.dashboard_layouts.keys())
    
    def get_available_themes(self) -> List[str]:
        """获取可用的主题"""
        return list(self.themes.keys())
