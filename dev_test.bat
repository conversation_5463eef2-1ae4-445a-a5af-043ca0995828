@echo off
echo ========================================
echo   AI Email Analysis System - Dev Test
echo ========================================
echo.

cd /d "%~dp0"
echo Current directory: %CD%

echo.
echo Checking Python...
python --version
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)
echo Python OK

echo.
echo Testing AI Analyzer...
python -c "import sys; sys.path.append('.'); from ai.simple_analyzer import SimpleAIAnalyzer; analyzer = SimpleAIAnalyzer(); result = analyzer.analyze('Test content', department='Tech', role='Engineer'); print('AI Analyzer OK - Report ID:', result.get('report_id', 'N/A'))"

echo.
echo System Check...
python system_check.py

echo.
echo Choose action:
echo 1. Start UI
echo 2. Run AI Test
echo 3. Exit
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo Starting UI...
    cd ui
    python -m streamlit run enhanced_app.py --server.port 8501
) else if "%choice%"=="2" (
    echo Running AI Test...
    python ai_test.py
    pause
) else (
    echo Exit
)

pause
