#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域值对象定义模块

模块描述: 定义系统中的值对象，包括处理上下文、筛选条件、指标阈值等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: dataclasses, typing, datetime
"""

from dataclasses import dataclass
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

@dataclass(frozen=True)
class ProcessingContext:
    """
    处理上下文值对象 - 不可变对象
    
    包含处理过程中需要的上下文信息
    """
    user_id: str
    department: str
    role: str
    preferences: Dict[str, Any]
    timestamp: datetime
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    locale: str = "zh-CN"
    timezone: str = "Asia/Shanghai"
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.user_id:
            raise ValueError("用户ID不能为空")
        if not self.department:
            raise ValueError("部门不能为空")
        if not self.role:
            raise ValueError("角色不能为空")
    
    def with_updated_timestamp(self) -> 'ProcessingContext':
        """
        创建更新时间戳的新实例
        
        Returns:
            ProcessingContext: 新的上下文实例
        """
        return ProcessingContext(
            user_id=self.user_id,
            department=self.department,
            role=self.role,
            preferences=self.preferences.copy(),
            timestamp=datetime.now(),
            session_id=self.session_id,
            request_id=self.request_id,
            locale=self.locale,
            timezone=self.timezone
        )
    
    def with_session(self, session_id: str) -> 'ProcessingContext':
        """
        创建带会话ID的新实例
        
        Args:
            session_id: 会话ID
            
        Returns:
            ProcessingContext: 新的上下文实例
        """
        return ProcessingContext(
            user_id=self.user_id,
            department=self.department,
            role=self.role,
            preferences=self.preferences.copy(),
            timestamp=self.timestamp,
            session_id=session_id,
            request_id=self.request_id,
            locale=self.locale,
            timezone=self.timezone
        )
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """
        获取用户偏好设置
        
        Args:
            key: 偏好键
            default: 默认值
            
        Returns:
            Any: 偏好值
        """
        return self.preferences.get(key, default)

@dataclass(frozen=True)
class VisualizationConfig:
    """
    可视化配置值对象
    
    定义图表和可视化的配置参数
    """
    chart_type: str
    title: str
    width: int = 800
    height: int = 600
    color_scheme: str = "default"
    interactive: bool = True
    export_formats: Tuple[str, ...] = ("png", "html", "pdf")
    theme: str = "light"
    show_legend: bool = True
    show_grid: bool = True
    animation: bool = True
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.chart_type:
            raise ValueError("图表类型不能为空")
        if not self.title:
            raise ValueError("图表标题不能为空")
        if self.width <= 0 or self.height <= 0:
            raise ValueError("图表尺寸必须大于0")
    
    def with_size(self, width: int, height: int) -> 'VisualizationConfig':
        """
        创建新尺寸的配置
        
        Args:
            width: 宽度
            height: 高度
            
        Returns:
            VisualizationConfig: 新的配置实例
        """
        return VisualizationConfig(
            chart_type=self.chart_type,
            title=self.title,
            width=width,
            height=height,
            color_scheme=self.color_scheme,
            interactive=self.interactive,
            export_formats=self.export_formats,
            theme=self.theme,
            show_legend=self.show_legend,
            show_grid=self.show_grid,
            animation=self.animation
        )
    
    def with_theme(self, theme: str) -> 'VisualizationConfig':
        """
        创建新主题的配置
        
        Args:
            theme: 主题名称
            
        Returns:
            VisualizationConfig: 新的配置实例
        """
        return VisualizationConfig(
            chart_type=self.chart_type,
            title=self.title,
            width=self.width,
            height=self.height,
            color_scheme=self.color_scheme,
            interactive=self.interactive,
            export_formats=self.export_formats,
            theme=theme,
            show_legend=self.show_legend,
            show_grid=self.show_grid,
            animation=self.animation
        )

@dataclass(frozen=True)
class FilterCriteria:
    """
    筛选条件值对象
    
    定义数据筛选的条件参数
    """
    department: Optional[str] = None
    role: Optional[str] = None
    week: Optional[str] = None
    tags: Tuple[str, ...] = ()
    date_range: Optional[Tuple[datetime, datetime]] = None
    employee_emails: Tuple[str, ...] = ()
    complexity_levels: Tuple[str, ...] = ()
    categories: Tuple[str, ...] = ()
    min_hours: Optional[float] = None
    max_hours: Optional[float] = None
    
    def is_empty(self) -> bool:
        """
        判断是否为空筛选条件
        
        Returns:
            bool: 是否为空
        """
        return all(value is None or (isinstance(value, tuple) and not value) 
                  for value in [
                      self.department, self.role, self.week, self.tags, 
                      self.date_range, self.employee_emails, self.complexity_levels,
                      self.categories, self.min_hours, self.max_hours
                  ])
    
    def has_time_filter(self) -> bool:
        """
        判断是否有时间筛选条件
        
        Returns:
            bool: 是否有时间筛选
        """
        return self.week is not None or self.date_range is not None
    
    def has_employee_filter(self) -> bool:
        """
        判断是否有员工筛选条件
        
        Returns:
            bool: 是否有员工筛选
        """
        return (self.department is not None or 
                self.role is not None or 
                len(self.employee_emails) > 0)
    
    def with_department(self, department: str) -> 'FilterCriteria':
        """
        创建带部门筛选的新实例
        
        Args:
            department: 部门名称
            
        Returns:
            FilterCriteria: 新的筛选条件实例
        """
        return FilterCriteria(
            department=department,
            role=self.role,
            week=self.week,
            tags=self.tags,
            date_range=self.date_range,
            employee_emails=self.employee_emails,
            complexity_levels=self.complexity_levels,
            categories=self.categories,
            min_hours=self.min_hours,
            max_hours=self.max_hours
        )

@dataclass(frozen=True)
class MetricThresholds:
    """
    指标阈值值对象
    
    定义各种指标的阈值范围
    """
    workload_low: float = 0.5
    workload_high: float = 1.2
    quality_low: float = 0.6
    quality_high: float = 0.9
    efficiency_low: float = 0.7
    efficiency_high: float = 1.0
    complexity_low: float = 1.5
    complexity_high: float = 3.0
    hours_per_week_min: float = 20.0
    hours_per_week_max: float = 60.0
    
    def __post_init__(self):
        """初始化后验证"""
        if self.workload_low >= self.workload_high:
            raise ValueError("工作负载低阈值必须小于高阈值")
        if self.quality_low >= self.quality_high:
            raise ValueError("质量低阈值必须小于高阈值")
        if self.efficiency_low >= self.efficiency_high:
            raise ValueError("效率低阈值必须小于高阈值")
        if self.hours_per_week_min >= self.hours_per_week_max:
            raise ValueError("周工时最小值必须小于最大值")
    
    def classify_workload(self, value: float) -> str:
        """
        分类工作负载
        
        Args:
            value: 工作负载值
            
        Returns:
            str: 分类结果
        """
        if value < self.workload_low:
            return "不足"
        elif value > self.workload_high:
            return "过载"
        else:
            return "适中"
    
    def classify_quality(self, value: float) -> str:
        """
        分类质量水平
        
        Args:
            value: 质量值
            
        Returns:
            str: 分类结果
        """
        if value < self.quality_low:
            return "待改进"
        elif value > self.quality_high:
            return "优秀"
        else:
            return "良好"
    
    def classify_efficiency(self, value: float) -> str:
        """
        分类效率水平
        
        Args:
            value: 效率值
            
        Returns:
            str: 分类结果
        """
        if value < self.efficiency_low:
            return "低效"
        elif value > self.efficiency_high:
            return "高效"
        else:
            return "正常"
    
    def is_workload_normal(self, value: float) -> bool:
        """
        判断工作负载是否正常
        
        Args:
            value: 工作负载值
            
        Returns:
            bool: 是否正常
        """
        return self.workload_low <= value <= self.workload_high

@dataclass(frozen=True)
class AnalysisParameters:
    """
    分析参数值对象
    
    定义分析过程中使用的参数
    """
    confidence_threshold: float = 0.8
    min_sample_size: int = 5
    max_iterations: int = 100
    convergence_tolerance: float = 0.001
    use_cache: bool = True
    cache_ttl: int = 3600
    parallel_processing: bool = True
    max_workers: int = 4
    
    def __post_init__(self):
        """初始化后验证"""
        if not (0.0 <= self.confidence_threshold <= 1.0):
            raise ValueError("置信度阈值必须在0.0到1.0之间")
        if self.min_sample_size <= 0:
            raise ValueError("最小样本数必须大于0")
        if self.max_iterations <= 0:
            raise ValueError("最大迭代次数必须大于0")
        if self.convergence_tolerance <= 0:
            raise ValueError("收敛容差必须大于0")
        if self.max_workers <= 0:
            raise ValueError("最大工作线程数必须大于0")
    
    def is_high_confidence(self, confidence: float) -> bool:
        """
        判断是否高置信度
        
        Args:
            confidence: 置信度值
            
        Returns:
            bool: 是否高置信度
        """
        return confidence >= self.confidence_threshold
    
    def with_confidence_threshold(self, threshold: float) -> 'AnalysisParameters':
        """
        创建新置信度阈值的参数
        
        Args:
            threshold: 新的置信度阈值
            
        Returns:
            AnalysisParameters: 新的参数实例
        """
        return AnalysisParameters(
            confidence_threshold=threshold,
            min_sample_size=self.min_sample_size,
            max_iterations=self.max_iterations,
            convergence_tolerance=self.convergence_tolerance,
            use_cache=self.use_cache,
            cache_ttl=self.cache_ttl,
            parallel_processing=self.parallel_processing,
            max_workers=self.max_workers
        )

@dataclass(frozen=True)
class TimeRange:
    """
    时间范围值对象
    
    定义时间范围的起止时间
    """
    start_time: datetime
    end_time: datetime
    timezone: str = "Asia/Shanghai"
    
    def __post_init__(self):
        """初始化后验证"""
        if self.start_time >= self.end_time:
            raise ValueError("开始时间必须早于结束时间")
    
    def duration_days(self) -> float:
        """
        计算时间范围的天数
        
        Returns:
            float: 天数
        """
        delta = self.end_time - self.start_time
        return delta.total_seconds() / (24 * 3600)
    
    def duration_hours(self) -> float:
        """
        计算时间范围的小时数
        
        Returns:
            float: 小时数
        """
        delta = self.end_time - self.start_time
        return delta.total_seconds() / 3600
    
    def contains(self, timestamp: datetime) -> bool:
        """
        判断时间戳是否在范围内
        
        Args:
            timestamp: 时间戳
            
        Returns:
            bool: 是否在范围内
        """
        return self.start_time <= timestamp <= self.end_time
    
    def overlaps(self, other: 'TimeRange') -> bool:
        """
        判断是否与另一个时间范围重叠
        
        Args:
            other: 另一个时间范围
            
        Returns:
            bool: 是否重叠
        """
        return (self.start_time <= other.end_time and 
                self.end_time >= other.start_time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'timezone': self.timezone,
            'duration_days': self.duration_days(),
            'duration_hours': self.duration_hours()
        }
